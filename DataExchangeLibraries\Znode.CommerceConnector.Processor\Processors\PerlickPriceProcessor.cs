using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Runtime.Remoting;
using System.Text;
using System;
using System.IO;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.ECommerce.Utilities;
using System.Collections.Generic;
using Znode.CommerceConnector.Model.Models;
using Znode.CommerceConnector.InputHandler.HandlerHelper;
using Znode.CommerceConnector.OutputHandler.IHandlers;
using static System.Runtime.InteropServices.JavaScript.JSType;
using System.Reflection.PortableExecutable;
using Znode.CommerceConnector.OutputHandler.Handlers;
using Newtonsoft.Json;
using System.Text.Json;

namespace Znode.CommerceConnector.Processor
{
    public class PerlickPriceProcessor : IProcessor
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public PerlickPriceProcessor(IOutputHandlerInitializer handler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = handler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

        public dynamic ProcessData(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            try
            {
                dynamic data = inputModel;
                MongoLogging.LogMessage("FSCRealTimeOrderHeaderCalled", ProcessorConstants.ProcessData, TraceLevel.Info);

                MongoLogging.LogMessage(ProcessorConstants.ParserFSCRealTimeOrderProcessorProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                //Process the data
                dynamic yamlModel = _inputHandler.GetYAMLDataHandler(erpId);
                data = _iParser.ParseYAMLData(data, erpId, yamlModel);

                MongoLogging.LogMessage("OutputHandler from FSCRealTimeOrderHeaderProcessor Called ", ProcessorConstants.ProcessData, TraceLevel.Info);

                WebHeaderCollection headers = new WebHeaderCollection();
                //Upload the data
                return JsonDocument.Parse( _outputHandler.OutputHandler(data, erpId, OutputHandlerRequestBody(data), headers, processorDetails, inputModel)).RootElement;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerFSCRealTimeOrderProcessorProcessorFailed + ex.Message, ProcessorConstants.ProcessData, TraceLevel.Info);
                return false;
            }
        }

        private dynamic OutputHandlerRequestBody(dynamic data)
        {
            return new NullableRequestBody();
        }

        private dynamic InputHandlerRequestBody()
        {
            return new NullableRequestBody();
        }
    }
}
