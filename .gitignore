﻿################################################################################
# This .gitignore file was automatically created by Microsoft(R) Visual Studio.
################################################################################

/.vs
*.suo
*.user
_ReSharper.*
bin
obj
packages
/Commerce Connector/obj/Debug/net8.0/Znode.Engine.CommerceConnector.AssemblyInfo.cs
/Commerce Connector/obj/Debug/net8.0/Znode.Engine.CommerceConnector.AssemblyInfoInputs.cache
/Commerce Connector/obj/Debug/net8.0/Znode.Engine.CommerceConnector.csproj.AssemblyReference.cache
/Libraries/Znode.CommerceConnector.API/obj/Debug/net8.0/Znode.CommerceConnector.API.AssemblyInfo.cs
/Libraries/Znode.CommerceConnector.API/obj/Debug/net8.0/Znode.CommerceConnector.API.AssemblyInfoInputs.cache
/Libraries/Znode.CommerceConnector.API/obj/Debug/net8.0/Znode.CommerceConnector.API.csproj.AssemblyReference.cache
/Libraries/Znode.CommerceConnector.Client/obj/Debug/net8.0/Znode.CommerceConnector.Client.AssemblyInfo.cs
/Libraries/Znode.CommerceConnector.Client/obj/Debug/net8.0/Znode.CommerceConnector.Client.AssemblyInfoInputs.cache
/Libraries/Znode.CommerceConnector.Client/obj/Debug/net8.0/Znode.CommerceConnector.Client.csproj.AssemblyReference.cache
/Libraries/Znode.CommerceConnector.Core/obj/Debug/net8.0/Znode.CommerceConnector.Core.AssemblyInfo.cs
/Libraries/Znode.CommerceConnector.Core/obj/Debug/net8.0/Znode.CommerceConnector.Core.AssemblyInfoInputs.cache
/Libraries/Znode.CommerceConnector.Core/obj/Debug/net8.0/Znode.CommerceConnector.Core.csproj.AssemblyReference.cache
/Libraries/Znode.CommerceConnector.Model/obj/Debug/net8.0/Znode.CommerceConnector.Model.AssemblyInfo.cs
/Libraries/Znode.CommerceConnector.Model/obj/Debug/net8.0/Znode.CommerceConnector.Model.AssemblyInfoInputs.cache
/Libraries/Znode.Libraries.Data/obj/Debug/net8.0/Znode.Libraries.Data.AssemblyInfo.cs
/Libraries/Znode.Libraries.Data/obj/Debug/net8.0/Znode.Libraries.Data.AssemblyInfoInputs.cache
/Libraries/Znode.Libraries.ECommerce.Utilities/obj/Debug/net8.0/Znode.Libraries.ECommerce.Utilities.AssemblyInfo.cs
/Libraries/Znode.Libraries.ECommerce.Utilities/obj/Debug/net8.0/Znode.Libraries.ECommerce.Utilities.AssemblyInfoInputs.cache
/Znode.Engine.Api/obj/Debug/net8.0/Znode.Engine.Api.AssemblyInfo.cs
/Znode.Engine.Api/obj/Debug/net8.0/Znode.Engine.Api.AssemblyInfoInputs.cache
/Znode.Engine.Api/obj/Debug/net8.0/Znode.Engine.Api.csproj.AssemblyReference.cache
/.vs/7. Commerce connector/v17/.wsuo
/.vs/7. Commerce connector/v17/DocumentLayout.json
/Commerce Connector/wwwroot/Scripts/Bundles/DataExchange.min.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/AddSelectedExchange.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/AddSelectedExchange.js.map
/Commerce Connector/wwwroot/Scripts/CommerceConnector/DataExchange.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/DataExchange.js.map
/Commerce Connector/wwwroot/Scripts/CommerceConnector/DynamicGrid.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/DynamicGrid.js.map
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ManageDataExchange.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ManageDataExchange.js.map
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ZnodeGlobal.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ZnodeGlobal.js.map
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ConfigurationLog.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ConfigurationLog.js.map
/Commerce Connector/obj/project.assets.json
/Commerce Connector/obj/Debug/net8.0/Znode.Engine.CommerceConnector.assets.cache
/Commerce Connector/obj/Debug/net8.0/Znode.Engine.CommerceConnector.GeneratedMSBuildEditorConfig.editorconfig
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ProcessingLog.js.map
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ZnodeImportLogs.js.map
/Libraries/Znode.CommerceConnector.API/obj/Debug/net8.0/Znode.CommerceConnector.API.assets.cache
/Libraries/Znode.CommerceConnector.API/obj/Debug/net8.0/Znode.CommerceConnector.API.GeneratedMSBuildEditorConfig.editorconfig
/Libraries/Znode.Libraries.Data/obj/Debug/net8.0/Znode.Libraries.Data.assets.cache
/Libraries/Znode.Libraries.Data/obj/Debug/net8.0/Znode.Libraries.Data.csproj.AssemblyReference.cache
/Libraries/Znode.Libraries.Data/obj/Debug/net8.0/Znode.Libraries.Data.GeneratedMSBuildEditorConfig.editorconfig
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ProcessingLog.js
/Commerce Connector/wwwroot/Scripts/CommerceConnector/ZnodeImportLogs.js
