﻿class ZnodeGlobal {
    DisplayPopup(url, parameter, targetDiv, contentDiv) {
        const $target = $("#" + targetDiv);
        $target.html('');
        $target.hide(); // hide before load

        $.ajax({
            url: url,
            type: 'GET',
            contentType: 'application/json',
            data: parameter,
            success: (res) => {
                $("#" + contentDiv).show();
                $("body").css("overflow", "hidden");
                $target.append(res);
                $target.fadeIn("fast");
                $(document).trigger("PARTIAL_LOADED", [url]);
            },
            error: function (err) {
                $("#" + contentDiv).show();
                $("body").css("overflow", "hidden");
                $target.append('Failed to retrieve data. Please try again.');
                $target.fadeIn("fast");
                $(document).trigger("PARTIAL_LOADED", [url]);
                console.error('Error loading popup:', err);
            }
        });
    }

    ShowNotificationBar(element: any, message: string, type: string,) {
        if (element.length) {
            if (message !== "" && message != null) {
                element.html("<div class='message-box alert text-center p-2 mt-1 mb-0' data-test-selector='divMessageBoxContainer'>" + message + "</div>");
                switch (type) {
                    case "success":
                        {
                            element.find('div').addClass('alert-success');
                            break;
                        }
                    case "error":
                        {
                            element.find('div').addClass('alert-danger');
                            break;
                        }
                    default:
                        {
                            element.find('div').addClass('alert-info');
                        }
                }
                setTimeout(function () {
                    element.fadeOut().empty();
                }, 5000);
            }
        }
    }

    FormatDateTime24Hour(value) {
        if (!value) return '';

        const date = new Date(value);
        if (isNaN(date.getTime())) return '';

        const month = ZnodeGlobal.prototype.pad(date.getMonth() + 1);
        const day = ZnodeGlobal.prototype.pad(date.getDate());
        const year = date.getFullYear();
        const hours = ZnodeGlobal.prototype.pad(date.getHours());
        const minutes = ZnodeGlobal.prototype.pad(date.getMinutes());

        return `${month}/${day}/${year} ${hours}:${minutes}:00`;
    }

    ConvertDateTimeToLocal(value) {
        if (!value) return '';

        var utcDate = new Date(value);

        if (isNaN(utcDate.getTime())) return 'Invalid date';

        var local = new Date(utcDate + 'Z'); 

        var mm = ZnodeGlobal.prototype.pad(local.getMonth() + 1); 
        var dd = ZnodeGlobal.prototype.pad(local.getDate());
        var yyyy = local.getFullYear();

        var hh = ZnodeGlobal.prototype.pad(local.getHours());
        var min = ZnodeGlobal.prototype.pad(local.getMinutes());

        return `${mm}/${dd}/${yyyy} ${hh}:${min}:00`;
    }

    pad(n) {
        return (n < 10 ? '0' : '') + n;
    }

    ShowLoader() {
        $("#loading-div-background").show();
    }

    HideLoader() {
        $("#loading-div-background").hide();
    }
}