﻿using Microsoft.AspNetCore.Mvc;

using System.Diagnostics;
using System.Web.Http.Description;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.API.Services;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API
{
    public class TaskSchedularController : BaseController
    {
        private readonly ITaskSchedularService _taskSchedularService;

        public TaskSchedularController(ITaskSchedularService taskSchedularService)
        {
            _taskSchedularService = taskSchedularService;
        }

        [Route("TriggerTaskSchedular")]
        [HttpGet]
        public IActionResult TriggerTaskSchedular(int erpId)
        {
            IActionResult response;
            try
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.TriggerTaskSchedulerControllerCalled, CommerceConnectorConstants.APIController, TraceLevel.Info);
                dynamic data = _taskSchedularService.CallTaskSchedular(erpId,null);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.TriggerTaskSchedulerControllerFailed, CommerceConnectorConstants.APIController, TraceLevel.Error);
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        [Route("EnableDisableTaskScheduler")]
        [HttpPost]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult EnableDisableTaskScheduler([FromQuery] int erpId, [FromQuery] bool isActive)
        {
            IActionResult response;
            try
            {
                bool result = _taskSchedularService.EnableDisableTaskScheduler(erpId, isActive);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });

            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }

        }
    }
}
