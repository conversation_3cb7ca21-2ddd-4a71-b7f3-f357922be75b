{"name": "@types/codemirror", "version": "5.60.16", "description": "TypeScript definitions for codemirror", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/codemirror", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mihailik"}, {"name": "nrbernard", "githubUsername": "nrbernard", "url": "https://github.com/nrbernard"}, {"name": "Pr1st0n", "githubUsername": "Pr1st0n", "url": "https://github.com/Pr1st0n"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "r<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rileymiller"}, {"name": "todd<PERSON>", "githubUsername": "todd<PERSON>", "url": "https://github.com/toddself"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/ysulyma"}, {"name": "a<PERSON><PERSON>", "githubUsername": "a<PERSON><PERSON>", "url": "https://github.com/azoson"}, {"name": "kylesferrazza", "githubUsername": "kylesferrazza", "url": "https://github.com/kylesferrazza"}, {"name": "fityocsaba96", "githubUsername": "fityocsaba96", "url": "https://github.com/fityocsaba96"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/koddsson"}, {"name": "fi<PERSON><PERSON>", "githubUsername": "fi<PERSON><PERSON>", "url": "https://github.com/ficristo"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "xem<PERSON>", "url": "https://github.com/xemlock"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/codemirror"}, "scripts": {}, "dependencies": {"@types/tern": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "a140700366487a6f08ca1970d67f8ae54f17eed2ac0ace2c27f7f147e35590a1", "typeScriptVersion": "5.1"}