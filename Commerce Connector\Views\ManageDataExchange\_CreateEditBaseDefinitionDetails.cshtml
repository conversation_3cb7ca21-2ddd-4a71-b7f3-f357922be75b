﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var isFromAddNew = ViewBag.isFromAddNew ?? false;
    string fileNameClass = Model.IsCustomProcessingRequired ? "" : "d-none";
}
<div class="base-definition my-3">
    <div class="row">
        <div class="col-12 col-md-5 col-lg-6">
            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divExchangeName">@CommerceConnector_Resources.ExchangeName <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span></div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.Name, new { maxlength = 255, @class = "disable-on-condition", @data_test_selector = "txtName", @id = "exchangeName", aria_label = "Exchange Name" })
                    <span id="exchangeNameError" data-test-selector="spnExchangeNameError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divDataSource">@CommerceConnector_Resources.DataSource <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span></div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.DataSource, new { maxlength = 255, @class = "disable-on-condition", @data_test_selector = "txtDataSource", @id = "dataSource", aria_label = "Data Source" })
                    <span id="dataSourceError" data-test-selector="spnDataSourceError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divDataDestination">@CommerceConnector_Resources.DataDestination <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"> </span></div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.DataDestination, new { maxlength = 255, @class = "disable-on-condition", @data_test_selector = "txtDataDestination", @id = "dataDestination", aria_label = "Data Destination" })
                    <span id="dataDestinationError" data-test-selector="spnDestinationError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divVersion">@CommerceConnector_Resources.Version <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span></div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.Version, new { maxlength = 255, @data_test_selector = "txtVersion", @id = "version", aria_label = "Version" })
                    <span id="versionError" data-test-selector="spnVersionError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divAccess">
                    @CommerceConnector_Resources.Access <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span>
                </div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.DropDownListFor(model => model.Access, Model?.accessDropdownModel?.accessMode, new { @class = "form-select", id = "Access", aria_label = "Access", @onchange = "DataExchange.prototype.Validate();" })
                    <span id="accessModeOptionsDropdownError" data-test-selector="spnaccessModeOptionsDropdownError" class="field-validation-error"> </span>
                </div>
            </div>
            @if (isFromAddNew)
            {
                <div class="row">
                    <div class="col-12 col-md-6 col-lg-4 heading mb-3 pe-4" data-test-selector="divIsCustomProcessingRequired">@CommerceConnector_Resources.LabelIsCustomProcessingRequired </div>
                    <div class="col-12 col-md-6  mb-3 ps-0">
                        @Html.CheckBoxFor(model => model.IsCustomProcessingRequired, new { @class = "disable-on-condition", @data_test_selector = "isCustomProcessingRequired", @id = "isCustomProcessingRequired", aria_label = "Custom Processing Required Checkbox" })
                        <span id="IsCustomProcessingRequiredError" data-test-selector="spnIsCustomProcessingRequired" class="field-validation-error"> </span>
                    </div>
                </div>
            }
            else
            {
                <div class="row">
                    <div class="col-12 col-md-6 col-lg-4 heading mb-3 pe-4" data-test-selector="divIsCustomProcessingRequired">@CommerceConnector_Resources.LabelIsCustomProcessingRequired </div>
                    <div class="col-12 col-md-6  mb-3 ps-0">
                        @(Model.IsCustomProcessingRequired ? CommerceConnector_Resources.LabelCustomProcessingRequired : CommerceConnector_Resources.LabelCustomProcessingNotRequired)
                    </div>
                </div>
            }

            <div class="row @fileNameClass">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divProcessorFileName">@CommerceConnector_Resources.ProcessorFileName <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span></div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.ProcessorFileName, new { maxlength = 255, @class = "disable-on-condition", @data_test_selector = "txtProcessorFileName", @id = "processorFileName", aria_label = "File Name" })
                    <span id="processorFileNameError" data-test-selector="spnProcessorFileNameError" class="field-validation-error"> </span>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-5 col-lg-6">
            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divTriggerOrigin">@CommerceConnector_Resources.TriggerOrigin <span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span></div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.TriggerOrigin, new { maxlength = 255, @class = "disable-on-condition", @data_test_selector = "txttriggerOrigin", @id = "triggerOrigin", aria_label = "Trigger Origin" })
                    <span id="triggerOriginError" data-test-selector="spnTriggerOriginError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divCollection">@CommerceConnector_Resources.Collection  </div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.Collection, new { maxlength = 255, @data_test_selector = "txtCollection", @id = "collection", aria_label = "Collection" })
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divTags">@CommerceConnector_Resources.Tags </div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextBoxFor(model => model.Tags, new { @data_test_selector = "divTags", @id = "tags", aria_label = "Tags" })
                    <span id="tagsError" data-test-selector="spnTagsError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" data-test-selector="divDescription">@CommerceConnector_Resources.Description</div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                    @Html.TextAreaFor(model => model.Description, new { @class = "form-control", @rows = "2", @data_test_selector = "txtDescription", @id = "description", @onclick = "DataExchange.prototype.Validate();", aria_label = "Description" })
                    <span id="descriptionError" data-test-selector="spnDescriptionError" class="field-validation-error"> </span>
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3" ">@CommerceConnector_Resources.LabelEnableStatus </div>
                <div class="col-12 col-md-6  mb-3 ps-0">
                @Html.CheckBoxFor(model => model.StatusActivationViewModel.Status, new { style = "opacity: revert !important", @data_test_selector = "isEnable", @id = "isEnable", aria_label = "Scheduler Status" })
                </div>
            </div>

            <div class="row">
                <div class="col-12 col-md-6 col-lg-4 heading mb-3 d-none" data-test-selector="divMethod">@CommerceConnector_Resources.MethodName</div>
                <div class="col-12 col-md-6  mb-3 ps-0 d-none">
                @Html.TextBoxFor(model => model.MethodName, new { maxlength = 255, @data_test_selector = "txtMethod", @id = "method", @onclick = "DataExchange.prototype.Validate();" })
                    <span id="methodError" data-test-selector="spnMethodNameError" class="field-validation-error"> </span>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="addCustomBaseDefinitionForm"></div>
<input type="hidden" id="erpExchangeId" value="@Model?.DataExchangeLibraryId" />
<input type="hidden" id="isFromAddNew" value="@( (ViewBag.isFromAddNew ?? false).ToString().ToLower() )" />
                @Html.HiddenFor(x => x.IsCustomProcessingRequired)
