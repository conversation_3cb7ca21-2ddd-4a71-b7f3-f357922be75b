﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeScheduleConfigurationTable
{
    public int SchedulerId { get; set; }

    public int? Erpid { get; set; }

    public string? SchedulerType { get; set; }

    public string? SchedulerName { get; set; }

    public string? SchedulerFrequency { get; set; }

    public DateTime? StartDate { get; set; }

    public string? CronExpression { get; set; }

    public string? ProcessorFileName { get; set; }

    public DateTime? LastRunTime { get; set; }

    public string? HangfireJobId { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? CreatedDate { get; set; }

    public int? ModifiedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public virtual ZnodeBaseDefinitionTable? Erp { get; set; }
}
