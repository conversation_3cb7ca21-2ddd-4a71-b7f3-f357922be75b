﻿using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Runtime.Remoting;
using System.Text;
using System;
using System.IO;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;
using System.Collections.Generic;

namespace Znode.CommerceConnector.Processor
{
    public class {{ClassName}} : IProcessor
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public {{ClassName}}(IOutputHandlerInitializer handler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = handler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

         public dynamic ProcessData(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            try
            {
                dynamic data = "";
                MongoLogging.LogMessage(string.Format(ProcessorConstants.InputHandlerProcessorCalled, processorDetails.ProcessorFileName), ProcessorConstants.ProcessData, TraceLevel.Info);
                
                WebHeaderCollection headers = new WebHeaderCollection();

                //Download the data
                dynamic inputHandlerData = _inputHandler.InputHandler(erpId, processorDetails.LogId, InputHandlerRequestBody(), headers, inputModel);
                Type outputType = ((object)inputHandlerData).GetType();

                if (outputType.Name == ProcessorConstants.Boolean && inputHandlerData == false)
                {
                    MongoLogging.LogMessage(ProcessorConstants.InvalidTempTableNameNotConstructedProperly, ProcessorConstants.ProcessData, TraceLevel.Error);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, string.Empty, false, ProcessorConstants.InvalidTempTableNameNotConstructedProperly);
                    if (!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, ProcessorConstants.ProcessData, TraceLevel.Error);
                    return false;
                }
                else
                {
                    MongoLogging.LogMessage(string.Format(ProcessorConstants.ParserProcessorCalled, processorDetails.ProcessorFileName), ProcessorConstants.ProcessData, TraceLevel.Info);
                    //Process the data
                    dynamic yamlModel = _inputHandler.GetYAMLDataHandler(erpId);
                    data = _iParser.ParseYAMLData(inputHandlerData, erpId, yamlModel);

                    if (HelperUtility.IsNotNull(data) && data != "")
                    {
                        MongoLogging.LogMessage(string.Format(ProcessorConstants.OutputHandlerProcessorCalled, processorDetails.ProcessorFileName), ProcessorConstants.ProcessData, TraceLevel.Info);
                        //Upload the data

                        ImportModel importModel = new ImportModel();

                        HTTPConfigurationModel httpConfigurationModel = _outputHandler.GetOutputHandlerAPICredentials(erpId);
                        headers = new WebHeaderCollection();
                        return _outputHandler.OutputHandler(data, erpId, OutputHandlerRequestBody(data), headers, processorDetails, inputModel);
                    }
                    MongoLogging.LogMessage(string.Format(ProcessorConstants.OutputHandlerProcessorCalled, processorDetails.ProcessorFileName), ProcessorConstants.ProcessData, TraceLevel.Info);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, string.Empty, false, ProcessorConstants.DataNotAvailable);
                    if (!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, ProcessorConstants.ProcessData, TraceLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(string.Format(ProcessorConstants.InputHandlerProcessorFailed, processorDetails.ProcessorFileName) + ex.Message, ProcessorConstants.ProcessData, TraceLevel.Info);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, string.Empty, false, ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, ProcessorConstants.ProcessData, TraceLevel.Error);
                return false;
            }
        }

        private dynamic OutputHandlerRequestBody(dynamic data)
        {
            byte[] byteArray = Encoding.UTF8.GetBytes(data);

            using (var outputStream = new MemoryStream())
            {
                using (var brotliStream = new BrotliStream(outputStream, CompressionLevel.Optimal))
                {
                    brotliStream.Write(byteArray, 0, byteArray.Length);
                    brotliStream.Flush();
                }
                byteArray = outputStream.ToArray();
            }

            ImportModel importModel = new ImportModel
            {
                ImportType = "Inventory",
                ImportData = byteArray,
                LocaleCode = "1",
                TouchPointName = "InventoryTouchpoint",
                Properties = new Dictionary<string, string>()
            };
            return importModel;
        }

        private dynamic InputHandlerRequestBody()
        {
            return new NullableRequestBody();
        }
    }
}
