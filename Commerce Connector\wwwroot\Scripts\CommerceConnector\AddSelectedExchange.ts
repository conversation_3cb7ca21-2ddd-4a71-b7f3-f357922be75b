﻿declare var agGrid: any;
declare const bootstrap: any;

class AddSelectedExchange {
    private gridInitialized = false;
    private gridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0 // disables AG Grid's internal sorting
        },
        rowData: [],
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,

        onGridReady: (params: any) => {
            this.gridOptions.api = params.api;
            this.gridOptions.columnApi = params.columnApi;
            $(window).on("resize", () => {
                if (this.gridOptions.api) {
                    this.gridOptions.api.sizeColumnsToFit();
                }
            });
            setTimeout(() => {
                params.api.sizeColumnsToFit();
            }, 0);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const lastSorted = sortState.find((c: any) => c.sort);
                if (!lastSorted || !lastSorted.sort) {
                    const previous = DynamicGrid.prototype.currentSortField;
                    if (previous) {
                        params.columnApi.applyColumnState({
                            state: [{ colId: previous, sort: 'asc' }],
                            defaultState: { sort: null }
                        });
                        return;
                    }
                }
                DynamicGrid.prototype.setSorting(lastSorted?.colId || null, lastSorted?.sort || null);
                this.LoadAvailableLibrariesGrid(1);
            });
        }
    };

    public AddSelectedIdsToTheTable() {
        var checkedIds = []; 
        var checkedExchangeNames = [];

        $('input[type="checkbox"]:checked').each(function () {
            checkedIds.push($(this).val());
            checkedExchangeNames.push($(this).data('exchangeName'));
        });  
        if (checkedIds.length > 0) {
            var checkedExchangeName = checkedExchangeNames[0];
            $.ajax({
                type: 'POST',
                url: '/commerce-connector/GetExchangeLibrary/ValidateExchangeName',
                data: JSON.stringify(checkedExchangeName),
                contentType: 'application/json',
                success: function (response) {
                    if (response.status) {
                        AddSelectedExchange.prototype.AddSelectedDataExchange(checkedIds);
                    }
                    else {
                        $('#selectedExchangeName').val(checkedExchangeName);
                        $('#dataExchangeName').val(checkedExchangeName);
                        $('#dataExchangeNameError').removeClass('error');
                        $('#dataExchangeNameError').text('');
                        ($('#availableLibraryModal') as any).modal('show');

                    }
                },
                error: function (error) {
                    console.error('Error:', error);
                    ZnodeGlobal.prototype.HideLoader();
                }
            });
        }
        else {
            alert('Please select at least one record.');
        }
    }

    public SaveDataExchange(e: Event) {
        e.preventDefault();
        var checkedIds = [];
        var checkedExchangeName = $('#dataExchangeName').val().toString().trim();
        $('#dataExchangeNameError').removeClass('error');
        $('#dataExchangeNameError').text('');
        if (AddSelectedExchange.prototype.ValidateDataExchangeName(checkedExchangeName)) {
            const exchangeModal = $('#availableLibraryModal');
            // Make modal not closable
            const modalInstance = bootstrap.Modal.getInstance(exchangeModal);
            exchangeModal.attr('data-bs-backdrop', 'static');
            exchangeModal.attr('data-bs-keyboard', 'false');

            // Refresh modal options
            modalInstance._config.backdrop = 'static';
            modalInstance._config.keyboard = false;
            $('#modalCloseBtn')
                .css({ 'pointer-events': 'none', 'opacity': '0.5' })
                .removeAttr('data-bs-dismiss');
            $('#btnSaveExchange').prop('disabled', true);
            // Add Loader
            $('input[type="checkbox"]:checked').each(function () {
                checkedIds.push($(this).val());
            });
            $.ajax({
                type: 'POST',
                url: '/commerce-connector/GetExchangeLibrary/ValidateExchangeName',
                data: JSON.stringify(checkedExchangeName),
                contentType: 'application/json',
                success: function (response) {
                    if (response.status) {
                        AddSelectedExchange.prototype.AddSelectedDataExchange(checkedIds, true, checkedExchangeName);
                    }
                    else {
                        $('#dataExchangeNameError').addClass('error');
                        $('#dataExchangeNameError').text(response.message);
                        AddSelectedExchange.prototype.ClosableModalPopup(exchangeModal, modalInstance);
                    }
                },
                error: function (error) {
                    console.error('Error:', error);
                    AddSelectedExchange.prototype.ClosableModalPopup(exchangeModal, modalInstance);
                }
            });
        }
    }

    private AddSelectedDataExchange(checkedIds, isFromPopup = false, checkedExchangeName = '') {
        ZnodeGlobal.prototype.ShowLoader();
        const exchangeModal = $('#availableLibraryModal');
        const modalInstance = bootstrap.Modal.getInstance(exchangeModal);
        $.ajax({
            type: 'POST',
            url: '/commerce-connector/GetExchangeLibrary/AddSelectedExchange',
            data: JSON.stringify({
                SelectedIds: checkedIds,
                SelectedExchangeName: checkedExchangeName
            }),
            contentType: 'application/json',
            success: function(response) {
                if (response.status) {
                    ZnodeGlobal.prototype.HideLoader();
                    var element: any = $(".messageBoxContainer");
                    window.location.href = '/commerce-connector/DataExchange/GetDataExchangeLibraryList';
                    if (!isFromPopup) {
                        alert(response.message);
                    }
                }
                else {
                    ZnodeGlobal.prototype.HideLoader();
                    ($('#availableLibraryModal') as any).modal('hide');
                    alert(response.message);
                    $('.exchange-checkbox:checked').each(function() {
                        $(this).prop('checked', false).change();
                    });
                    AddSelectedExchange.prototype.ClosableModalPopup(exchangeModal, modalInstance);
                    return false;
                }
            },
            error: function (error) {
                ZnodeGlobal.prototype.HideLoader();
                ($('#availableLibraryModal') as any).modal('hide');
                AddSelectedExchange.prototype.ClosableModalPopup(exchangeModal, modalInstance);
                console.error('Error:', error);
            }
        });
    }

    // Make modal closable and enable Svae and Back button.
    private ClosableModalPopup(exchangeModal: JQuery<HTMLElement>, modalInstance: any) {
        exchangeModal.attr('data-bs-backdrop', 'true');
        exchangeModal.attr('data-bs-keyboard', 'true');
        modalInstance._config.backdrop = true;
        modalInstance._config.keyboard = true;
        $('#modalCloseBtn')
            .css({ 'pointer-events': 'auto', 'opacity': '1' })
            .attr('data-bs-dismiss', 'modal');
        $('#btnSaveExchange').prop('disabled', false);
    }

    private ValidateDataExchangeName(exchangeName: string): boolean {
        let isValid = true;
        const validTextRegEx = /^[a-zA-Z0-9 ]+$/;
        if (exchangeName.trim() && validTextRegEx.test(exchangeName)) {
            $('#dataExchangeNameError').removeClass('error');
            $('#dataExchangeNameError').text('');
        }
        else if (!validTextRegEx.test(exchangeName) && exchangeName !== '') {
            $('#dataExchangeNameError').addClass('error');
            $('#dataExchangeNameError').text(resources.SpecialCharactersRegex);
            isValid = false;
        }
        else {
            $('#dataExchangeNameError').addClass('error');
            $('#dataExchangeNameError').text(resources.ExchangeNameError);
            isValid = false;
        }
        return isValid;
    }

    public OnDataExchangeLoad() {
        $('.exchange-checkbox').each(function () {
            if ($(this).is(":checked")) {
                $(this).prop('disabled', true);
            }
        });
    }

    public LoadAvailableLibrariesGrid(page: number) {
        ZnodeGlobal.prototype.ShowLoader();
        const request = {
            page: page,
            pageSize: Number($('#pageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#globalSearch").val()
        };

        $.ajax({
            url: '/commerce-connector/GetExchangeLibrary/RenderLibraryGrid',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(request),
            success: (res) => {
                const rowCount = res?.rows?.length || 0;

                if (rowCount === 0) {
                    if (this.gridInitialized) {
                        this.gridOptions.api.setRowData([]); 
                    }
                    $("#add-selected-btn").hide();
                    $("#availableLibrariesGrid").hide();
                    $("#paginationControls").hide();
                    $(".show-per-page").hide();
                    $(".show-page-count").hide();
                    $("#customRowInfo").hide();
                    $("#noRecordsMessage").show();
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    $("#noRecordsMessage").hide();
                    $(".show-per-page").show();
                    $(".show-page-count").show();
                    $("#customRowInfo").show();
                    $("#availableLibrariesGrid").show();
                    $("#paginationControls").show();
                    $("#add-selected-btn").show();
                }
                DynamicGrid.prototype.setTotalRows(res.totalCount);

                if (!this.gridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#availableLibrariesGrid');
                    new agGrid.Grid(gridDiv!, this.gridOptions);
                    this.gridInitialized = true;
                }
                if (!this.gridOptions.columnDefs?.length && res.columns) {
                    const dynamicCols = res.columns.map(c => ({
                        field: c.field,
                        headerName: c.title,
                        flex: 1,
                        sortable: c.sortable === true,
                        filter: false,
                        hide: ["dataExchangeLibraryId", "selected", "format"].indexOf(c.field) !== -1
                    }));
                    dynamicCols.forEach(col => {
                        if (col.field === "exchangeName") {
                            col.cellRenderer = function (params) {
                                const id = params.data?.dataExchangeLibraryId;
                                const exchangeName = params.value;
                                return `<a style="color: #181d1f;" data-id="${id}" title="${exchangeName}">${exchangeName}</a>`;
                            };
                        }
                    });
                    const checkboxCol = {
                        headerName: "",
                        field: "dataExchangeLibraryId",
                        width: 60,
                        sortable: false,
                        pinned: "left",
                        cellStyle: { textAlign: 'center', padding: '0px' },
                        cellRenderer: function (params) {
                            const id = params.value;
                            const exchangeName = params.data?.exchangeName;
                            const selected = params.data?.selected === 1 ? "checked" : "";
                            return `
                                    <input type="checkbox"
                                        class="form-check-input exchange-checkbox"
                                        value="${id}"
                                        data-exchange-name="${exchangeName}"
                                        data-selected="${selected ? "true" : "false"}"
                                        ${selected} />`;
                        }
                    };                  
                    this.gridOptions.api.setColumnDefs([checkboxCol, ...dynamicCols]);
                }

                this.gridOptions.api.setRowData(res.rows);
                setTimeout(() => {
                    this.gridOptions.api.sizeColumnsToFit();
                }, 100);
                // Auto-size columns to fit content
                const allColumnIds = [];
                this.gridOptions.columnApi.getAllColumns().forEach(col => allColumnIds.push(col.getColId()));
                this.gridOptions.columnApi.autoSizeColumns(allColumnIds);
                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, '', (page) => {
                    this.LoadAvailableLibrariesGrid(page);
                });
                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount);
                AddSelectedExchange.prototype.OnDataExchangeLoad();
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    public getGridOptions() {
        return this.gridOptions;
    }

}
$(document).ready(function () {
    $('#add-selected-btn').prop('disabled', true);
    const instance = new AddSelectedExchange();
    if ($("#availableLibrariesGrid").length > 0) {
        instance.LoadAvailableLibrariesGrid(1);
        $('#pageSizeSelect').on('change', () => {
            this.currentPageSize = + $('#pageSizeSelect').val();
            instance.LoadAvailableLibrariesGrid(1);
        });
        $('#globalSearchbtn').on('click', () => {
            instance.LoadAvailableLibrariesGrid(1)
        });
        $('#globalSearch').keydown(function (e) {
            if (e.keyCode === 13) {
                instance.LoadAvailableLibrariesGrid(1);
            }
        });
    }

    var exchangeNameElement = $('#dataExchangeName');
    $(exchangeNameElement).on('input change', (e) => {
        if (exchangeNameElement.val() == '' || exchangeNameElement.val() == undefined || exchangeNameElement.val() == null) {
            $('#dataExchangeNameError').addClass('error');
            $('#dataExchangeNameError').text(resources.ExchangeNameError);
        }
        else {
            $('#dataExchangeNameError').removeClass('error');
            $('#dataExchangeNameError').text('');
        }
    });
}); 

$(document).on('change', '.exchange-checkbox', function () {
    if (this.checked) {
        $('#add-selected-btn').prop('disabled', false);
        $('.exchange-checkbox').not(this).prop('checked', false);
    }
    else {
        $('#add-selected-btn').prop('disabled', true);
    }
});