﻿namespace Znode.CommerceConnector.Model
{
    public class BaseDefinitionModel
    {
        public int ERPBaseDefinitionId { get; set; }

        public int DataExchangeLibraryId { get; set; }
        public string? Name { get; set; }

        public string? Format { get; set; }

        public string? Version { get; set; }

        public string? DataSource { get; set; }

        public string? DataDestination { get; set; }
        public string? TriggerOrigin { get; set; }

        public string? Description { get; set; }
        public string? Tags { get; set; }
        public string? Access { get; set; }

        public string? Library { get; set; }
        public string? Collection { get; set; }
        public string? ConfigurationChangeJsonData { get; set; }
        public bool IsCustomProcessingRequired { get; set; } = false;
        public string? ProcessorFileName { get; set; }
        public string? MethodName { get; set; }
        public TransmissionConfigurationModel? TransmissionConfigurations { get; set; }
        public SchedulerConfigurationModel? SchedulerSettingModel { get; set; }

        public StatusActivationModel? StatusActivationModel { get; set; }

        public BaseDefinitionModel()
        {
            StatusActivationModel = new StatusActivationModel();
            SchedulerSettingModel = new SchedulerConfigurationModel();
        }
    }
}
