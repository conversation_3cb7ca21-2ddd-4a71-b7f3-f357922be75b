﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeApiconfigurationTable
{
    public int TransmissionId { get; set; }

    public int ConfigurationId { get; set; }

    public string? Apiaction { get; set; }

    public string? Endpoint { get; set; }

    public string? AuthType { get; set; }

    public string? GrantType { get; set; }

    public string? AccessTokenUrl { get; set; }

    public string? ClientId { get; set; }

    public string? ClientSecret { get; set; }

    public string? Scope { get; set; }

    public string? LocationOfCredentials { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public string? Type { get; set; }

    public string? Format { get; set; }

    public virtual ZnodeTransmissionConfigurationTable Configuration { get; set; } = null!;

    public virtual ICollection<ZnodeHeaderParameterTable> ZnodeHeaderParameterTables { get; set; } = new List<ZnodeHeaderParameterTable>();

    public virtual ICollection<ZnodeQueryParameterTable> ZnodeQueryParameterTables { get; set; } = new List<ZnodeQueryParameterTable>();
}
