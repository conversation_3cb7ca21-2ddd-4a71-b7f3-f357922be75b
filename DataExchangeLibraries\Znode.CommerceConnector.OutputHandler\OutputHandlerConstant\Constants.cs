﻿namespace Znode.CommerceConnector.OutputHandler
{
    public static class Constants
    {
        public static string DefaultLocaleCode = "1";
        public static string Source = "Source";
        public static string Destination = "Destination";
        public static string Authorization = "Authorization";
        public static string GET = "GET";
        public static string PUT = "PUT";
        public static string POST = "POST";
        public static string DELETE = "DELETE";
        public static string PATCH = "PATCH";
        public static string CallOutputHandler = "CallOutputHandler";
        public static string CallOutputHandlerMethodInOutputHandlerInitializerCalled = "CallOutputHandler method in OutputHandlerInitializer called";
        public static string SFTPOutputDataHandler = "SFTPOutputDataHandler";
        public static string APIOutputDataHandler = "APIOutputDataHandler";
        public static string FTPOutputDataHandler = "FTPOutputDataHandler";
        public static string SFTPOutputDataHandlerMethodInSFTPHandlerCalled = "SFTPOutputDataHandler method in SFTPHandler called";
        public static string GetSFTPCredentialsMethodInSFTPHandlerCalled = "GetSFTPCredentials method in SFTPHandler called";
        public static string UploadData = "UploadData";
        public static string UploadDataMethodInSFTPClientCalled = "UploadData method in SFTPClient called";
        public static string SFTPFolderInSFTPClientCalled = "sftp folder SFTPClient called ";
        public static string FolderPathNotFound = "Folder path not found: ";
        public static string FileToUploadIsEmpty = "File to upload is empty";
        public static string ErrorUploadingFileUsingSFTP = "Error uploading file using SFTP: ";
        public static string client_id = "client_id";
        public static string client_secret = "client_secret";
        public static string grant_type = "grant_type";
        public static string scope = "scope";
        public static string APIHandler = "APIHandler";
        public static string APIOutputDataHandlerMethodCalled = "APIOutputDataHandler method in APIHandler called";
        public static string GetOutputHandlerAPICredentials = "GetOutputHandlerAPICredentials method in APIHandler called";
        public static string GetMethodCalled = "Get method called";
        public static string GetMethodFailed = "Get method failed";
        public static string UploadDataUsingOAuth2 = "UploadDataUsingOAuth2";
        public static string UploadDataUsingOAuth2MethodCalled = "UploadDataUsingOAuth2 method in api client called";
        public static string UploadDataUsingOAuth2MethodFailed = "UploadDataUsingOAuth2 method in api client failed ";
        public static string UploadDataUsingBearer = "UploadDataUsingBearer";
        public static string UploadDataUsingBearerMethodCalled = "UploadDataUsingBearer method in api client called";
        public static string UploadDataUsingBearerMethodFailed = "UploadDataUsingBearer method in api client failed ";
        public static string ProcessorFileNotFound = "Processor File Not Found";
        public static string CompletedSuccessfully = "Completed Successfully";
        public static string Failed = "Failed";
        public static string Started = "Started";
        public static string GetInputHandlerHTTPCredentialsCalled = "Get input handler credentials method called in API Handler ";
        public static string GetInputHandlerHTTPCredentialsFailed = "Get input handler credentials method failed in API Handler ";
        public static string SendData = "Send Data";
        public static string CheckParamsMethod = "CheckParams";
        public static string CheckParamsMethodCalled = "CheckParams method in output handler called";
        public static string CheckParamsMethodFailed = "CheckParams method in output handler failed ";
    }
}
