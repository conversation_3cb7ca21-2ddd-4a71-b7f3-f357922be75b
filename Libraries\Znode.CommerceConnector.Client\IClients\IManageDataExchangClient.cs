﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client.IClients
{
    public interface IManageDataExchangClient
    {
        BaseDefinitionModel GetDataExchangeDetailsByID(int id);

        bool SaveDataExchangeDetails(BaseDefinitionModel model);

        BaseDefinitionModel GetBaseDefinitionDetailsByID(int erpId);

        bool TestConnection(string mode, string request);

        ConfigurationChangeLogListModel GetConfigurationChangeList(int id, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex, int? pageSize);
        
        bool ValidateSchedulerName(string schedulerName);
    }
}
