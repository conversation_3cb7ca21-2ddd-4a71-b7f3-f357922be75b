﻿using Newtonsoft.Json;

using Znode.CommerceConnector.Client.Clients;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client
{
    public class GetDataExchangeClient : BaseClient, IGetDataExchangeClient
    {
        public StandardDataExchangeListModel GetDataExchangeList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.ExchangeList();
            endpoint += BuildEndpointQueryString(null, filters, sorts, pageIndex, pageSize);
            StandardDataExchangeListModel standardDataExchangeListModel = apiClient.GetRequest<StandardDataExchangeListModel>(endpoint);
            return standardDataExchangeListModel;
        }

        public bool TriggerDataExchangeSchedular(int erpId)
        {
            ApiClient apiClient = new ApiClient();              
            string endpoint = ExchangeLibraryEndpoint.TriggerDataExchangeSchedular(erpId);
            bool response = apiClient.GetRequest<bool>(endpoint);
            return response;
        }

        public dynamic RealTimeDataScheduler(dynamic requestBody, int erpId)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.RealTimeDataScheduler(erpId, requestBody);
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(requestBody);
            requestModel.RequestType = "POST";
            dynamic response = apiClient.PostRequest<dynamic>(requestModel, out string statusCode);
            return response;
        }

        public bool DeleteDataExchangeSchedular(int erpId)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.DeleteDataExchangeSchedular(erpId);
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            TrueFalseResponse response = apiClient.PostRequest<TrueFalseResponse>(requestModel, out string statusCode);
            return response.IsSuccess;
        }

        public bool EnableDisableDataExchangeScheduler(int erpId, bool isActive)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.EnableDisableTaskScheduler(erpId,isActive);         
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            TrueFalseResponse response = apiClient.PostRequest<TrueFalseResponse>(requestModel, out string statusCode);
            return response.IsSuccess;
        }

        public int CreateEditBaseDataExchange(BaseDefinitionModel model)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.CreateEditBaseDataExchange();
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(model);
            requestModel.RequestType = "POST";
            int response = apiClient.PostRequest<int>(requestModel, out string statusCode);
            return response;
        }
        public ImportLogsListResponse GetProcessingLogList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10)
        {
            ApiClient apiClient = new ApiClient();

            string endpoint = ExchangeLibraryEndpoint.GetProcessingLogList();
            endpoint += BuildEndpointQueryString(null, filters, sorts, pageIndex, pageSize);
            ImportLogsListResponse processingLogListModel = apiClient.GetRequest<ImportLogsListResponse>(endpoint);
            return processingLogListModel;
        }

        public ImportLogDetailsListResponse GetProcessingLogDetails(int logId, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetProcessingLogDetails(logId);
            endpoint += BuildEndpointQueryStringWithoutEncode(null, filters, sorts, pageIndex, pageSize);
            ImportLogDetailsListResponse processingLogDetailsModel = apiClient.GetRequest<ImportLogDetailsListResponse>(endpoint);
            return processingLogDetailsModel;
        }

        public ProcessorLogListResponse GetGenericLogList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetGenericLogList();
            endpoint += BuildEndpointQueryString(null, filters, sorts, pageIndex, pageSize);
            ProcessorLogListResponse processingLogListModel = apiClient.GetRequest<ProcessorLogListResponse>(endpoint);
            return processingLogListModel;
        }

        public ProcessingLogDetailListModel GetGenericLogDetails(int logId, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetGenericLogDetails(logId);
            endpoint += BuildEndpointQueryString(null, filters, sorts, pageIndex, pageSize);
            ProcessingLogDetailListModel processingLogListModel = apiClient.GetRequest<ProcessingLogDetailListModel>(endpoint);
            return processingLogListModel;
        }
    }
}
