﻿using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client
{
    public interface IExchangeLibraryClient
    {
        /// <summary>
        /// Get ship to code list.
        /// </summary>
        /// <param name="accountId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        DataExchangeListModel GetLibrariesList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex, int? pageSize);


        /// <summary>
        /// Inserts data into ZNodeBaseDefinition table based on ids.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns>bool result</returns>
        bool AddSelectedExchange(AddSelectedExchangeModel addSelectedExchangeModel);

        /// <summary>
        /// Validate whether Exchange name exists or not in Base Definition. 
        /// </summary>
        /// <param name="exchangeName"></param>
        /// <returns>Will return true if Exchange name is proper and not exists.</returns>
        bool ValidateExchangeName(string exchangeName);
    }
}
