﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="mt-3">
    <!-- Hidden ERP ID -->
    @Html.HiddenFor(model => model.ERPBaseDefinitionId)
    @Html.HiddenFor(Model => Model.TransmissionConfigurations.TransmissionConfigurationId)


    <!-- Server Address -->
    <div class="row">
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfig.ServerAddress" class="form-label" data-test-selector="lblServerAddress" aria-label="Server Address"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfig.ServerAddress" class="form-control" placeholder="SFTP Server Address" id="SourceServerAddress" data-test-selector="txtServerAddress" aria-label="Source Server Address" />
            <span id="SourceServerAddressError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Folder Path -->
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfig.FolderPath" class="form-label" data-test-selector="lblFolderPath" aria-label="Folder Path"></label></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfig.FolderPath" class="form-control" id="SourceFolderPath" placeholder="Directory path on the server" data-test-selector="txtFolderPath" aria-label="Source Folder Path" />
            <span id="SourceFolderPathError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Filename -->
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfig.Filename" class="form-label" data-test-selector="lblFilename" aria-label="File Name"></label></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfig.Filename" class="form-control" id="SourceFilename" placeholder="File Name to retrieve" data-test-selector="txtFilename" aria-label="Source File Name" />
            <span id="SourceFilenameError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Port Number -->
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfig.PortNumber" class="form-label" data-test-selector="lblPortNumber" aria-label="Port Number"></label></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfig.PortNumber" type="text" inputmode="numeric" class="form-control" id="SourcePortNumber" data-test-selector="txtPortNumber" aria-label="Source Port" maxlength="4" />
            <span id="SourcePortNumberError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Username -->
        <div class="col-md-12 col-lg-6 mb-3 position-relative">
            <label asp-for="TransmissionConfigurations.SftpConfig.Username" class="form-label" data-test-selector="lblUsername" aria-label="Username"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <div class="input-group">
                <input asp-for="TransmissionConfigurations.SftpConfig.Username" type="text" id="SourceUsername" class="form-control" data-test-selector="txtUsername" aria-label="Source Username" />
            </div>
            <span id="SourceUsernameError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Password -->
        <div class="col-md-12 col-lg-6 mb-3 position-relative">
            <label asp-for="TransmissionConfigurations.SftpConfig.Password" class="form-label" data-test-selector="lblPassword" aria-label="SFTP Configuration Password"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <div class="input-group">
                <input class="form-control"
                       type="password"
                       name="TransmissionConfigurations.SftpConfig.Password"
                       id="SourcePassword"
                       value="@Model.TransmissionConfigurations?.SftpConfig?.Password" data-test-selector="txtPassword" aria-label="Source Password" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle password visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
            <span id="SourcePasswordError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Action After Retrieval -->
        <!-- Added d-none for future use and set a default to keep validation working for the required field. -->
        <div class="col-md-12 col-lg-6 mb-3 d-none">
            <label asp-for="TransmissionConfigurations.SftpConfig.ActionAfterRetrieval" class="form-label" data-test-selector="lblActionAfterRetrieval" aria-label="Action After Retrieval"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.SftpConfig.ActionAfterRetrieval" class="form-select" id="SourceActionAfterRetrieval" data-test-selector="drpActionAfterRetrieval" aria-label="Source Action After File Retrieval">
                @* <option value="">@CommerceConnector_Resources.SelectAction</option> *@
                <option value="Delete">@CommerceConnector_Resources.DeleteAction</option>
                <option value="Archive">@CommerceConnector_Resources.Archive</option>
            </select>
            <span id="SourceActionAfterRetrievalError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfig.Format" class="form-label" data-test-selector="lblSourceFileFormat" aria-label="Source Format"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.SftpConfig.Format" class="form-select" id="SFTPSourceDropdownFileFormat" data-test-selector="drpFileFormat" aria-label="Source File Format">
                <option value="">@CommerceConnector_Resources.SelectSourceFileFormat</option>
                <option value="CSV">@CommerceConnector_Resources.CSV</option>
                <option value="JSON">@CommerceConnector_Resources.JSON</option>
                <option value="XML">@CommerceConnector_Resources.XML</option>
            </select>
            <span id="SFTPSourceFileFormatError" class="text-danger field-validation-error"> </span>
        </div>
    </div>
</div>
