﻿using AutoMapper;
using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;
using Znode.Libraries.Resources.CommerceConnector_Resources;

namespace Znode.CommerceConnector.Core.Agents
{
    public class GetExchangeLibrariesAgent : IGetExchangeLibrariesAgent
    {
        private readonly IExchangeLibraryClient _exchangeLibrary;
        private readonly IMapper _mapper;
        private readonly IYAMLClient _yAMLClient;

        public GetExchangeLibrariesAgent(IExchangeLibraryClient exchangeLibrary, IMapper mapper, IYAMLClient yAMLClient)
        {
            _mapper = mapper;
            _exchangeLibrary = exchangeLibrary;
            _yAMLClient = yAMLClient;
        }

        public DataExchangeListViewModel GetLibrarieslist(GridModel gridModel)
        {
            FilterCollection filter = new FilterCollection();
            if (!string.IsNullOrWhiteSpace(gridModel?.GlobalSearch))
            {
                filter.Add(new FilterTuple($"{CommerceConnectorConstants.Name} | {CommerceConnectorConstants.Tags} | {CommerceConnectorConstants.Collection}", CommerceConnectorConstants.Contains, gridModel.GlobalSearch));
            }

            Dictionary<string, string> sorts = new();
            if (!(string.IsNullOrWhiteSpace(gridModel?.SortDirection) && string.IsNullOrWhiteSpace(gridModel?.SortField)))
            {
                if (gridModel.SortField?.ToLower() == "exchangename")
                    gridModel.SortField = "name";
                sorts.Add(gridModel.SortField, gridModel.SortDirection);
            }

            DataExchangeListModel modelWrapper = _exchangeLibrary.GetLibrariesList(filter, sorts, gridModel.Page, gridModel.PageSize);
            List<DataExchangeViewModel> viewModels = _mapper.Map<List<DataExchangeViewModel>>(modelWrapper.DataModelList);
            DataExchangeListViewModel listViewModel = new DataExchangeListViewModel()
            {
                DataExchangeList = viewModels,
                TotalRows = modelWrapper.TotalRows
            };
            return listViewModel;
        }

        public bool AddSelectedExchange(AddSelectedExchangeViewModel addSelectedExchangeViewModel, out string message)
        {
            message = CommerceConnector_Resources.AddSelectedErrorMessage;
            try
            {
                MediaConfigurationModel mediaConfiguration = _yAMLClient.GetMediaConfiguration();
                AddSelectedExchangeModel addSelectedExchangeModel = _mapper.Map<AddSelectedExchangeModel>(addSelectedExchangeViewModel);
                addSelectedExchangeModel.MediaConfiguration = mediaConfiguration;
                bool isAdded = _exchangeLibrary.AddSelectedExchange(addSelectedExchangeModel);
                if (!isAdded)
                    message = CommerceConnector_Resources.AddSelectedErrorMessage;
                return isAdded;
            }
            catch (Exception ex)
            {       
                message = CommerceConnector_Resources.AddSelectedErrorMessage;
                return false;
            }
        }

        public bool ValidateExchangeName(string exchangeName, out string message)
        {
            message = string.Empty;
            try
            {
                bool isValid = _exchangeLibrary.ValidateExchangeName(exchangeName);
                if(!isValid)
                    message = CommerceConnector_Resources.ExchangeNameExistsError;
                return isValid;
            }
            catch (Exception ex)
            {
                message = CommerceConnector_Resources.ExchangeNameExistsError;
                return false;
            }
        }
    }
}
