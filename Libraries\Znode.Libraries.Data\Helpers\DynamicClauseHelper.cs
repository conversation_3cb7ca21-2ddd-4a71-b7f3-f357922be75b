﻿using System;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.Libraries.Data.Helpers
{
    public class DynamicClauseHelper
    {
        private int placeLocator;
        private DateTime filterValueinDateTime;
        private const string CommaReplacer = "^";

        public static string GenerateDynamicWhereClauseForSP(FilterDataCollection filters)
        {
            string whereClause = string.Empty;
            DynamicClauseHelper dynamicClauseHelper = new DynamicClauseHelper();
            if (filters?.Count > 0)
            {
                foreach (var tuple in filters)
                {
                    var filterKey = tuple.Item1;
                    var filterOperator = tuple.Item2;
                    var filterValue = tuple.Item3;
                    if (!Equals(filterKey, "_") && !Equals(filterKey.ToLower(), "usertype"))
                        whereClause = (string.IsNullOrEmpty(whereClause))
                            ? dynamicClauseHelper.SetQueryParameterForSP(filterKey, filterOperator, filterValue)
                            : $"{whereClause} {StoredProcedureKeys.AND} {dynamicClauseHelper.SetQueryParameterForSP(filterKey, filterOperator, filterValue)}";
                }
            }
            return string.IsNullOrEmpty(whereClause) ? string.Empty : string.Format("({0})", whereClause);
        }

        public virtual string SetQueryParameterForSP(string filterKey, string filterOperator, string filterValue)
        {
            string query = string.Empty;
            if (Equals(filterOperator, ProcedureFilterOperators.Equals)) return AppendQuery(filterKey, " = ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.Like)) return AppendLikeQuery(filterKey, " like ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.GreaterThan)) return AppendQuery(filterKey, " > ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.GreaterThanOrEqual)) return AppendQuery(filterKey, " >= ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.LessThan)) return AppendQuery(filterKey, " < ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.LessThanOrEqual)) return AppendQuery(filterKey, " <= ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.Contains)) return AppendLikeQuery(filterKey, " like ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.StartsWith)) return GenerateStartwithQuery(filterKey, " like ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.EndsWith)) return GenerateEndwithQuery(filterKey, " like ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.Is)) return GenerateQuery(filterKey, " = ", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.NotIn)) return GenerateNotInQuery(filterKey, "not in", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.In)) return GenerateSPInQuery(filterKey, "in", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.NotEquals)) return GenerateNotaEquals(filterKey, "!=", filterValue);
            if (Equals(filterOperator, ProcedureFilterOperators.Between)) return AppendQuery(filterKey, " between ", filterValue);

            return query;
        }

        public virtual string AppendQuery(string filterKey, string filterOperator, string filterValue)
        {
            //This Special Case required to Get the Franchisable Product in which Product belongs to Portals & all product having franchisable flat true will comes.
            if (filterKey.Contains("$")) return AppendSpecialOrClauseToQuery(filterKey, filterOperator, filterValue);
            //To generate OR condition with null value for the filter key passed.
            if (filterKey.Contains("!")) return AppendNullOrClauseToQuery(filterKey, filterOperator, filterValue, null);
            return $"{StoredProcedureKeys.TildOperator}{filterKey}{StoredProcedureKeys.TildOperator}{filterOperator}{filterValue}";
        }

        public virtual string AppendSpecialOrClauseToQuery(string filterKey, string filterOperator, string filterValue)
        {
            string[] strSplit = filterKey.Split('$');
            return $"({StoredProcedureKeys.TildOperator}{strSplit[0]}{StoredProcedureKeys.TildOperator}={filterValue} OR ({StoredProcedureKeys.TildOperator}{strSplit[0]}{StoredProcedureKeys.TildOperator} is null and {StoredProcedureKeys.TildOperator}{strSplit[1]}{StoredProcedureKeys.TildOperator} = 1 ))";
        }

        public virtual string AppendNullOrClauseToQuery(string filterKey, string filterOperator, string filterValue, List<object> filterList)
        {
            string[] strSplit = filterKey.Split('!');
            if (IsDateTime(filterValue, out filterValueinDateTime))
            {
                string query = $"{strSplit[0]}{filterOperator}(@{placeLocator})";
                if (!Equals(filterList, null))
                {
                    filterList.Add(filterValueinDateTime);
                    placeLocator++;
                }
                return $"({query} || {strSplit[0]} = null)";
            }
            else
                return $"({StoredProcedureKeys.TildOperator}{strSplit[0]}{StoredProcedureKeys.TildOperator}{filterOperator}{filterValue} OR {StoredProcedureKeys.TildOperator}{strSplit[0]}{StoredProcedureKeys.TildOperator} Is NULL)";
        }

        public virtual string AppendLikeQuery(string filterKey, string filterOperator, string filterValue)
        {
            if (filterKey.Contains("|")) return AppendOrClauseToQuery(filterKey, filterOperator, filterValue);
            return GenerateLikeQuery(filterKey, filterOperator, filterValue);
        }

        public virtual string AppendOrClauseToQuery(string filterKey, string filterOperator, string filterValue)
        {
            string strQuery = string.Empty;
            if (filterKey.Contains("|"))
            {
                string innerQuery = string.Empty;
                foreach (string item in filterKey.Split('|'))
                {
                    if (!string.IsNullOrEmpty(item))
                        innerQuery = (string.IsNullOrEmpty(innerQuery)) ? GenerateLikeQuery(item, filterOperator, filterValue) : innerQuery + " OR " + GenerateLikeQuery(item, filterOperator, filterValue);
                }
                strQuery = (string.IsNullOrEmpty(innerQuery)) ? string.Empty : string.Format("({0})", innerQuery);
            }
            return strQuery;
        }

        public virtual string AppendOrClauseToQueryStartWith(string filterKey, string filterOperator, string filterValue)
        {
            string strQuery = string.Empty;
            if (filterKey.Contains("|"))
            {
                string innerQuery = string.Empty;
                foreach (string item in filterKey.Split('|'))
                {
                    if (!string.IsNullOrEmpty(item))
                        innerQuery = (string.IsNullOrEmpty(innerQuery)) ? GenerateStartwithQuery(item, filterOperator, filterValue) : innerQuery + " OR " + GenerateStartwithQuery(item, filterOperator, filterValue);
                }
                strQuery = (string.IsNullOrEmpty(innerQuery)) ? string.Empty : string.Format("({0})", innerQuery);
            }
            return strQuery;
        }

        public virtual bool IsDateTime(string filterValue, out DateTime filterValueInDateTime)
        => DateTime.TryParse(filterValue, out filterValueInDateTime);

        public virtual string GenerateLikeQuery(string filterKey, string filterOperator, string filterValue)
        => $"{StoredProcedureKeys.TildOperator}{filterKey}{StoredProcedureKeys.TildOperator}{filterOperator}'%{filterValue}%'";

        public virtual string GenerateNotaEquals(string filterKey, string filterOperator, string filterValue)
        {
            //This Special Case required to Get the Franchisable Product in which Product belongs to Portals & all product having franchisable flat true will comes.
            if (filterKey.Contains("$")) return AppendSpecialOrClauseToQuery(filterKey, filterOperator, filterValue);
            //To generate OR condition with null value for the filter key passed.
            if (filterKey.Contains("!")) return AppendNullOrClauseToQuery(filterKey, filterOperator, filterValue, null);
            return string.Format("{0}{1}{2}{3}{4}'{5}'", StoredProcedureKeys.TildOperator, filterKey, " ", filterOperator, " ", filterValue);
        }

        public virtual string GenerateEndwithQuery(string filterKey, string filterOperator, string filterValue)
            => $"{StoredProcedureKeys.TildOperator}{filterKey}{StoredProcedureKeys.TildOperator}{filterOperator}'%{filterValue}'";

        public virtual string GenerateSPInQuery(string filterKey, string filterOperator, string filterValue)
        {
            string[] filterArray = filterValue.Split(',');
            string filterValues = string.Empty;

            string filterClause = $"{filterKey} in";

            for (int index = 0; index < filterArray.Length; index++)
                filterValues += string.IsNullOrEmpty(filterValues) ? $"'{filterArray[index]}'" : $",'{filterArray[index]}'";

            if (!string.IsNullOrEmpty(filterValues))
                filterClause = string.Format("({0}({1}))", filterClause, filterValues);
            else
                filterClause = string.Empty;

            return filterClause;
        }

        public virtual string GenerateStartwithQuery(string filterKey, string filterOperator, string filterValue)
        {
            if (filterKey.Contains("|")) return AppendOrClauseToQueryStartWith(filterKey, filterOperator, filterValue);
            return $"{StoredProcedureKeys.TildOperator}{filterKey}{StoredProcedureKeys.TildOperator}{filterOperator}'{filterValue}%'";
        }

        public virtual string GenerateQuery(string filterKey, string filterOperator, string filterValue)
        => string.Format("{0}{1}{2}{3}'{4}'", StoredProcedureKeys.TildOperator, filterKey, StoredProcedureKeys.TildOperator, filterOperator, filterValue);

        public virtual string GenerateNotInQuery(string filterKey, string filterOperator, string filterValue)
        {
            string[] filterArray = filterValue.Split(',');
            string filterClause = string.Empty;
            for (int index = 0; index < filterArray.Length; index++)
            {
                string filter = $" and {filterKey} != {filterArray[index]}";
                filterClause += filter;
            }
            filterClause = filterClause.Remove(0, 4);

            return filterClause;
        }

        public static void SetPaging(NameValueCollection page, out int pagingStart, out int pagingLength)
        {
            // We use int.MaxValue for the paging length to ensure we always get total results back
            pagingStart = 1;
            pagingLength = int.MaxValue;
            if (!Equals(page, null) && page.HasKeys())
            {
                // Only do if both index and size are given
                if (!string.IsNullOrEmpty(page.Get(PageKeys.Index)) && !string.IsNullOrEmpty(page.Get(PageKeys.Size)))
                {
                    pagingStart = Convert.ToInt32(page.Get(PageKeys.Index));
                    pagingLength = Convert.ToInt32(page.Get(PageKeys.Size));
                }
            }
        }

        public static string GenerateDynamicOrderByClause(NameValueCollection sorts)
        {
            string orderBy = string.Empty;
            if (sorts?.Count > 0 && sorts.HasKeys())
            {
                foreach (string key in sorts.AllKeys)
                {
                    if (!string.IsNullOrWhiteSpace(key))
                    {
                        orderBy = orderBy + (!string.IsNullOrWhiteSpace(orderBy) ? "," : "") + $"{key} {sorts.Get(key)}";
                    }
                }
            }
            return orderBy;
        }
    }
}
