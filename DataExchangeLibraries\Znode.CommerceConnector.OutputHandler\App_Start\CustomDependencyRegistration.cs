﻿using Znode.CommerceConnector.OutputHandler.IHandlers;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Znode.CommerceConnector.OutputHandler.Handlers;

namespace Znode.CommerceConnector.OutputHandler
{
    public static class CustomDependencyRegistration
    {
        public static void RegisterOutputHandlerDI(this WebApplicationBuilder builder)
        {
            builder.Services.AddTransient<IAPIHandler, APIHandler>();
            builder.Services.AddTransient<ISFTPHandler, SFTPHandler>();            
            builder.Services.AddTransient<IFTPHandler, FTPHandler>();            
            builder.Services.AddTransient<ISFTPClient, SFTPClient>();
            builder.Services.AddTransient<IAPIClient, APIClient>();
            builder.Services.AddTransient<IFTPClient, FTPClient>();
        }
    }
}