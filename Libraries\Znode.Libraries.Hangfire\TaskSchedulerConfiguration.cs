﻿using Microsoft.Data.SqlClient;

using System.Data;
using System.Diagnostics;
using System.Reflection;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

using Constants = Znode.CommerceConnector.OutputHandler.Constants;


namespace Znode.Libraries.Hangfire
{
    public class TaskSchedulerConfiguration : ITaskSchedulerConfiguration
    {
        public object Result { get; set; }
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public TaskSchedulerConfiguration(IOutputHandlerInitializer outputHandler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = outputHandler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }
        public bool TriggerTaskSchedular(int erpId, dynamic inputModel)
        {
            MongoLogging.LogMessage(MongoLoggingConstants.TaskSchedulerCalled, MongoLoggingConstants.TaskSchedulerConfiguration, TraceLevel.Info);
            ProcessorDetails processorDetails = HelperMethods.GetProcessorDetails(erpId);
            bool isSchedularTriggered = CallProcessor(processorDetails, erpId, null);
            if (!isSchedularTriggered)
                InsertUpdateProcessorLogs(erpId, processorDetails.LogId, Constants.Failed);
            UpdateLastRunTime(erpId);
            return isSchedularTriggered;
        }

        public bool CallProcessor(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            MongoLogging.LogMessage(MongoLoggingConstants.CallProcessorCalled, MongoLoggingConstants.TaskSchedulerConfiguration, TraceLevel.Info);

            ProcessorLogResponse response = InsertUpdateProcessorLogs(erpId, processorDetails.LogId, Constants.Started);
            if (response?.Status ?? false)
                processorDetails.LogId = response.Id;
            else
                MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, MongoLoggingConstants.TaskSchedulerConfiguration, TraceLevel.Info);

            Assembly _erpAssembly = Assembly.Load("Znode.CommerceConnector.Processor");
            Type _erpClassName = (_erpAssembly?.GetTypes()).FirstOrDefault(g => g.Name == Convert.ToString(processorDetails.ProcessorFileName));

            if (!Equals(_erpClassName, null))
            {
                //Create instance
                object _erpInstance = Activator.CreateInstance(_erpClassName, new object[] { _outputHandler, _inputHandler, _iParser });
                MethodInfo _method = _erpClassName.GetMethods().FirstOrDefault();

                Result = _method?.Invoke(_erpInstance, new object[] { processorDetails, erpId, inputModel });
                return (bool)Convert.ToBoolean(Result);
            }
            bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, processorDetails.LogId, string.Empty, false, Constants.ProcessorFileNotFound);
            if (!logResponse)
                MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, MongoLoggingConstants.TaskSchedulerConfiguration, TraceLevel.Error);
            return false;
        }

        private ProcessorLogResponse InsertUpdateProcessorLogs(int erpId, int logId, string statusName)
        {
            int totalRecords = 0;
            int failedRecords = 0;
            int userid = 0;
            string logMessage = string.Empty;
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@CustomProcessLogId", logId));
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@StatusName", statusName));
            sqlParameters.Add(new SqlParameter("@ImportLogMessage", logMessage));
            sqlParameters.Add(new SqlParameter("@TotalProcessedRecords", totalRecords));
            sqlParameters.Add(new SqlParameter("@FailedRecordCount ", failedRecords));
            sqlParameters.Add(new SqlParameter("@SuccessRecordCount", totalRecords));
            sqlParameters.Add(new SqlParameter("@ProcessStartedDate", logId > 0 ? DBNull.Value : DateTime.UtcNow));
            sqlParameters.Add(new SqlParameter("@ProcessCompletedDate", logId > 0 ? DateTime.UtcNow : DBNull.Value));
            sqlParameters.Add(new SqlParameter("@UserId", userid));
            sqlParameters.Add(new SqlParameter("@Status", SqlDbType.Bit) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_InsertUpdateImportDataExchangeProcessLog", sqlParameters, out outValue);
            return znodeViewRepository.ConvertDataTableToList<ProcessorLogResponse>(response).FirstOrDefault();
        }

        private bool UpdateLastRunTime(int erpId)
        {
            SchedulerConfigurationModel configurationModel = HelperMethods.GetSchedulerConfiguration(erpId);
            configurationModel.LastRunTime = DateTime.UtcNow;
            return HelperMethods.SaveSchedulerConfiguration(configurationModel);
        }
    }
}
