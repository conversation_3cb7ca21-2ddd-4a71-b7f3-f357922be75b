﻿using Hangfire;
using System.Diagnostics;
using Znode.Libraries.ECommerce.Utilities;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Hangfire;

namespace Znode.CommerceConnector.Hangfire
{
    public class HangfireJob : IHangfireJob
    {
        public virtual bool ConfigureJobs(SchedulerConfigurationModel model, string schedulerStatus, out string hangfireJobId)
        {
            var status = false;
            hangfireJobId = string.Empty;
            try
            {
                MongoLogging.LogMessage(MongoLoggingConstants.JobCreationStarted + model.SchedulerName, MongoLoggingConstants.HangfireJob, TraceLevel.Info);

                //In case of update, delete the current job and create a new job in Hangfire after that.
                bool removeJobResult = RemoveJob(model);
                if (model.SchedulerType == MongoLoggingConstants.Scheduled && schedulerStatus == "True")
                {
                    if (model.IsInstantJob)
                    {
                        hangfireJobId = BackgroundJob.Enqueue<TaskSchedulerConfiguration>(x => x.TriggerTaskSchedular(Convert.ToInt32(model.ERPBaseDefinitionId), null));
                    }
                    else if (model.SchedulerFrequency == MongoLoggingConstants.OneTimeScheduler)
                    {
                        hangfireJobId = BackgroundJob.Schedule<TaskSchedulerConfiguration>(x => x.TriggerTaskSchedular(Convert.ToInt32(model.ERPBaseDefinitionId), null), new DateTimeOffset(TimeZoneInfo.ConvertTimeFromUtc(model.StartDate.Value, TimeZoneInfo.Local)));
                    }
                    else if (model.SchedulerFrequency == MongoLoggingConstants.RecurringScheduler)
                    {
                        RecurringJob.AddOrUpdate<TaskSchedulerConfiguration>(model.SchedulerName, x => x.TriggerTaskSchedular(Convert.ToInt32(model.ERPBaseDefinitionId), null), model.CronExpression);
                    }
                    MongoLogging.LogMessage(MongoLoggingConstants.JobCreated + model.SchedulerName, MongoLoggingConstants.HangfireJob, TraceLevel.Info);
                }
                status = true;
            }
            catch (Exception ex)
            {
                status = false;
                MongoLogging.LogMessage(string.Format(MongoLoggingConstants.HangfireError, ex.Message), MongoLoggingConstants.HangfireJob, TraceLevel.Error, ex);
            }
            return status;
        }

        public virtual bool RemoveJob(SchedulerConfigurationModel schedulerModel)
        {
            bool result = true;

            try
            {
                if (schedulerModel.ERPBaseDefinitionId <= 0)
                    return result;

                //Delete one-time job
                if (!string.IsNullOrEmpty(schedulerModel.HangfireJobId))
                    result = BackgroundJob.Delete(schedulerModel.HangfireJobId);

                //Delete recurring job
                result = RemoveJob(schedulerModel.SchedulerName);
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(ex, MongoLoggingConstants.HangfireJob, TraceLevel.Error);
                result = false;
            }

            return result;
        }

        protected virtual bool RemoveJob(string schedulerName)
        {
            bool status;
            try
            {
                RecurringJob.RemoveIfExists(schedulerName);
                MongoLogging.LogMessage(MongoLoggingConstants.JobRemoved + schedulerName, MongoLoggingConstants.HangfireJob, TraceLevel.Info);
                status = true;
            }
            catch (Exception ex)
            {
                status = false;
                MongoLogging.LogMessage(string.Format(MongoLoggingConstants.JobRemovingError, ex.Message), MongoLoggingConstants.HangfireJob, TraceLevel.Error, ex);
            }
            return status;
        }
    }
}
