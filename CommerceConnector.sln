﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.35027.167
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.Engine.CommerceConnector", "Commerce Connector\Znode.Engine.CommerceConnector.csproj", "{A76D7B2A-72B4-462C-9166-D407B748C027}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Libraries", "Libraries", "{833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.Core", "Libraries\Znode.CommerceConnector.Core\Znode.CommerceConnector.Core.csproj", "{54809410-1612-4862-B5D9-E1E153394C8D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.Client", "Libraries\Znode.CommerceConnector.Client\Znode.CommerceConnector.Client.csproj", "{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.Model", "Libraries\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj", "{8763C5E5-8B5D-4460-A062-CF32FE134971}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.Engine.API", "Znode.Engine.Api\Znode.Engine.API.csproj", "{0626BE95-9B16-4CAB-A901-464F4D28A2E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.API", "Libraries\Znode.CommerceConnector.API\Znode.CommerceConnector.API.csproj", "{27179954-098E-43E5-92EF-6647BD12BDAA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.Libraries.ECommerce.Utilities", "Libraries\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj", "{EDBBB322-4555-4E79-8B00-E6C9B9AECAF9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.Libraries.Data", "Libraries\Znode.Libraries.Data\Znode.Libraries.Data.csproj", "{29235D36-0E61-4DDB-80BE-EF9786B3A2EB}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DataExchangeLibraries", "DataExchangeLibraries", "{FE133AB8-76CA-4C48-9FFC-55E5E886D9A9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.InputHandler", "DataExchangeLibraries\Znode.CommerceConnector.InputHandler\Znode.CommerceConnector.InputHandler.csproj", "{64AFABB8-F3A7-4224-8C0A-74A926000129}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.Processor", "DataExchangeLibraries\Znode.CommerceConnector.Processor\Znode.CommerceConnector.Processor.csproj", "{127A0CAC-38A7-4001-9F59-FD94E7AA8D01}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.CommerceConnector.OutputHandler", "DataExchangeLibraries\Znode.CommerceConnector.OutputHandler\Znode.CommerceConnector.OutputHandler.csproj", "{C3C4DBA1-A34A-4D14-B09B-175FEB8BE79A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Znode.Libraries.Resources", "Libraries\Znode.Libraries.Resources\Znode.Libraries.Resources.csproj", "{33545516-35B6-40CF-9AF0-1E3E83AAC994}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Znode.CommerceConnector.Parser", "Znode.CommerceConnector.Parser\Znode.CommerceConnector.Parser.csproj", "{5797D0A7-F06C-4098-B371-FD80DEF94CB5}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{81DDED9D-158B-E303-5F62-77A2896D2A5A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Znode.CommerceConnector.Hangfire", "Libraries\Znode.CommerceConnector.Hangfire\Znode.CommerceConnector.Hangfire.csproj", "{A9A9C493-3532-4E6B-A6BE-46829172A61C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Znode.Libraries.Hangfire", "Libraries\Znode.Libraries.Hangfire\Znode.Libraries.Hangfire.csproj", "{38A750EE-B7F5-4F9E-B21D-802785E18165}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A76D7B2A-72B4-462C-9166-D407B748C027}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A76D7B2A-72B4-462C-9166-D407B748C027}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A76D7B2A-72B4-462C-9166-D407B748C027}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A76D7B2A-72B4-462C-9166-D407B748C027}.Release|Any CPU.Build.0 = Release|Any CPU
		{54809410-1612-4862-B5D9-E1E153394C8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{54809410-1612-4862-B5D9-E1E153394C8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{54809410-1612-4862-B5D9-E1E153394C8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{54809410-1612-4862-B5D9-E1E153394C8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}.Release|Any CPU.Build.0 = Release|Any CPU
		{8763C5E5-8B5D-4460-A062-CF32FE134971}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8763C5E5-8B5D-4460-A062-CF32FE134971}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8763C5E5-8B5D-4460-A062-CF32FE134971}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8763C5E5-8B5D-4460-A062-CF32FE134971}.Release|Any CPU.Build.0 = Release|Any CPU
		{0626BE95-9B16-4CAB-A901-464F4D28A2E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0626BE95-9B16-4CAB-A901-464F4D28A2E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0626BE95-9B16-4CAB-A901-464F4D28A2E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0626BE95-9B16-4CAB-A901-464F4D28A2E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{27179954-098E-43E5-92EF-6647BD12BDAA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27179954-098E-43E5-92EF-6647BD12BDAA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27179954-098E-43E5-92EF-6647BD12BDAA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27179954-098E-43E5-92EF-6647BD12BDAA}.Release|Any CPU.Build.0 = Release|Any CPU
		{EDBBB322-4555-4E79-8B00-E6C9B9AECAF9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EDBBB322-4555-4E79-8B00-E6C9B9AECAF9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EDBBB322-4555-4E79-8B00-E6C9B9AECAF9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EDBBB322-4555-4E79-8B00-E6C9B9AECAF9}.Release|Any CPU.Build.0 = Release|Any CPU
		{29235D36-0E61-4DDB-80BE-EF9786B3A2EB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29235D36-0E61-4DDB-80BE-EF9786B3A2EB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29235D36-0E61-4DDB-80BE-EF9786B3A2EB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29235D36-0E61-4DDB-80BE-EF9786B3A2EB}.Release|Any CPU.Build.0 = Release|Any CPU
		{64AFABB8-F3A7-4224-8C0A-74A926000129}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{64AFABB8-F3A7-4224-8C0A-74A926000129}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{64AFABB8-F3A7-4224-8C0A-74A926000129}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{64AFABB8-F3A7-4224-8C0A-74A926000129}.Release|Any CPU.Build.0 = Release|Any CPU
		{127A0CAC-38A7-4001-9F59-FD94E7AA8D01}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{127A0CAC-38A7-4001-9F59-FD94E7AA8D01}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{127A0CAC-38A7-4001-9F59-FD94E7AA8D01}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{127A0CAC-38A7-4001-9F59-FD94E7AA8D01}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3C4DBA1-A34A-4D14-B09B-175FEB8BE79A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3C4DBA1-A34A-4D14-B09B-175FEB8BE79A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3C4DBA1-A34A-4D14-B09B-175FEB8BE79A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3C4DBA1-A34A-4D14-B09B-175FEB8BE79A}.Release|Any CPU.Build.0 = Release|Any CPU
		{33545516-35B6-40CF-9AF0-1E3E83AAC994}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{33545516-35B6-40CF-9AF0-1E3E83AAC994}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{33545516-35B6-40CF-9AF0-1E3E83AAC994}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{33545516-35B6-40CF-9AF0-1E3E83AAC994}.Release|Any CPU.Build.0 = Release|Any CPU
		{5797D0A7-F06C-4098-B371-FD80DEF94CB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5797D0A7-F06C-4098-B371-FD80DEF94CB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5797D0A7-F06C-4098-B371-FD80DEF94CB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5797D0A7-F06C-4098-B371-FD80DEF94CB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.Build.0 = Release|Any CPU
		{A9A9C493-3532-4E6B-A6BE-46829172A61C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A9A9C493-3532-4E6B-A6BE-46829172A61C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A9A9C493-3532-4E6B-A6BE-46829172A61C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A9A9C493-3532-4E6B-A6BE-46829172A61C}.Release|Any CPU.Build.0 = Release|Any CPU
		{38A750EE-B7F5-4F9E-B21D-802785E18165}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{38A750EE-B7F5-4F9E-B21D-802785E18165}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{38A750EE-B7F5-4F9E-B21D-802785E18165}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{38A750EE-B7F5-4F9E-B21D-802785E18165}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{54809410-1612-4862-B5D9-E1E153394C8D} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{EE199DAA-CA4A-4654-8AC6-D8F3697C642F} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{8763C5E5-8B5D-4460-A062-CF32FE134971} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{27179954-098E-43E5-92EF-6647BD12BDAA} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{EDBBB322-4555-4E79-8B00-E6C9B9AECAF9} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{29235D36-0E61-4DDB-80BE-EF9786B3A2EB} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{64AFABB8-F3A7-4224-8C0A-74A926000129} = {FE133AB8-76CA-4C48-9FFC-55E5E886D9A9}
		{127A0CAC-38A7-4001-9F59-FD94E7AA8D01} = {FE133AB8-76CA-4C48-9FFC-55E5E886D9A9}
		{C3C4DBA1-A34A-4D14-B09B-175FEB8BE79A} = {FE133AB8-76CA-4C48-9FFC-55E5E886D9A9}
		{33545516-35B6-40CF-9AF0-1E3E83AAC994} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{5797D0A7-F06C-4098-B371-FD80DEF94CB5} = {FE133AB8-76CA-4C48-9FFC-55E5E886D9A9}
		{A9A9C493-3532-4E6B-A6BE-46829172A61C} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
		{38A750EE-B7F5-4F9E-B21D-802785E18165} = {833E8B21-0C6C-475F-B3E8-ECAA56D8E33E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A423F8C3-3424-4051-BB46-45DA0258B1BA}
	EndGlobalSection
EndGlobal
