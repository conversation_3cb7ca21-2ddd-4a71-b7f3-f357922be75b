﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Znode.Libraries.Resources.CommerceConnector_Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class CommerceConnector_Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal CommerceConnector_Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Znode.Libraries.Resources.CommerceConnector_Resources.CommerceConnector_Resources" +
                            "", typeof(CommerceConnector_Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access.
        /// </summary>
        public static string Access {
            get {
                return ResourceManager.GetString("Access", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actions.
        /// </summary>
        public static string Actions {
            get {
                return ResourceManager.GetString("Actions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Field.
        /// </summary>
        public static string AddField {
            get {
                return ResourceManager.GetString("AddField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add from Library.
        /// </summary>
        public static string AddFromLibrary {
            get {
                return ResourceManager.GetString("AddFromLibrary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New.
        /// </summary>
        public static string AddNew {
            get {
                return ResourceManager.GetString("AddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string AddSelected {
            get {
                return ResourceManager.GetString("AddSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to add selected data exchange..
        /// </summary>
        public static string AddSelectedErrorMessage {
            get {
                return ResourceManager.GetString("AddSelectedErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have successfully added the selected Data Exchange to your Exchange list..
        /// </summary>
        public static string AddSelectedSuccessMessage {
            get {
                return ResourceManager.GetString("AddSelectedSuccessMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to API Configuration.
        /// </summary>
        public static string APIConfig {
            get {
                return ResourceManager.GetString("APIConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Archive.
        /// </summary>
        public static string Archive {
            get {
                return ResourceManager.GetString("Archive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base Definition.
        /// </summary>
        public static string BaseDefinition {
            get {
                return ResourceManager.GetString("BaseDefinition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basic.
        /// </summary>
        public static string Basic {
            get {
                return ResourceManager.GetString("Basic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bearer.
        /// </summary>
        public static string Bearer {
            get {
                return ResourceManager.GetString("Bearer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collection.
        /// </summary>
        public static string Collection {
            get {
                return ResourceManager.GetString("Collection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commerce  Connector  2.0.
        /// </summary>
        public static string CommerceConnectorLogo {
            get {
                return ResourceManager.GetString("CommerceConnectorLogo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commonly Used Cron Expressions.
        /// </summary>
        public static string CommonlyUsedCronExpressions {
            get {
                return ResourceManager.GetString("CommonlyUsedCronExpressions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration Change History.
        /// </summary>
        public static string ConfigurationChangeLog {
            get {
                return ResourceManager.GetString("ConfigurationChangeLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration Set.
        /// </summary>
        public static string ConfigurationSet {
            get {
                return ResourceManager.GetString("ConfigurationSet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CONFIGURE A DATA EXCHANGE.
        /// </summary>
        public static string CONFIGUREADATAEXCHANGE {
            get {
                return ResourceManager.GetString("CONFIGUREADATAEXCHANGE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Znode. All Rights Reserved. | Version 10.0.0 -.
        /// </summary>
        public static string Copyright {
            get {
                return ResourceManager.GetString("Copyright", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cron Expression can not be longer than 100 characters..
        /// </summary>
        public static string CronMaxLengthError {
            get {
                return ResourceManager.GetString("CronMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV.
        /// </summary>
        public static string CSV {
            get {
                return ResourceManager.GetString("CSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom.
        /// </summary>
        public static string CustomLibrary {
            get {
                return ResourceManager.GetString("CustomLibrary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination.
        /// </summary>
        public static string DataDestination {
            get {
                return ResourceManager.GetString("DataDestination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Exchange.
        /// </summary>
        public static string DataExchange {
            get {
                return ResourceManager.GetString("DataExchange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Exchange Library.
        /// </summary>
        public static string DataExchangeLibrary {
            get {
                return ResourceManager.GetString("DataExchangeLibrary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Exchange List.
        /// </summary>
        public static string DataExchangeList {
            get {
                return ResourceManager.GetString("DataExchangeList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Exchange Logs.
        /// </summary>
        public static string DataExchangeLog {
            get {
                return ResourceManager.GetString("DataExchangeLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This exchange will be added to the data exchange list, and all exchanges must have a different name..
        /// </summary>
        public static string DataExchangePopupNote {
            get {
                return ResourceManager.GetString("DataExchangePopupNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Exchange - Add Exchange Name.
        /// </summary>
        public static string DataExchangePopupTitle {
            get {
                return ResourceManager.GetString("DataExchangePopupTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source.
        /// </summary>
        public static string DataSource {
            get {
                return ResourceManager.GetString("DataSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DELETE.
        /// </summary>
        public static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string DeleteAction {
            get {
                return ResourceManager.GetString("DeleteAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to delete data exchange..
        /// </summary>
        public static string DeleteTaskSchedularErrorMessage {
            get {
                return ResourceManager.GetString("DeleteTaskSchedularErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data exchange deleted successfully..
        /// </summary>
        public static string DeleteTaskSchedularSuccessMessage {
            get {
                return ResourceManager.GetString("DeleteTaskSchedularSuccessMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        public static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download.
        /// </summary>
        public static string Download {
            get {
                return ResourceManager.GetString("Download", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download Sample YML/YAML File.
        /// </summary>
        public static string DownloadSampleYMLYAMLFile {
            get {
                return ResourceManager.GetString("DownloadSampleYMLYAMLFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        public static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Message.
        /// </summary>
        public static string ErrorMessage {
            get {
                return ResourceManager.GetString("ErrorMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Run Date may not be in the past..
        /// </summary>
        public static string ErrorStartDateGreaterThan {
            get {
                return ResourceManager.GetString("ErrorStartDateGreaterThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run time cannot be earlier than current time..
        /// </summary>
        public static string ErrorStartTimeEarlierThanNow {
            get {
                return ResourceManager.GetString("ErrorStartTimeEarlierThanNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to An error occurred while processing your request..
        /// </summary>
        public static string ErrorWhileProcessingRequest {
            get {
                return ResourceManager.GetString("ErrorWhileProcessingRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Name.
        /// </summary>
        public static string ExchangeName {
            get {
                return ResourceManager.GetString("ExchangeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Exchange is added with same name. Please enter a different Exchange Name..
        /// </summary>
        public static string ExchangeNameExistsError {
            get {
                return ResourceManager.GetString("ExchangeNameExistsError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange name is required..
        /// </summary>
        public static string ExchangeNameRequiredError {
            get {
                return ResourceManager.GetString("ExchangeNameRequiredError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Format.
        /// </summary>
        public static string Format {
            get {
                return ResourceManager.GetString("Format", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GET.
        /// </summary>
        public static string Get {
            get {
                return ResourceManager.GetString("Get", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Header Parameter.
        /// </summary>
        public static string HeaderParams {
            get {
                return ResourceManager.GetString("HeaderParams", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTTPHandler.
        /// </summary>
        public static string HTTPHandler {
            get {
                return ResourceManager.GetString("HTTPHandler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTTPSHandler.
        /// </summary>
        public static string HTTPSHandler {
            get {
                return ResourceManager.GetString("HTTPSHandler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Input/Edit YAML.
        /// </summary>
        public static string InputEditYAML {
            get {
                return ResourceManager.GetString("InputEditYAML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter a valid cron expression..
        /// </summary>
        public static string InvalidCronExpressionError {
            get {
                return ResourceManager.GetString("InvalidCronExpressionError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to JSON.
        /// </summary>
        public static string JSON {
            get {
                return ResourceManager.GetString("JSON", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Key.
        /// </summary>
        public static string Key {
            get {
                return ResourceManager.GetString("Key", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        public static string LabelBack {
            get {
                return ResourceManager.GetString("LabelBack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base Definition Details.
        /// </summary>
        public static string LabelBaseDefinitionDetails {
            get {
                return ResourceManager.GetString("LabelBaseDefinitionDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cron Expression.
        /// </summary>
        public static string LabelCronExpression {
            get {
                return ResourceManager.GetString("LabelCronExpression", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV Column Data.
        /// </summary>
        public static string LabelCSVColumnData {
            get {
                return ResourceManager.GetString("LabelCSVColumnData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV Column Name.
        /// </summary>
        public static string LabelCSVColumnName {
            get {
                return ResourceManager.GetString("LabelCSVColumnName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string LabelCustomProcessingNotRequired {
            get {
                return ResourceManager.GetString("LabelCustomProcessingNotRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string LabelCustomProcessingRequired {
            get {
                return ResourceManager.GetString("LabelCustomProcessingRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        public static string LabelDisable {
            get {
                return ResourceManager.GetString("LabelDisable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        public static string LabelEnable {
            get {
                return ResourceManager.GetString("LabelEnable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable.
        /// </summary>
        public static string LabelEnableStatus {
            get {
                return ResourceManager.GetString("LabelEnableStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Date.
        /// </summary>
        public static string LabelEndDate {
            get {
                return ResourceManager.GetString("LabelEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Description.
        /// </summary>
        public static string LabelErrorDescription {
            get {
                return ResourceManager.GetString("LabelErrorDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed Records.
        /// </summary>
        public static string LabelFailedRecords {
            get {
                return ResourceManager.GetString("LabelFailedRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frequency.
        /// </summary>
        public static string LabelFrequency {
            get {
                return ResourceManager.GetString("LabelFrequency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Name.
        /// </summary>
        public static string LabelImportName {
            get {
                return ResourceManager.GetString("LabelImportName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Id.
        /// </summary>
        public static string LabelImportProcessLogId {
            get {
                return ResourceManager.GetString("LabelImportProcessLogId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Custom Processing Required.
        /// </summary>
        public static string LabelIsCustomProcessingRequired {
            get {
                return ResourceManager.GetString("LabelIsCustomProcessingRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        public static string LabelLogId {
            get {
                return ResourceManager.GetString("LabelLogId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to One-Time.
        /// </summary>
        public static string LabelOneTime {
            get {
                return ResourceManager.GetString("LabelOneTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recurring.
        /// </summary>
        public static string LabelRecurring {
            get {
                return ResourceManager.GetString("LabelRecurring", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Row Number.
        /// </summary>
        public static string LabelRowNumber {
            get {
                return ResourceManager.GetString("LabelRowNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run Date.
        /// </summary>
        public static string LabelRunDate {
            get {
                return ResourceManager.GetString("LabelRunDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On-Demand.
        /// </summary>
        public static string LabelRunOnDemand {
            get {
                return ResourceManager.GetString("LabelRunOnDemand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Real-Time.
        /// </summary>
        public static string LabelRunOnRealTime {
            get {
                return ResourceManager.GetString("LabelRunOnRealTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduled.
        /// </summary>
        public static string LabelRunOnSchedule {
            get {
                return ResourceManager.GetString("LabelRunOnSchedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run Time.
        /// </summary>
        public static string LabelRunTime {
            get {
                return ResourceManager.GetString("LabelRunTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Configuration.
        /// </summary>
        public static string LabelScheduleConfiguration {
            get {
                return ResourceManager.GetString("LabelScheduleConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Name.
        /// </summary>
        public static string LabelScheduleName {
            get {
                return ResourceManager.GetString("LabelScheduleName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Settings.
        /// </summary>
        public static string LabelScheduleSettings {
            get {
                return ResourceManager.GetString("LabelScheduleSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Type.
        /// </summary>
        public static string LabelScheduleType {
            get {
                return ResourceManager.GetString("LabelScheduleType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        public static string LabelStartDate {
            get {
                return ResourceManager.GetString("LabelStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Succeeded Records.
        /// </summary>
        public static string LabelSucceededRecords {
            get {
                return ResourceManager.GetString("LabelSucceededRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Template Id.
        /// </summary>
        public static string LabelTemplateId {
            get {
                return ResourceManager.GetString("LabelTemplateId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Template Name.
        /// </summary>
        public static string LabelTemplateName {
            get {
                return ResourceManager.GetString("LabelTemplateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Connectivity.
        /// </summary>
        public static string LabelTestConnectivity {
            get {
                return ResourceManager.GetString("LabelTestConnectivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Processed Records.
        /// </summary>
        public static string LabelTotalProcessedRecords {
            get {
                return ResourceManager.GetString("LabelTotalProcessedRecords", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View a Data Exchange.
        /// </summary>
        public static string LabelViewDataExchange {
            get {
                return ResourceManager.GetString("LabelViewDataExchange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Znode Import Logs.
        /// </summary>
        public static string LabelZnodeImportLog {
            get {
                return ResourceManager.GetString("LabelZnodeImportLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Run Result.
        /// </summary>
        public static string LastRunResult {
            get {
                return ResourceManager.GetString("LastRunResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Run Time.
        /// </summary>
        public static string LastRunTime {
            get {
                return ResourceManager.GetString("LastRunTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Library.
        /// </summary>
        public static string Library {
            get {
                return ResourceManager.GetString("Library", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Data Exchange.
        /// </summary>
        public static string ManageDataExchange {
            get {
                return ResourceManager.GetString("ManageDataExchange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Method.
        /// </summary>
        public static string MethodName {
            get {
                return ResourceManager.GetString("MethodName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Data Found.
        /// </summary>
        public static string NoDataFound {
            get {
                return ResourceManager.GetString("NoDataFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No records found..
        /// </summary>
        public static string NoRecordsFound {
            get {
                return ResourceManager.GetString("NoRecordsFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are not authorized to access this page..
        /// </summary>
        public static string NotAuthorizedPage {
            get {
                return ResourceManager.GetString("NotAuthorizedPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OAuth 2.0.
        /// </summary>
        public static string OAuth {
            get {
                return ResourceManager.GetString("OAuth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oops!.
        /// </summary>
        public static string Oops {
            get {
                return ResourceManager.GetString("Oops", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A page you are accessing is not available..
        /// </summary>
        public static string PageNotAvailable {
            get {
                return ResourceManager.GetString("PageNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 404 Error.
        /// </summary>
        public static string PageNotFoundError {
            get {
                return ResourceManager.GetString("PageNotFoundError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PATCH.
        /// </summary>
        public static string Patch {
            get {
                return ResourceManager.GetString("Patch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POST.
        /// </summary>
        public static string Post {
            get {
                return ResourceManager.GetString("Post", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Exchange Log Details.
        /// </summary>
        public static string ProcessingLogDetails {
            get {
                return ResourceManager.GetString("ProcessingLogDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processor File Name.
        /// </summary>
        public static string ProcessorFileName {
            get {
                return ResourceManager.GetString("ProcessorFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PUT.
        /// </summary>
        public static string Put {
            get {
                return ResourceManager.GetString("Put", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query Parameter.
        /// </summary>
        public static string QueryParams {
            get {
                return ResourceManager.GetString("QueryParams", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run Date is required..
        /// </summary>
        public static string ReqRunDateError {
            get {
                return ResourceManager.GetString("ReqRunDateError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run Time is required..
        /// </summary>
        public static string ReqRunTimeError {
            get {
                return ResourceManager.GetString("ReqRunTimeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Name is required..
        /// </summary>
        public static string ReqSchedulerNameError {
            get {
                return ResourceManager.GetString("ReqSchedulerNameError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Type is required..
        /// </summary>
        public static string ReqSchedulerTypeError {
            get {
                return ResourceManager.GetString("ReqSchedulerTypeError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        public static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule.
        /// </summary>
        public static string Schedule {
            get {
                return ResourceManager.GetString("Schedule", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Name already exists..
        /// </summary>
        public static string SchedulerNameExists {
            get {
                return ResourceManager.GetString("SchedulerNameExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduler Name can not be longer than 100 characters..
        /// </summary>
        public static string SchedulerNameMaxLengthError {
            get {
                return ResourceManager.GetString("SchedulerNameMaxLengthError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- Select --.
        /// </summary>
        public static string SelectAction {
            get {
                return ResourceManager.GetString("SelectAction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- Select --.
        /// </summary>
        public static string SelectAPIMethod {
            get {
                return ResourceManager.GetString("SelectAPIMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- Select --.
        /// </summary>
        public static string SelectAuthType {
            get {
                return ResourceManager.GetString("SelectAuthType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to -- Select --.
        /// </summary>
        public static string SelectSourceFileFormat {
            get {
                return ResourceManager.GetString("SelectSourceFileFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SFTP Configuration.
        /// </summary>
        public static string SFTPConfig {
            get {
                return ResourceManager.GetString("SFTPConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SFTPHandler.
        /// </summary>
        public static string SFTPHandler {
            get {
                return ResourceManager.GetString("SFTPHandler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        public static string Show {
            get {
                return ResourceManager.GetString("Show", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Note: Single Data Exchange can be added at a time..
        /// </summary>
        public static string SingleDataExchange {
            get {
                return ResourceManager.GetString("SingleDataExchange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Something went wrong..
        /// </summary>
        public static string SomethingWrong {
            get {
                return ResourceManager.GetString("SomethingWrong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activation.
        /// </summary>
        public static string StatusActivation {
            get {
                return ResourceManager.GetString("StatusActivation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string Tags {
            get {
                return ResourceManager.GetString("Tags", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transmission Confguration.
        /// </summary>
        public static string TransmissionConfguration {
            get {
                return ResourceManager.GetString("TransmissionConfguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Configuration.
        /// </summary>
        public static string TransmissionDestination {
            get {
                return ResourceManager.GetString("TransmissionDestination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source Configuration.
        /// </summary>
        public static string TransmissionSource {
            get {
                return ResourceManager.GetString("TransmissionSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trigger Origin.
        /// </summary>
        public static string TriggerOrigin {
            get {
                return ResourceManager.GetString("TriggerOrigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please try again later..
        /// </summary>
        public static string TryAgainLater {
            get {
                return ResourceManager.GetString("TryAgainLater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Mapping.
        /// </summary>
        public static string UpdateMapping {
            get {
                return ResourceManager.GetString("UpdateMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload.
        /// </summary>
        public static string Upload {
            get {
                return ResourceManager.GetString("Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Your YML/YAML File.
        /// </summary>
        public static string UploadYourYMLYAMLFile {
            get {
                return ResourceManager.GetString("UploadYourYMLYAMLFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        public static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string Version {
            get {
                return ResourceManager.GetString("Version", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to What is a Cron Expression?.
        /// </summary>
        public static string WhatIsCronExpression {
            get {
                return ResourceManager.GetString("WhatIsCronExpression", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to XML.
        /// </summary>
        public static string XML {
            get {
                return ResourceManager.GetString("XML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ZnodeExportHandler.
        /// </summary>
        public static string ZnodeExportHandler {
            get {
                return ResourceManager.GetString("ZnodeExportHandler", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Znode Import Log Details.
        /// </summary>
        public static string ZnodeImportLogDetails {
            get {
                return ResourceManager.GetString("ZnodeImportLogDetails", resourceCulture);
            }
        }
    }
}
