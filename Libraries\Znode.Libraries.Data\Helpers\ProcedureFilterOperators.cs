﻿namespace Znode.Libraries.Data.Helpers
{
    public struct ProcedureFilterOperators
    {
        public static string Contains { get; } = "cn";
        public static new string Equals { get; } = "eq";
        public static string EndsWith { get; } = "ew";
        public static string GreaterThan { get; } = "gt";
        public static string GreaterThanOrEqual { get; } = "ge";
        public static string LessThan { get; } = "lt";
        public static string LessThanOrEqual { get; } = "le";
        public static string NotEquals { get; } = "ne";
        public static string StartsWith { get; } = "sw";
        public static string Like { get; } = "lk";
        public static string Is { get; } = "is";
        public static string In { get; } = "in";
        public static string NotIn { get; } = "not in";
        public static string Between { get; } = "bw";
    }

    public struct StoredProcedureKeys
    {
        //Operatod
        public const string AND = "and";
        public const string OR = "or";
        public const string TildOperator = "";
        public const string TildEqualToOperator = "";

        //columns
        //public const string ProductTypeId = "producttypeid";
        //public const string ProductId = "productid";
        //public const string PortalId = "portalId";
        //public const string Portalid = "portalid";
        //public const string AccountId = "accountid";
        //public const string RoleName = "rolename";
        //public const string CategoryId = "categoryid";
        //public const string SkuId = "skuId";

        ////Condition
        //public const string RoleNameIsNull = "~~rolename~~ is null";
        //public const string RoleNameIsNullWithAnd = " and ~~rolename~~ is null";
        //public const string GetAccountId = "~~accountid~~={0}";
        //public const string FranchiseRole = "FRANCHISE<br>";
        //public const string VendorRole = "VENDOR<br>";
        //public const string RejectionMessageFilterCondition = "~~PortalId~~={0} and ~~LocaleId~~={1}";
        ////Order By 
        //public const string sortKey = "sort";
        //public const string sortDirKey = "sortDir";
        //public const string defaultSortKey = "displayorder asc";
    }
}
