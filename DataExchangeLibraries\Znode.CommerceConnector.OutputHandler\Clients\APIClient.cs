﻿using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Diagnostics;
using System.Net;
using System.Text.RegularExpressions;
using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Model.Models;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.OutputHandler
{
    public class APIClient : IAPIClient
    {
        public dynamic SendData<T>(int erpId, int logId, HTTPConfigurationModel httpConfigurationModel, string data, dynamic requestBody, WebHeaderCollection headers, dynamic inputModel, bool isRealTime, bool returnWithoutDeserialize = false)
        {
            try
            {
                MongoLogging.LogMessage(Constants.SendData, Constants.SendData, TraceLevel.Info);
                if (HelperUtility.IsNotNull(inputModel))
                {
                    httpConfigurationModel = CheckParams(erpId, logId,httpConfigurationModel, inputModel);
                }
                string endpoint = httpConfigurationModel.Endpoint.Trim();

                try
                {
                    ApiClient apiClient = new ApiClient();
                    RequestModel requestModel = apiClient.SetRequestModel(endpoint);
                    if (HelperUtility.IsNotNull(headers))
                    {
                        var headerParameter = isRealTime? HelperMethods.GetHeaderParameter(httpConfigurationModel.APIConfigurationId, Constants.Source) : HelperMethods.GetHeaderParameter(httpConfigurationModel.APIConfigurationId, Constants.Destination);
                        foreach (var item in headerParameter)
                        {
                            if (!headers.AllKeys.Contains(item.Key))
                            {
                                headers.Add(item.Key, item.Value);
                            }
                        }
                        requestModel.HeaderCollection = headers;
                    }
                    if (requestBody is not NullableRequestBody)
                    {
                        requestModel.RequestBody = JsonConvert.SerializeObject(requestBody);
                    }
                    dynamic apiResponse = "";
                    string statusCode = "";
                    if (httpConfigurationModel.APIAction == Constants.GET)
                    {
                        apiResponse = apiClient.GetRequest<dynamic>(requestModel, out statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.PUT)
                    {
                        apiResponse = apiClient.PutRequest<dynamic>(requestModel, out statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.POST)
                    {
                        apiResponse = apiClient.PostRequest<dynamic>(requestModel, out statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.DELETE)
                    {
                        apiResponse = apiClient.DeleteRequest<dynamic>(requestModel, out statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.PATCH)
                    {
                        apiResponse = apiClient.PatchRequest<dynamic>(requestModel, out statusCode);
                    }

                    var responseData = JsonConvert.DeserializeObject<dynamic>(apiResponse.ToString());
                    dynamic combinedResponse = new ApiResponse
                    {
                        Data = JsonConvert.SerializeObject(responseData),
                        StatusCode = statusCode
                    };
                    //
                    return combinedResponse;

                    //dynamic result = JsonConvert.SerializeObject(apiResponse);
                    //return result;
                }
                catch (Exception ex)
                {
                    MongoLogging.LogMessage(Constants.SendData + ex.Message, Constants.SendData, TraceLevel.Error);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.SendData + ex.Message);
                    if (!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.SendData, TraceLevel.Error); 
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.SendData + ex.Message, Constants.SendData, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.SendData + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.SendData, TraceLevel.Error); 
                return false;
            }
        }

        public dynamic CheckParams(int erpId, int logId, HTTPConfigurationModel httpConfigurationModel, dynamic inputModel)
        {
            try
            {
                MongoLogging.LogMessage(Constants.CheckParamsMethodCalled, Constants.CheckParamsMethod, TraceLevel.Error);
                string url = httpConfigurationModel.Endpoint;
                JObject firstItem = null;
                JToken parsedInput = JToken.Parse(inputModel.ToString());

                if (parsedInput.Type == JTokenType.Array)
                {
                    firstItem = (JObject)parsedInput.First;
                }
                else if (parsedInput.Type == JTokenType.Object)
                {
                    firstItem = (JObject)parsedInput;
                }

                var matches = Regex.Matches(url, @"\{(.*?)\}");
                foreach (Match match in matches)
                {
                    string key = match.Groups[1].Value;
                    if (firstItem?.ContainsKey(key) ?? false)
                    {
                        string value = Uri.EscapeDataString(firstItem[key].ToString());
                        url = url.Replace(match.Value, value);
                    }
                }
                httpConfigurationModel.Endpoint = url;
                return httpConfigurationModel;
            }
            catch(Exception ex)
            {
                MongoLogging.LogMessage(Constants.CheckParamsMethodFailed + ex.Message, Constants.CheckParamsMethod, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.CheckParamsMethodFailed + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.CheckParamsMethod, TraceLevel.Error); 
                return httpConfigurationModel;
            }
        }

        //Get Bearer Token
        public dynamic GetBearerToken(HTTPConfigurationModel httpConfigurationModel)
        {
            BearerTokenModel bearerTokenModel = new BearerTokenModel();
            var keyValues = new List<KeyValuePair<string, string>>()
            {
                new KeyValuePair<string, string>(Constants.client_id,httpConfigurationModel.ClientId),
                new KeyValuePair<string, string>(Constants.client_secret, httpConfigurationModel.ClientSecret),
                new KeyValuePair<string, string>(Constants.grant_type, httpConfigurationModel.GrantType),
                new KeyValuePair<string, string>(Constants.scope, httpConfigurationModel.Scope)
            };

            ApiClient apiClient = new ApiClient();
            RequestModel requestModel = apiClient.SetRequestModel(httpConfigurationModel.AccessTokenURL);
            requestModel.RequestBody = string.Join("&", keyValues.Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value ?? "")}"));
            requestModel.ContentType = "application/x-www-form-urlencoded";
            TokenResponseModel apiResponse = apiClient.PostRequest<TokenResponseModel>(requestModel, out string statusCode);
            return apiResponse?.access_token;
        }
    }
}
