﻿using Newtonsoft.Json;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client.Clients
{
    public class ManageDataExchangClient : BaseClient,IManageDataExchangClient
    {
        public BaseDefinitionModel GetDataExchangeDetailsByID(int id)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetDataExchangeDetails(id);
            BaseDefinitionModel listModel = apiClient.GetRequest<BaseDefinitionModel>(endpoint);
            return listModel;
        }

        public bool SaveDataExchangeDetails(BaseDefinitionModel model)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.SaveDataExchangeDetails();
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(model);
            requestModel.RequestType = "POST";
            TrueFalseResponse response = apiClient.PostRequest<TrueFalseResponse>(requestModel, out string statusCode);
            return response.IsSuccess;
        }

        public BaseDefinitionModel GetBaseDefinitionDetailsByID(int erpId)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetBaseDefinitionDetailsByID(erpId);
            BaseDefinitionModel listModel = apiClient.GetRequest<BaseDefinitionModel>(endpoint);
            return listModel;
        }

        public bool TestConnection(string mode, string request)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.TestConnection(mode, request);
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(request);
            requestModel.RequestType = "POST";
            TrueFalseResponse response = apiClient.PostRequest<TrueFalseResponse>(requestModel, out string statusCode);
            return response.IsSuccess;
        }

        public ConfigurationChangeLogListModel GetConfigurationChangeList(int id, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetConfigurationChangeList(id);
            endpoint += BuildEndpointQueryString(null, filters, sorts, pageIndex, pageSize);
            ConfigurationChangeLogListModel standardDataExchangeListModel = apiClient.GetRequest<ConfigurationChangeLogListModel>(endpoint);
            return standardDataExchangeListModel;
        }

        public bool ValidateSchedulerName(string schedulerName)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.ValidateSchedulerName(schedulerName);
            TrueFalseResponse response = apiClient.GetRequest<TrueFalseResponse>(endpoint);
            return response.IsSuccess;
        }
    }
}
