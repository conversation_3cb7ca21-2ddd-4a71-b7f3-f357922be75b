﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.OutputHandler.Handlers
{
    public class FTPHandler : IFTPHandler
    {
        IFTPClient _FTPClient;

        public FTPHandler(IFTPClient FTPClient)
        {
            _FTPClient = FTPClient;
        }

        public bool FTPOutputDataHandler(string csv, int erpId, ProcessorDetails processorDetails)
        {
            MongoLogging.LogMessage("FTPOutputDataHandler method in FTPHandler called", "FTPHandler", TraceLevel.Info);
            FTPConfigurationModel FTPConfigurationModel = GetOutputHandlerFTPCredentials(erpId);
            bool result = _FTPClient.UploadData<bool>(FTPConfigurationModel, csv, false);
            bool recordsUpdated = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, csv, result);
            if (!recordsUpdated)
                MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.FTPOutputDataHandler, TraceLevel.Error);
            return result;
        }

        public FTPConfigurationModel GetOutputHandlerFTPCredentials(int erpId)
        {
            FTPConfigurationModel FTPConfigurationModel = GetFTPCredentials(erpId);
            return FTPConfigurationModel;
        }

        public FTPConfigurationModel GetFTPCredentials(int erpId)
        {
            MongoLogging.LogMessage("GetFTPCredentials method in FTPHandler called", "FTPHandler", TraceLevel.Info);
            List<FTPConfigurationModel> FTPCredentials = new List<FTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Destination));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
            List<FTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<FTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }
    }
}
