﻿using Microsoft.AspNetCore.Mvc;
using System.Web.Http.Description;
using Znode.CommerceConnector.API.Services;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Model.Models;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API
{
    [ApiController]
    [Route("DataExchangeLibrary")]
    public class DataExchangeLibraryController : BaseController
    {
        private readonly IDataExchangeLibraryService _dataExchangeLibraryService;

        public DataExchangeLibraryController(IDataExchangeLibraryService dataExchangeLibraryService)
        {
            _dataExchangeLibraryService = dataExchangeLibraryService;
        }

        [Route("GetDataExchangeLibraryList")]
        [HttpGet]
        [ResponseType(typeof(DataExchangeListModel))]
        public IActionResult GetDataExchangeLibraryList()
        {
            IActionResult response;
            try
            {
                DataExchangeListModel data = _dataExchangeLibraryService.GetDataExchangeLibraryList(Filters, Sorts, Page);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        [Route("GetDataExchangeList")]
        [HttpGet]
        [ResponseType(typeof(StandardDataExchangeListModel))]
        public IActionResult GetDataExchangeList()
        {
            IActionResult response;
            try
            {
                StandardDataExchangeListModel data = _dataExchangeLibraryService.GetDataExchangeList(Filters, Sorts, Page);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        [Route("AddSelectedExchange")]
        [HttpPost]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult AddSelectedExchange([FromBody] AddSelectedExchangeModel addSelectedExchangeModel)
        {
            try
            {
                bool result = _dataExchangeLibraryService.AddSelectedExchange(addSelectedExchangeModel);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });

            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }
        
        [Route("DeleteDataExchangeSchedular")]
        [HttpPost]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult DeleteDataExchangeSchedular(int erpId)
        {
            try
            {
                bool result = _dataExchangeLibraryService.DeleteDataExchangeSchedular(erpId);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });

            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }


        [Route("DeleteDataExchangeDetails")]
        [HttpDelete]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult DeleteDataExchangeDetails(BaseDefinitionModel model)
        {
            try
            {
                bool result = _dataExchangeLibraryService.DeleteDataExchangeSchedular(model.ERPBaseDefinitionId);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });

            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }

        [Route("GetGenericLogList")]
        [HttpGet]
        [ResponseType(typeof(ProcessorLogListResponse))]
        public IActionResult GetGenericLogList()
        {
            IActionResult response;
            try
            {
                ProcessorLogListModel data = _dataExchangeLibraryService.GetGenericLogList(Filters, Sorts, Page);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }



        [Route("GetGenericLogDetails/{logId}")]
        [HttpGet]
        [ResponseType(typeof(ProcessingLogDetailListModel))]
        public IActionResult GetGenericLogDetails(int logId)
        {
            IActionResult response;
            try
            {
                ProcessingLogDetailListModel data = _dataExchangeLibraryService.GetGenericLogDetails(Filters, Sorts, Page, logId);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        /// <summary>
        /// Validate whether Exchange name exists or not in Base Definition. 
        /// </summary>
        /// <param name="exchangeName"></param>
        /// <returns>Will return true if Exchange name is proper and not exists.</returns>
        [Route("ValidateExchangeName/{exchangeName}")]
        [HttpGet]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult ValidateExchangeName(string exchangeName)
        {
            try
            {
                bool result = _dataExchangeLibraryService.ValidateExchangeName(exchangeName);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });
            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }
    }
}
