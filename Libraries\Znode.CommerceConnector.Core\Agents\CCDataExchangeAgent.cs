﻿using AutoMapper;
using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Core.Agents
{
    public class CCDataExchangeAgent : ICCDataExchangeAgent
    {
        private readonly IGetDataExchangeClient _dataExchangeClient;
        private readonly IMapper _mapper;
        private readonly string znodeImportEndPoint = $"{BaseEndpoint.ZnodeApi}/v2/imports/";

        public CCDataExchangeAgent(IGetDataExchangeClient dataExchangeClient, IMapper mapper)
        {
            _dataExchangeClient = dataExchangeClient;
            _mapper = mapper;
        }

        public StandardDataExchangesListViewModel GetStandardDataExchangeList(GridModel gridModel)
        {
            FilterCollection filter = new FilterCollection();
            if (!string.IsNullOrWhiteSpace(gridModel?.GlobalSearch))
            {
                filter.Add(new FilterTuple(CommerceConnectorConstants.Name, CommerceConnectorConstants.Contains, gridModel.GlobalSearch));
            }

            Dictionary<string, string> sorts = new();
            if (!(string.IsNullOrWhiteSpace(gridModel?.SortDirection) && string.IsNullOrWhiteSpace(gridModel?.SortField)))
            {
                if (gridModel.SortField?.ToLower() == CommerceConnectorConstants.ExchangeName.ToLower())
                    gridModel.SortField = CommerceConnectorConstants.Name.ToLower();
                sorts.Add(gridModel.SortField, gridModel.SortDirection);
            }

            StandardDataExchangeListModel list = _dataExchangeClient.GetDataExchangeList(filter, sorts, gridModel?.Page, gridModel?.PageSize);
            StandardDataExchangesListViewModel listViewModel = new StandardDataExchangesListViewModel
            {
                DataExchangeList = _mapper.Map<List<StandardDataExchangesViewModel>>(list?.DataModelList),
                TotalRows = list.TotalRows
            };
            return listViewModel;
        }

        public bool DeleteDataExchangeSchedular(int erpId)
        {
            bool result = _dataExchangeClient.DeleteDataExchangeSchedular(erpId);
            return result;
        }

        public bool TriggerDataExchangeSchedular(int erpId)
        {
            bool result = _dataExchangeClient.TriggerDataExchangeSchedular(erpId);
            return result;
        }

        public dynamic RealTimeDataScheduler(dynamic requestBody, int erpId)
        {
            dynamic result = _dataExchangeClient.RealTimeDataScheduler(requestBody, erpId);
            return result;
        }

        public bool EnableDisableDataExchangeScheduler(int erpId, bool isActive)
        {
            bool result = _dataExchangeClient.EnableDisableDataExchangeScheduler(erpId, isActive);
            return result;
        }


        public ProcessingLogListViewModel GetProcessingLogList(LogListRequestModel logListRequest)
        {
            FilterCollection filters = new FilterCollection();
            if (!string.IsNullOrWhiteSpace(logListRequest?.GridModel?.GlobalSearch))
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.TemplateName))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.TemplateName);
                filters.Add(new FilterTuple(CommerceConnectorConstants.TemplateName, CommerceConnectorConstants.Contains, logListRequest?.GridModel.GlobalSearch));
            }

            if (!string.IsNullOrWhiteSpace(logListRequest?.ExchangeName))
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.TemplateName))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.TemplateName);
                filters.Add(new FilterTuple(CommerceConnectorConstants.TemplateName, CommerceConnectorConstants.Contains, logListRequest.ExchangeName));
            }
            if (logListRequest.ERPBaseDefinitionId > 0)
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.ERPBaseDefinitionId))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.ERPBaseDefinitionId);
                filters.Add(new FilterTuple(CommerceConnectorConstants.ERPBaseDefinitionId, CommerceConnectorConstants.Contains, logListRequest?.ERPBaseDefinitionId.ToString()));
            }

            Dictionary<string, string> sorts = new();
            if (!(string.IsNullOrWhiteSpace(logListRequest?.GridModel?.SortDirection) && string.IsNullOrWhiteSpace(logListRequest?.GridModel?.SortField)))
            {
                sorts.Add(logListRequest?.GridModel.SortField, logListRequest?.GridModel.SortDirection);
            }

            ProcessorLogListResponse processorLogList = _dataExchangeClient.GetGenericLogList(filters, sorts, logListRequest?.GridModel?.Page, logListRequest?.GridModel?.PageSize);
            return new ProcessingLogListViewModel
            {
                ProcessingLogList = _mapper.Map<List<ProcessingLogsViewModel>>(processorLogList?.ProcessorLogs),
                TotalRows = processorLogList?.TotalRecords ?? 0
            };
        }

        public ProcessingLogListViewModel GetZnodeImportLogList(GridModel gridModel, string exchangeName)
        {
            FilterCollection filters = new FilterCollection();
            if (!string.IsNullOrWhiteSpace(gridModel?.GlobalSearch))
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.TemplateName))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.TemplateName);
                filters.Add(new FilterTuple(CommerceConnectorConstants.TemplateName, CommerceConnectorConstants.Contains, gridModel.GlobalSearch));
            }

            if (!string.IsNullOrWhiteSpace(exchangeName))
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.TemplateName))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.TemplateName);
                filters.Add(new FilterTuple(CommerceConnectorConstants.TemplateName, CommerceConnectorConstants.Contains, exchangeName));
            }

            Dictionary<string, string> sorts = new();
            if (!(string.IsNullOrWhiteSpace(gridModel?.SortDirection) && string.IsNullOrWhiteSpace(gridModel?.SortField)))
            {
                sorts.Add(gridModel.SortField, gridModel.SortDirection);
            }

            ImportLogsListResponse list = _dataExchangeClient.GetProcessingLogList(filters, sorts, gridModel?.Page, gridModel?.PageSize);
            return new ProcessingLogListViewModel
            {
                ProcessingLogList = _mapper.Map<List<ProcessingLogsViewModel>>(list?.LogsList),
                TotalRows = list?.TotalResults ?? 0
            };
        }


        public int CreateEditBaseDataExchange(BaseDefinitionViewModel viewModel)
        {
            BaseDefinitionModel model = new BaseDefinitionModel();
            model = _mapper.Map<BaseDefinitionModel>(viewModel);
            model.Library = CCAdminConstant.LibraryCustom;
            model.StatusActivationModel = _mapper.Map<StatusActivationModel>(viewModel.StatusActivationViewModel);
            int erpId = _dataExchangeClient.CreateEditBaseDataExchange(model);
            return erpId;
        }

        public LogDetailsListViewModel GetProcessingLogDetails(GridModel gridModel, int logId)
        {
            FilterCollection filters = new FilterCollection();
            if (!string.IsNullOrWhiteSpace(gridModel?.GlobalSearch))
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.ColumnName))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.ColumnName);
                // Need to change as per search columns
                filters.Add(new FilterTuple(CommerceConnectorConstants.ColumnName, CommerceConnectorConstants.Equal, gridModel.GlobalSearch));
            }

            Dictionary<string, string> sorts = new();
            if (!(string.IsNullOrWhiteSpace(gridModel?.SortDirection) && string.IsNullOrWhiteSpace(gridModel?.SortField)))
            {
                sorts.Add(gridModel.SortField, gridModel.SortDirection);
            }

            ImportLogDetailsListResponse list = _dataExchangeClient.GetProcessingLogDetails(logId, filters, sorts, gridModel?.Page, gridModel?.PageSize);
            LogDetailsListViewModel listViewModel = new LogDetailsListViewModel
            {
                LogDetailsList = _mapper.Map<List<LogDetailsViewModel>>(list?.LogDetailsList),
                ProcessingLogs = _mapper.Map<ProcessingLogsViewModel>(list?.ImportLogs),
                TotalRows = list.TotalResults
            };

            return listViewModel;
        }

        public ProcessingLogDetailListViewModel GetGenericLogDetails(GridModel gridModel, int logId)
        {
            FilterCollection filters = new FilterCollection();
            if (!string.IsNullOrWhiteSpace(gridModel?.GlobalSearch))
            {
                if (filters.Exists(x => x.Item1 == CommerceConnectorConstants.ImportLogMessage))
                    filters.RemoveAll(x => x.FilterName == CommerceConnectorConstants.ImportLogMessage);
                filters.Add(new FilterTuple(CommerceConnectorConstants.ImportLogMessage, CommerceConnectorConstants.Contains, gridModel.GlobalSearch));
            }

            Dictionary<string, string> sorts = new();
            if (!(string.IsNullOrWhiteSpace(gridModel?.SortDirection) && string.IsNullOrWhiteSpace(gridModel?.SortField)))
            {
                sorts.Add(gridModel.SortField, gridModel.SortDirection);
            }

            ProcessingLogDetailListModel processorLogList = _dataExchangeClient.GetGenericLogDetails(logId, filters, sorts, gridModel?.Page, gridModel?.PageSize);
            ProcessingLogDetailListViewModel listViewModel = new ProcessingLogDetailListViewModel()
            {
                ProcessingLogDetails = _mapper.Map<List<ProcessingLogDetailViewModel>>(processorLogList?.ProcessingLogDetails),
                TotalRecords = processorLogList?.TotalRecords ?? 0
            };

            return listViewModel;
        }

    }
}
