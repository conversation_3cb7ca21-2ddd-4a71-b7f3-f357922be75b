﻿using System.Net;

using Znode.CommerceConnector.Model;

namespace Znode.CommerceConnector.InputHandler
{
    public interface IInputHandlerInitializer
    {
        dynamic InputHandler(int erpId, int logId, dynamic requestBody, WebHeaderCollection headerCollection, dynamic inputModel);
        dynamic GetYAMLDataHandler(int erpId);
        HTTPConfigurationModel GetInputHandlerHTTPCredentials(int erpId);
    }
}
