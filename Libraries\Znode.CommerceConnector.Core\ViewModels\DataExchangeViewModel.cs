﻿using System.ComponentModel.DataAnnotations;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class DataExchangeViewModel 
    {
        public int DataExchangeLibraryId { get; set; }
        [Display(Name = "Exchange Name")]
        public string ExchangeName { get; set; }
        public string Version { get; set; }
        public string Tags { get; set; }
        public string Library { get; set; }
        public string Collection { get; set; }
        [Display(Name = "Source")]
        public string DataSource { get; set; }
        public string Format { get; set; }
        public string Access { get; set; }
        public int Selected { get; set; }
    }
}