﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Access" xml:space="preserve">
    <value>Access</value>
  </data>
  <data name="Actions" xml:space="preserve">
    <value>Actions</value>
  </data>
  <data name="AddField" xml:space="preserve">
    <value>Add Field</value>
  </data>
  <data name="AddFromLibrary" xml:space="preserve">
    <value>Add from Library</value>
  </data>
  <data name="AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="AddSelected" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="APIConfig" xml:space="preserve">
    <value>API Configuration</value>
  </data>
  <data name="BaseDefinition" xml:space="preserve">
    <value>Base Definition</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Collection" xml:space="preserve">
    <value>Collection</value>
  </data>
  <data name="CommerceConnectorLogo" xml:space="preserve">
    <value>Commerce  Connector  2.0</value>
  </data>
  <data name="ConfigurationChangeLog" xml:space="preserve">
    <value>Configuration Change History</value>
  </data>
  <data name="ConfigurationSet" xml:space="preserve">
    <value>Configuration Set</value>
  </data>
  <data name="CONFIGUREADATAEXCHANGE" xml:space="preserve">
    <value>CONFIGURE A DATA EXCHANGE</value>
  </data>
  <data name="Copyright" xml:space="preserve">
    <value> Znode. All Rights Reserved. | Version 10.0.0 -</value>
  </data>
  <data name="CronMaxLengthError" xml:space="preserve">
    <value>Cron Expression can not be longer than 100 characters.</value>
  </data>
  <data name="DataDestination" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="DataExchange" xml:space="preserve">
    <value>Data Exchange</value>
  </data>
  <data name="DataExchangeLibrary" xml:space="preserve">
    <value>Data Exchange Library</value>
  </data>
  <data name="DataSource" xml:space="preserve">
    <value>Source</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ErrorStartDateGreaterThan" xml:space="preserve">
    <value>The Run Date may not be in the past.</value>
  </data>
  <data name="ErrorStartTimeEarlierThanNow" xml:space="preserve">
    <value>Run time cannot be earlier than current time.</value>
  </data>
  <data name="ExchangeName" xml:space="preserve">
    <value>Exchange Name</value>
  </data>
  <data name="Format" xml:space="preserve">
    <value>Format</value>
  </data>
  <data name="HeaderParams" xml:space="preserve">
    <value>Header Parameter</value>
  </data>
  <data name="HTTPHandler" xml:space="preserve">
    <value>HTTPHandler</value>
  </data>
  <data name="HTTPSHandler" xml:space="preserve">
    <value>HTTPSHandler</value>
  </data>
  <data name="Key" xml:space="preserve">
    <value>Key</value>
  </data>
  <data name="LabelCronExpression" xml:space="preserve">
    <value>Cron Expression</value>
  </data>
  <data name="LabelFrequency" xml:space="preserve">
    <value>Frequency</value>
  </data>
  <data name="LabelOneTime" xml:space="preserve">
    <value>One-Time</value>
  </data>
  <data name="LabelRecurring" xml:space="preserve">
    <value>Recurring</value>
  </data>
  <data name="LabelRunDate" xml:space="preserve">
    <value>Run Date</value>
  </data>
  <data name="LabelRunOnDemand" xml:space="preserve">
    <value>On-Demand</value>
  </data>
  <data name="LabelRunOnSchedule" xml:space="preserve">
    <value>Scheduled</value>
  </data>
  <data name="LabelRunTime" xml:space="preserve">
    <value>Run Time</value>
  </data>
  <data name="LabelScheduleConfiguration" xml:space="preserve">
    <value>Scheduler Configuration</value>
  </data>
  <data name="LabelScheduleName" xml:space="preserve">
    <value>Scheduler Name</value>
  </data>
  <data name="LabelScheduleSettings" xml:space="preserve">
    <value>Scheduler Settings</value>
  </data>
  <data name="LabelScheduleType" xml:space="preserve">
    <value>Scheduler Type</value>
  </data>
  <data name="LastRunResult" xml:space="preserve">
    <value>Last Run Result</value>
  </data>
  <data name="LastRunTime" xml:space="preserve">
    <value>Last Run Time</value>
  </data>
  <data name="Library" xml:space="preserve">
    <value>Library</value>
  </data>
  <data name="ManageDataExchange" xml:space="preserve">
    <value>Manage Data Exchange</value>
  </data>
  <data name="QueryParams" xml:space="preserve">
    <value>Query Parameter</value>
  </data>
  <data name="ReqRunDateError" xml:space="preserve">
    <value>Run Date is required.</value>
  </data>
  <data name="ReqRunTimeError" xml:space="preserve">
    <value>Run Time is required.</value>
  </data>
  <data name="ReqSchedulerNameError" xml:space="preserve">
    <value>Scheduler Name is required.</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Schedule" xml:space="preserve">
    <value>Schedule</value>
  </data>
  <data name="SchedulerNameMaxLengthError" xml:space="preserve">
    <value>Scheduler Name can not be longer than 100 characters.</value>
  </data>
  <data name="SFTPConfig" xml:space="preserve">
    <value>SFTP Configuration</value>
  </data>
  <data name="SFTPHandler" xml:space="preserve">
    <value>SFTPHandler</value>
  </data>
  <data name="Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StatusActivation" xml:space="preserve">
    <value>Activation</value>
  </data>
  <data name="Tags" xml:space="preserve">
    <value>Tags</value>
  </data>
  <data name="TransmissionConfguration" xml:space="preserve">
    <value>Transmission Confguration</value>
  </data>
  <data name="TransmissionDestination" xml:space="preserve">
    <value>Destination Configuration</value>
  </data>
  <data name="TransmissionSource" xml:space="preserve">
    <value>Source Configuration</value>
  </data>
  <data name="TriggerOrigin" xml:space="preserve">
    <value>Trigger Origin</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Value</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="ZnodeExportHandler" xml:space="preserve">
    <value>ZnodeExportHandler</value>
  </data>
  <data name="InvalidCronExpressionError" xml:space="preserve">
    <value>Enter a valid cron expression.</value>
  </data>
  <data name="PageNotFoundError" xml:space="preserve">
    <value>404 Error</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ErrorMessage" xml:space="preserve">
    <value>Error Message</value>
  </data>
  <data name="ErrorWhileProcessingRequest" xml:space="preserve">
    <value>An error occurred while processing your request.</value>
  </data>
  <data name="NotAuthorizedPage" xml:space="preserve">
    <value>You are not authorized to access this page.</value>
  </data>
  <data name="Oops" xml:space="preserve">
    <value>Oops!</value>
  </data>
  <data name="PageNotAvailable" xml:space="preserve">
    <value>A page you are accessing is not available.</value>
  </data>
  <data name="SomethingWrong" xml:space="preserve">
    <value>Something went wrong.</value>
  </data>
  <data name="TryAgainLater" xml:space="preserve">
    <value>Please try again later.</value>
  </data>
  <data name="Archive" xml:space="preserve">
    <value>Archive</value>
  </data>
  <data name="Basic" xml:space="preserve">
    <value>Basic</value>
  </data>
  <data name="Bearer" xml:space="preserve">
    <value>Bearer</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>DELETE</value>
  </data>
  <data name="Get" xml:space="preserve">
    <value>GET</value>
  </data>
  <data name="OAuth" xml:space="preserve">
    <value>OAuth 2.0</value>
  </data>
  <data name="Post" xml:space="preserve">
    <value>POST</value>
  </data>
  <data name="Put" xml:space="preserve">
    <value>PUT</value>
  </data>
  <data name="SelectAction" xml:space="preserve">
    <value>-- Select --</value>
  </data>
  <data name="SelectAPIMethod" xml:space="preserve">
    <value>-- Select --</value>
  </data>
  <data name="SelectAuthType" xml:space="preserve">
    <value>-- Select --</value>
  </data>
  <data name="LabelBaseDefinitionDetails" xml:space="preserve">
    <value>Base Definition Details</value>
  </data>
  <data name="LabelViewDataExchange" xml:space="preserve">
    <value>View a Data Exchange</value>
  </data>
  <data name="DeleteTaskSchedularSuccessMessage" xml:space="preserve">
    <value>Data exchange deleted successfully.</value>
  </data>
  <data name="DeleteTaskSchedularErrorMessage" xml:space="preserve">
    <value>Failed to delete data exchange.</value>
  </data>
  <data name="NoRecordsFound" xml:space="preserve">
    <value>No records found.</value>
  </data>
  <data name="LabelBack" xml:space="preserve">
    <value>Back</value>
  </data>
  <data name="DataExchangeList" xml:space="preserve">
    <value>Data Exchange List</value>
  </data>
  <data name="DeleteAction" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="LabelTestConnectivity" xml:space="preserve">
    <value>Test Connectivity</value>
  </data>
  <data name="MethodName" xml:space="preserve">
    <value>Method</value>
  </data>
  <data name="ProcessorFileName" xml:space="preserve">
    <value>Processor File Name</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>No Data Found</value>
  </data>
  <data name="Patch" xml:space="preserve">
    <value>PATCH</value>
  </data>
  <data name="SelectSourceFileFormat" xml:space="preserve">
    <value>-- Select --</value>
  </data>
  <data name="CSV" xml:space="preserve">
    <value>CSV</value>
  </data>
  <data name="JSON" xml:space="preserve">
    <value>JSON</value>
  </data>
  <data name="XML" xml:space="preserve">
    <value>XML</value>
  </data>
  <data name="Upload" xml:space="preserve">
    <value>Upload</value>
  </data>
  <data name="InputEditYAML" xml:space="preserve">
    <value>Input/Edit YAML</value>
  </data>
  <data name="UpdateMapping" xml:space="preserve">
    <value>Update Mapping</value>
  </data>
  <data name="UploadYourYMLYAMLFile" xml:space="preserve">
    <value>Upload Your YML/YAML File</value>
  </data>
  <data name="DownloadSampleYMLYAMLFile" xml:space="preserve">
    <value>Download Sample YML/YAML File</value>
  </data>
  <data name="Download" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="LabelRunOnRealTime" xml:space="preserve">
    <value>Real-Time</value>
  </data>
  <data name="LabelTotalProcessedRecords" xml:space="preserve">
    <value>Total Processed Records</value>
  </data>
  <data name="LabelSucceededRecords" xml:space="preserve">
    <value>Succeeded Records</value>
  </data>
  <data name="LabelFailedRecords" xml:space="preserve">
    <value>Failed Records</value>
  </data>
  <data name="ProcessingLogDetails" xml:space="preserve">
    <value>Data Exchange Log Details</value>
  </data>
  <data name="ReqSchedulerTypeError" xml:space="preserve">
    <value>Scheduler Type is required.</value>
  </data>
  <data name="CommonlyUsedCronExpressions" xml:space="preserve">
    <value>Commonly Used Cron Expressions</value>
  </data>
  <data name="WhatIsCronExpression" xml:space="preserve">
    <value>What is a Cron Expression?</value>
  </data>
  <data name="LabelZnodeImportLog" xml:space="preserve">
    <value>Znode Import Logs</value>
  </data>
  <data name="LabelLogId" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="LabelRowNumber" xml:space="preserve">
    <value>Row Number</value>
  </data>
  <data name="LabelCSVColumnName" xml:space="preserve">
    <value>CSV Column Name</value>
  </data>
  <data name="LabelCSVColumnData" xml:space="preserve">
    <value>CSV Column Data</value>
  </data>
  <data name="LabelErrorDescription" xml:space="preserve">
    <value>Error Description</value>
  </data>
  <data name="LabelImportProcessLogId" xml:space="preserve">
    <value>Log Id</value>
  </data>
  <data name="LabelTemplateId" xml:space="preserve">
    <value>Template Id</value>
  </data>
  <data name="LabelTemplateName" xml:space="preserve">
    <value>Template Name</value>
  </data>
  <data name="LabelImportName" xml:space="preserve">
    <value>Import Name</value>
  </data>
  <data name="LabelStartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="LabelEndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="ZnodeImportLogDetails" xml:space="preserve">
    <value>Znode Import Log Details</value>
  </data>
  <data name="DataExchangeLog" xml:space="preserve">
    <value>Data Exchange Logs</value>
  </data>
  <data name="CustomLibrary" xml:space="preserve">
    <value>Custom</value>
  </data>
  <data name="SchedulerNameExists" xml:space="preserve">
    <value>Scheduler Name already exists.</value>
  </data>
  <data name="LabelIsCustomProcessingRequired" xml:space="preserve">
    <value>Custom Processing Required</value>
  </data>
  <data name="LabelEnableStatus" xml:space="preserve">
    <value>Enable</value>
  </data>
  <data name="LabelEnable" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="LabelDisable" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="ExchangeNameExistsError" xml:space="preserve">
    <value>This Exchange is added with same name. Please enter a different Exchange Name.</value>
  </data>
  <data name="ExchangeNameRequiredError" xml:space="preserve">
    <value>Exchange name is required.</value>
  </data>
  <data name="DataExchangePopupTitle" xml:space="preserve">
    <value>Data Exchange - Add Exchange Name</value>
  </data>
  <data name="LabelCustomProcessingRequired" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="LabelCustomProcessingNotRequired" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="DataExchangePopupNote" xml:space="preserve">
    <value>This exchange will be added to the data exchange list, and all exchanges must have a different name.</value>
  </data>
  <data name="SingleDataExchange" xml:space="preserve">
    <value>Note: Single Data Exchange can be added at a time.</value>
  </data>
  <data name="AddSelectedSuccessMessage" xml:space="preserve">
    <value>You have successfully added the selected Data Exchange to your Exchange list.</value>
  </data>
  <data name="AddSelectedErrorMessage" xml:space="preserve">
    <value>Failed to add selected data exchange.</value>
  </data>
</root>
