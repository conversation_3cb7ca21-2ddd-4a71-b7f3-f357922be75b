﻿ @using Znode.Libraries.Resources.CommerceConnector_Resources

@{
    ViewBag.Title = "UnAuthorizedRequest";
}

<div class="d-flex justify-content-center align-items-center full-height mt-5">
    <div class="error-message text-center">
        <h1 data-test-selector="hdgOopsLabel">@CommerceConnector_Resources.Oops</h1>
        <h3 data-test-selector="hdgSomethingWentWrong">@CommerceConnector_Resources.SomethingWrong</h3>
        <p data-test-selector="paraNotAuthorizedText">@CommerceConnector_Resources.NotAuthorizedPage</p>
    </div>
</div>