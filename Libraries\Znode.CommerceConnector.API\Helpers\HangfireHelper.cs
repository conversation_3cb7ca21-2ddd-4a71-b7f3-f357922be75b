﻿using Znode.CommerceConnector.API.IHelpers;
using Znode.CommerceConnector.Hangfire;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Helpers;

namespace Znode.CommerceConnector.API.Helpers
{
    public class HangfireHelper : IHangfireHelper
    {
        private readonly IHangfireJob _hangfireJob;
        public HangfireHelper(IHangfireJob hangfireJob)
        {
            _hangfireJob = hangfireJob;
        }

        public bool CreateHangfireJob(SchedulerConfigurationModel model, string schedulerStatus)
        {
            string hangfireJobId = string.Empty;
            // Call HangfireJob             
            bool status = _hangfireJob.ConfigureJobs(model, schedulerStatus,out hangfireJobId);
            if (status && !string.IsNullOrEmpty(hangfireJobId))
            {
                model.HangfireJobId = hangfireJobId;

                //Update the Hangfire Job Id in the DB.
                HelperMethods.SaveSchedulerConfiguration(model);
            }
            return status;
        }

        public bool RemoveHangfireJob(SchedulerConfigurationModel model)
        => _hangfireJob.RemoveJob(model);

    }
}
