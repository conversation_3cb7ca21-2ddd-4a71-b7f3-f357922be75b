﻿root[].DestinationColumnName1: "{SourceColumnName1}"
root[].DestinationColumnName2: "{SourceColumnName2}"
root[].DestinationColumnName3: "{SourceColumnName3}"
root[].DestinationColumnName4: "{SourceColumnName4}"
root[].DestinationColumnName5: "{SourceColumnName5}"
Dtls: #if the destination request body contains nested array json, refer the below mapping and create the YAML accordingly else if not needed remove the below code 
  - root[].DestinationColumnName6: "{SourceColumnName6}"
    root[].DestinationColumnName7: "{SourceColumnName7}"
    root[].DestinationColumnName8: "{SourceColumnName8}"
    root[].DestinationColumnName9: "{SourceColumnName9}"
    root[].DestinationColumnName10: "{SourceColumnName10}"
root[].DestinationColumnName11: "{SourceColumnName11}"
root[].DestinationColumnName12: "{SourceColumnName12}"
root[].DestinationColumnName13: "{SourceColumnName13}"
root[].DestinationColumnName14: "{SourceColumnName14}"
root[].DestinationColumnName15: "{SourceColumnName15}"