﻿ @using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="download-upload-yml d-flex align-items-center mt-2 mb-4">
    <div class="w-auto">
        <p class="my-0" data-test-selector="paraDownloadSampleYMLYAMLFile">@CommerceConnector_Resources.DownloadSampleYMLYAMLFile</p>
    </div>
    <div class="w-50 download-btn">
        <a class="btn-text btn-text-secondary" aria-label="Download Sample YAML" onclick="dataExchange.DownloadTemplate()" data-test-selector="lnkDownloadSampleYAML">
            <i class="fas fa-download me-1"></i>
            <span data-test-selector="spnDownloadSampleYAML">@CommerceConnector_Resources.Download</span>
        </a>
    </div>
</div>
 
<div class="download-upload-yml d-flex align-items-center my-4">
    <div class="w-auto me-4">
        <p class="my-0" data-test-selector="paraUploadYourYMLYAMLFile">@CommerceConnector_Resources.UploadYourYMLYAMLFile</p>
    </div>
    <input type="file" id="yamlUpload" data-test-selector="txtYAMLUpload" class="form-control" style="display: none;" />
    <div class="w-auto ms-5">
        <button class="btn-text btn-text-secondary" aria-label="Upload YAML/YML File" id="uploadBtn" type="button" data-test-selector="btnUploadYAMLYMLFile">
            <i class="fas fa-upload me-1"></i>
            <span data-test-selector="spnUploadYAMLYMLFile">@CommerceConnector_Resources.Upload</span>
        </button>
    </div>
</div>

<div class="yaml-textarea" aria-label="YAML">
    <div class="editor-menu">
        <p class="ps-3 py-2 mb-0" data-test-selector="paraInputEditYAML">@CommerceConnector_Resources.InputEditYAML</p>
    </div>
    <div class="editor-root" style="overflow-y:scroll;"><textarea id="yamlEditor" placeholder="Paste or type your data here...." data-test-selector="txtYAMLEditor"></textarea></div>
</div>
<div class="align-content-center">
    <button class="btn-text btn-text-secondary mt-3" data-test-selector="btnUpdateMapping" aria-label="Update Mapping" onclick="return ManageDataExchange.prototype.saveYaml()">@CommerceConnector_Resources.UpdateMapping</button>
</div>
