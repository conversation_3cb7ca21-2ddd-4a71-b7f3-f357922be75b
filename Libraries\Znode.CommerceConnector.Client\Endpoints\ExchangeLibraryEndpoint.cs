﻿using Microsoft.AspNetCore.Http;

namespace Znode.CommerceConnector.Client.Endpoints
{
    public class ExchangeLibraryEndpoint : BaseEndpoint
    {
        public static string List() => $"{CommerceConnectorApi}/DataExchangeLibrary/GetDataExchangeLibraryList";

        public static string GetDataExchangeDetails(int id) => $"{CommerceConnectorApi}/ManageDataExchang/GetDataExchangeDetailsByID/{id}";

        public static string SaveDataExchangeDetails() => $"{CommerceConnectorApi}/ManageDataExchang/SaveDataExchangeDetails";
        public static string ExchangeList() => $"{CommerceConnectorApi}/DataExchangeLibrary/GetDataExchangeList";

        public static string AddSelectedExchange() => $"{CommerceConnectorApi}/DataExchangeLibrary/AddSelectedExchange";


        public static string TriggerDataExchangeSchedular(int erpId) => $"{CommerceConnectorApi}/TriggerTaskSchedular?erpId={erpId}";
        public static string RealTimeDataScheduler(dynamic requestData, int erpId) => $"{CommerceConnectorApi}/CallRealTimeDataExchange?erpId={erpId}";
        public static string DeleteDataExchangeSchedular(int erpId) => $"{CommerceConnectorApi}/DataExchangeLibrary/DeleteDataExchangeSchedular?erpId={erpId}";
        public static string GetBaseDefinitionDetailsByID(int erpId) => $"{CommerceConnectorApi}/ManageDataExchang/GetBaseDefinitionDetailsByID/{erpId}";

        public static string EnableDisableTaskScheduler(int erpId, bool isActive) => $"{CommerceConnectorApi}/EnableDisableTaskScheduler/?erpId={erpId}&isActive={isActive}";

        public static string CreateEditBaseDataExchange() => $"{CommerceConnectorApi}/ManageDataExchang/CreateEditBaseDataExchange";

        public static string TestConnection(string mode, string request) => $"{CommerceConnectorApi}/ManageDataExchang/TestConnection?mode={mode}";
        public static string GetConfigurationChangeList(int id) => $"{CommerceConnectorApi}/ManageDataExchang/GetConfigurationChangeList/{id}";
        public static string GetProcessingLogList() => $"{ZnodeApi}/import/getimportlogs";
        public static string GetProcessingLogDetails(int logId) => $"{ZnodeApi}/import/getimportlogdetails/{logId}";
        public static string DownloadSample() => $"{CommerceConnectorApi}/DownloadSample";
        public static string SaveYaml() => $"{CommerceConnectorApi}/SaveYaml";
        public static string GetSavedYamlData(int erpId) => $"{CommerceConnectorApi}/GetSavedYamlData?erpId={erpId}";

        public static string GetGenericLogList() => $"{CommerceConnectorApi}/DataExchangeLibrary/GetGenericLogList";

        public static string GetGenericLogDetails(int logId) => $"{CommerceConnectorApi}/DataExchangeLibrary/GetGenericLogDetails/{logId}";
        public static string ValidateSchedulerName(string schedulerName) => $"{CommerceConnectorApi}/ManageDataExchang/ValidateSchedulerName/{schedulerName}";
        public static string GetDefaultMediaConfiguration() => $"{ZnodeApi}/MediaConfiguration/GetDefaultMediaConfiguration";
        public static string ValidateExchangeName(string exchangeName) => $"{CommerceConnectorApi}/DataExchangeLibrary/ValidateExchangeName/{exchangeName}";
    }
}
