﻿@model Znode.CommerceConnector.Core.ConfigurationChangeLogListViewModel
@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();

}
<div class="col-md-12 nopadding dashboard-title">
    <div class="title-container d-flex justify-content-between">
      
        <h1 data-test-selector="hdgManageDataExchange" aria-label="Manage Data Exchange">@CommerceConnector_Resources.ManageDataExchange @(!string.IsNullOrEmpty(Model.ERPExchangeName) ? " - " + Model.ERPExchangeName : "")</h1>

        <div class="mt-2" id="configurationLog">
            <a href="@Url.Action("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token })" type="button" class="btn-text-icon" onclick="location.href='@Url.Action("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token})'" data-bs-dismiss="modal" data-test-selector="btnBack"> <em class='z-back'></em> @CommerceConnector_Resources.LabelBack </a>
        </div>
    </div>
</div>
<div class="col-md-12 page-container">
    <div class="row">
        <div class="col-md-3 col-lg-2">
            <div class="left-panel-side list-group" id="configTabs" aria-label="Configuration Navigation">
                <ul class="left-panel-side-ul nav nav-tabs border-0 d-block" data-test-selector="listLeftPanelSideUl">
                    <li class="nav-item" data-test-selector="listConfigurationSet" data-section="BaseDef"><a class="nav-link" href="/commerce-connector/ManageDataExchange/ManageDataExchangedetails/@Model.ERPBaseDefinitionId?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkConfigurationSet">@CommerceConnector_Resources.ConfigurationSet</a></li>
                    <li class="nav-item" data-test-selector="listConfigurationChangeLog" data-section="ChangeLog"><a class="nav-link active" href="/commerce-connector/ManageDataExchange/GetConfigurationChangeLogList/@Model.ERPBaseDefinitionId?exchangeName=@Model.ERPExchangeName&@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkConfigurationSet">@CommerceConnector_Resources.ConfigurationChangeLog</a></li>
                </ul>
            </div>
        </div>
        <div class="col-md-9 col-lg-10">
            <div class="data-exchange-library page-content" id="configChange">
                <div class="controls">
                    <div class="filter-search d-flex">
                        <input type="text" id="globalSearch" maxlength="130" placeholder="Search..." data-test-selector="txtSearch" />
                        <button type="button" id="globalSearchbtn" type="button" class="btn-search" data-test-selector="btnSearchIcon"><em class="z-search"></em></button>
                    </div>
                </div>
                <div id="configurationChangeLogGrid" class="ag-theme-alpine mt-4" data-test-selector="divConfigurationChangeLogGrid"></div>
                <div class="show-per-page d-flex mt-3" style="display: none;">
                    <div id="paginationControls" class="me-3"></div>
                    <div id="customRowInfo" class="total-record" data-test-selector="divCustomRowInfo" style="display: none;"></div>
                    <div class="show-page-count ps-3" style="display: none;">
                        <div class="d-flex align-items-center">
                            <label class="pe-3" data-test-selector="lblShow">@CommerceConnector_Resources.Show</label>
                            <select id="pageSizeSelect" data-test-selector="drpPageSizeSelect">
                                <option value="10" selected>10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="noRecordsMessage" class="text-center mt-3" style="display: none;">
                    @CommerceConnector_Resources.NoRecordsFound
                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="processLogErpId" value="@Model.ERPBaseDefinitionId" />

