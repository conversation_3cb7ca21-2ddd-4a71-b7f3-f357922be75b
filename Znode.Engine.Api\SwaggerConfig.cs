﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace Znode.Engine.Api
{
    /// <summary>
    /// Swagger Config
    /// </summary>
    public static class SwaggerConfig
    {
        /// <summary>
        /// Build Swagger
        /// </summary>
        /// <param name="builder"></param>
        public static void BuildSwagger(this WebApplicationBuilder builder)
        {
            builder.Services.AddSwaggerGen(c =>
            {
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
                {
                    In = ParameterLocation.Header,
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    BearerFormat = "JWT",
                    Description = "Enter your bearer token in the format: Bearer {your_token}"
                });
                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
                 {
                    new OpenApiSecurityScheme
                     {
                         Reference = new OpenApiReference
                         {
                           Type = ReferenceType.SecurityScheme,
                           Id = "Bearer"
                         }
                    },
                    new string[] { }
                 }
                });
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Znode Custom API",
                    Version = "v1"
                });
                c.ResolveConflictingActions(apiDescriptions => apiDescriptions.First());
                c.CustomSchemaIds(type => type.FullName);
                //c.IncludeXmlComments(GetXmlCommentsPath());
                //c.IncludeXmlComments(GetEventSubscriberXmlCommentsPath());
                c.DocInclusionPredicate((docName, apiDesc) =>
                {
                    if (!apiDesc.TryGetMethodInfo(out MethodInfo methodInfo))
                    {
                        return false;
                    }

                    return !methodInfo.GetCustomAttributes(true).OfType<ExcludeFromSwaggerAttribute>().Any();
                });
            });
        }

        static string GetXmlCommentsPath()
        {
            return string.Format(@"{0}Custom.Api.Core.xml",
                    AppDomain.CurrentDomain.BaseDirectory);
        }

        static string GetEventSubscriberXmlCommentsPath()
        {
            return string.Format(@"{0}Custom.Libraries.Event.xml",
                    AppDomain.CurrentDomain.BaseDirectory);
        }

        /// <summary>
        /// Configure Swagger
        /// </summary>
        /// <param name="app"></param>        
        public static void ConfigureSwagger(this WebApplication app)
        {
            // Configure the HTTP request pipeline.
            if (app.Environment.IsDevelopment() || app.Environment.IsProduction())
            {
                app.UseSwagger();
                app.UseSwaggerUI(c =>
                {
                    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Znode Custom API");
                });
            }

        }
    }

    [AttributeUsage(AttributeTargets.Method)]
    public class ExcludeFromSwaggerAttribute : Attribute
    {
    }
}