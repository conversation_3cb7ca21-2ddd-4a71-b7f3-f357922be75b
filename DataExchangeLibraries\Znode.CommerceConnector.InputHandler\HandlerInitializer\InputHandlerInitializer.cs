﻿using Microsoft.Data.SqlClient;

using System.Data;
using System.Diagnostics;
using System.Net;
using Znode.CommerceConnector.InputHandler.IHandlers;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class InputHandlerInitializer : IInputHandlerInitializer
    {
        IAPIHandler _aPIHandler;
        ISFTPHandler _iSFTPHandler;
        IFTPHandler _iFTPHandler;
        IHandlerDataHelper _dataHelper;
        IYAMLDataHandler _iYAMLDataHandler;

        public InputHandlerInitializer(IAPIHandler aPIHandler, ISFTPHandler sFTPHandler, IFTPHandler iFTPHandler, IHandlerDataHelper dataHelper, IYAMLDataHandler iYAMLDataHandler)
        {
            _aPIHandler = aPIHandler;
            _iSFTPHandler = sFTPHandler;
            _iFTPHandler = iFTPHandler;
            _dataHelper = dataHelper;
            _iYAMLDataHandler = iYAMLDataHandler;
        }

        public dynamic InputHandler(int erpId, int logId, dynamic requestBody, WebHeaderCollection headerCollection, dynamic inputModel)
        {
            MongoLogging.LogMessage(Constants.CallInputHandlerMethodCalledInInputHandlerInitializer, Constants.CallInputHandler, TraceLevel.Info);

            string transmissionMode = _dataHelper.GetInputTransmissionType(erpId);

            switch (transmissionMode)
            {
                case "APIHandler":
                    return _aPIHandler.APIDataHandler(erpId, logId, requestBody, headerCollection, inputModel);
                case "SFTPHandler":
                    return _iSFTPHandler.SFTPDataHandler(erpId, logId);
                case "FTPHandler":
                    return _iFTPHandler.FTPDataHandler(erpId, logId);
                case null:
                    MongoLogging.LogMessage(Constants.TranmissionModeNotFound, Constants.CallInputHandler, TraceLevel.Info);
                    return false;
                default:
                    return false;
            }
        }

        public HTTPConfigurationModel GetInputHandlerHTTPCredentials(int erpId)
        {
            List<HTTPConfigurationModel> dataModelList;
            try
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsCalledInAPIInputHandler, Constants.APIDataHandler, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPITransmissionDetails", sqlParameters);
                dataModelList = znodeViewRepository.ConvertDataTableToList<HTTPConfigurationModel>(response);
                return dataModelList?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsFailedInAPIInputHandler + ex.Message, Constants.APIDataHandler, TraceLevel.Error);
                return new HTTPConfigurationModel();
            }
        }

        public dynamic GetYAMLDataHandler(int erpId)
        {
            MongoLogging.LogMessage(Constants.GetYAMLDataHandlerMethodCalled, Constants.CallInputHandler, TraceLevel.Info);
            try
            {
                string yamlFileName = _dataHelper.GetYAMLFileNameByErpId(erpId);
                dynamic mappingModels = _iYAMLDataHandler.GetYAMLData(yamlFileName);
                return mappingModels;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetYAMLDataHandlerMethodFailed + ex.Message, Constants.CallInputHandler, TraceLevel.Error);
                return "";
            }
        }
    }
}
