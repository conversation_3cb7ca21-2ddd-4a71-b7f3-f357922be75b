﻿namespace Znode.CommerceConnector.Core.Models.WebGridModels
{
    public class GridResponseModel
    {
        public IList<dynamic> Data { get; set; } = new List<dynamic>();
        public IList<WebGridColumn> Columns { get; set; } = new List<WebGridColumn>();
        public int TotalCount { get; set; }
        public int FilteredCount { get; set; }
        public int PageSize { get; set; }
        public int LastPage => (int)Math.Ceiling((double)TotalCount / PageSize);
    }
}