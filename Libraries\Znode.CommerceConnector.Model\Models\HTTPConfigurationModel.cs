﻿namespace Znode.CommerceConnector.Model
{
    public class HTTPConfigurationModel
    {
        public int APIConfigurationId { get; set; }
        public int TransmissionConfigurationId { get; set; }
        public string APIAction { get; set; }
        public string Endpoint { get; set; }
        public string AuthType { get; set; }
        public string GrantType { get; set; }
        public string AccessTokenURL { get; set; }
        public string ClientId { get; set; }
        public string ClientSecret { get; set; }
        public string Scope { get; set; }
        public string LocationOfCredentials { get; set; }
        public string? UserName { get; set; }
        public string? Password { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
    }
}
