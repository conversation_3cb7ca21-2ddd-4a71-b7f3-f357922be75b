﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeTransmissionConfigurationTable
{
    public int ConfigurationId { get; set; }

    public int Erpid { get; set; }

    public string? SourceTransmissionMode { get; set; }

    public string? DestinationTransmissionMode { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeBaseDefinitionTable Erp { get; set; } = null!;

    public virtual ICollection<ZnodeApiconfigurationTable> ZnodeApiconfigurationTables { get; set; } = new List<ZnodeApiconfigurationTable>();

    public virtual ICollection<ZnodeSftpconfigurationTable> ZnodeSftpconfigurationTables { get; set; } = new List<ZnodeSftpconfigurationTable>();
}
