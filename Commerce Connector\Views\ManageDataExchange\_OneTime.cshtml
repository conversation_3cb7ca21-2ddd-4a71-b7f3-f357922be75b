﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel

@{
    var startTimeUtc = Model?.SchedulerConfigurationViewModel.StartTime ?? "00:00:00";
    var displayTime = "";
}

<div class="row">
    <div class="col-md-12 col-lg-6">
        <!-- Start Date -->
        <div class="control-label mb-2">
            @Html.LabelFor(model => model.SchedulerConfigurationViewModel.StartDate, new { @class = "required", @data_test_selector = "lblSchedulerStartDate" })
        </div>
        <div class="schedule position-relative">
            <div class="calender-icon">
                @Html.TextBoxFor(model => model.SchedulerConfigurationViewModel.StartDate, new { @id = "StartDate", @class = "form-control", autocomplete = "off", aria_label = "Scheduler Start Date" })
                <em class="z-calendar position-absolute" data-time-icon="icon-time" data-date-icon="icon-calendar"></em>
            </div>
            @Html.ValidationMessageFor(model => model.SchedulerConfigurationViewModel.StartDate, "", new { @id = "valStartDate", @data_test_selector = "valSchedulerStartDate" })
        </div>
    </div>
    <div class="col-md-12 col-lg-6">
        <!-- Start Time -->
        <div class="control-label mb-2">
            @Html.LabelFor(model => model.SchedulerConfigurationViewModel.StartTime, new { @class = "required", @data_test_selector = "lblSchedulerStartTime" })
        </div>
        <div class="schedule position-relative">
            <div class="time-icon">
                @Html.TextBox("StartTime_Display", displayTime, new { @id = "StartTime", @class = "form-control", autocomplete = "off", aria_label = "Scheduler Start Time", data_utc_time = @startTimeUtc })
                <em class="z-time-picker position-absolute" data-time-icon="icon-time" data-date-icon="icon-calendar"></em>
            </div>
            @Html.ValidationMessageFor(model => model.SchedulerConfigurationViewModel.StartTime, "", new { @id = "valStartTime", @data_test_selector = "valSchedulerStartTime" })
        </div>
    </div>
</div>
<!-- Hidden Fields -->
@Html.HiddenFor(model => model.SchedulerConfigurationViewModel.StartDate, new { @id = "HiddenStartDate" })
@Html.HiddenFor(model => model.SchedulerConfigurationViewModel.StartTime, new { @id = "HiddenStartTime" })

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const timeInput = document.getElementById("StartTime");
        const utcTime = timeInput.getAttribute("data-utc-time");

        let defaultLocalTime = null;
        if (utcTime && utcTime !== "00:00:00") {
            const [hh, mm, ss] = utcTime.split(":").map(Number);

            // Create local time from UTC
            const utcDate = new Date(Date.UTC(
                new Date().getUTCFullYear(),
                new Date().getUTCMonth(),
                new Date().getUTCDate(),
                hh, mm, ss
            ));
            defaultLocalTime = new Date(
                utcDate.getUTCFullYear(),
                utcDate.getUTCMonth(),
                utcDate.getUTCDate(),
                utcDate.getUTCHours(),
                utcDate.getUTCMinutes(),
                utcDate.getUTCSeconds()
            );
        }
        const timeHidden = document.getElementById("HiddenStartTime");
        const dateInput = document.getElementById("StartDate");
        const dateHidden = document.getElementById("HiddenStartDate");

        // Date picker (stores MM/dd/yyyy as UTC)
        flatpickr(dateInput, {
            dateFormat: "m/d/Y",
            defaultDate: dateInput.value || null,
            allowInput: false,
            clickOpens: true,
            onChange: function (selectedDates) {
                if (selectedDates.length) {
                    const localDate = selectedDates[0];

                    // Convert to UTC
                    const utcDate = new Date(Date.UTC(
                        localDate.getFullYear(),
                        localDate.getMonth(),
                        localDate.getDate()
                    ));

                    // Format as MM/dd/yyyy
                    const mm = String(utcDate.getUTCMonth() + 1).padStart(2, '0');
                    const dd = String(utcDate.getUTCDate()).padStart(2, '0');
                    const yyyy = utcDate.getUTCFullYear();

                    dateHidden.value = `${mm}/${dd}/${yyyy}`;
                }
            }
        });

        // Time picker (stores UTC hh:mm:ss)
        const timePicker = flatpickr(timeInput, {
            enableTime: true,
            noCalendar: true,
            dateFormat: "h:i k",
            clickOpens: true,
            allowInput: false,
            defaultDate: defaultLocalTime || null,
            time_24hr: false,
            formatDate: function (date, format, instance) {
                return DataExchange.prototype.ConvertTimeTo12HourFormat(date, false);
            },
            onOpen: function (selectedDates, dateStr, instance) {
                if (!selectedDates.length) {
                    const now = new Date();
                    now.setSeconds(0, 0);
                    instance.setDate(now, true);
                    instance.redraw();
                }
            },
            onChange: function (selectedDates, dateStr, instance) {
                if (selectedDates.length) {
                    const localDate = selectedDates[0];
                    timeInput.value = DataExchange.prototype.ConvertTimeTo12HourFormat(localDate, false);

                    // Convert to UTC
                    const utcHours = localDate.getUTCHours();
                    const utcMinutes = localDate.getUTCMinutes();

                    const hh = String(utcHours).padStart(2, '0');
                    const mm = String(utcMinutes).padStart(2, '0');

                    document.getElementById("HiddenStartTime").value = `${hh}:${mm}:00`;
                }
            },
            onReady: function (selectedDates, dateStr, instance) {
                if (selectedDates.length) {
                    timeInput.value = DataExchange.prototype.ConvertTimeTo12HourFormat(selectedDates[0], false);
                }
            }
        });
        if (!timeInput.value && defaultLocalTime) {
            timeInput.value = DataExchange.prototype.ConvertTimeTo12HourFormat(defaultLocalTime, false);
        }
        document.querySelector(".z-calendar")?.addEventListener("click", () => dateInput.focus());
        document.querySelector(".z-time-picker")?.addEventListener("click", () => timePicker.open());
    });
</script>
