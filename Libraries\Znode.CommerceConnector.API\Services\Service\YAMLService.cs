﻿using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using System.Text;
using System.Text.RegularExpressions;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Services.Service
{
    public class YAMLService : IYAMLService
    {
        private readonly AppDbContext _context;
        private readonly string _samplePath;
        private readonly string _blobFolderPath;

        public YAMLService(AppDbContext context,  IWebHostEnvironment webHostEnvironment)
        {
            _context = context;
            _blobFolderPath = HelperMethods.YAMlFilepath ?? "Data/CCYAMLFiles";
            _samplePath = "SampleYaml.yaml";
        }

        public dynamic DownloadSample(MediaConfigurationModel mediaConfiguration)
        {
            string yamlContent = string.Empty;

            try
            {
                string blobPath = $"{_blobFolderPath}/{_samplePath}"; 

                BlobContainerClient containerClient = HelperUtility.GetBlobContainerClient(mediaConfiguration);
                BlobClient blobClient = containerClient.GetBlobClient(blobPath);

                if (blobClient.Exists())
                {
                    var downloadResponse = blobClient.DownloadContent();
                    yamlContent = downloadResponse.Value.Content.ToString();
                }
                else
                {
                    MongoLogging.LogMessage(CommerceConnectorConstants.BlobPathNotFound + blobPath, CommerceConnectorConstants.YAMLService, TraceLevel.Warning);
                }
                return yamlContent;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.DownloadSampleServiceFailed + ex.Message, CommerceConnectorConstants.YAMLService, TraceLevel.Error);
                return string.Empty;
            }
        }

        public dynamic SaveYaml(YAMLSaveRequestModel fileContent)
        {
            try
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.SaveYamlServiceCalled, CommerceConnectorConstants.YAMLService, TraceLevel.Info);

                string yamlFileName = $"{Regex.Replace(Regex.Replace(fileContent.DataExchangeName ?? "", @"[^a-zA-Z0-9]", ""), @"\s+", "")}{fileContent.ERPId}.yaml";
                string blobPath = $"{_blobFolderPath}/{yamlFileName}";
                MediaConfigurationModel mediaConfiguration = fileContent.MediaConfigurationModel;

                BlobContainerClient containerClient = HelperUtility.GetBlobContainerClient(mediaConfiguration);
                
                BlobClient blobClient = containerClient.GetBlobClient(blobPath);

                if (!string.IsNullOrWhiteSpace(fileContent.FileContent))
                {
                    using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(fileContent.FileContent)))
                    {
                        blobClient.Upload(stream, overwrite: true);
                    }
                }
                int userId = 0;
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>
        {
            new SqlParameter("@FileName", yamlFileName),
            new SqlParameter("@ERPBaseDefinitionId", fileContent.ERPId),
            new SqlParameter("@UserId", userId)
        };
                DataTable response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_UpdateYAMLFileNameByERPId", sqlParameters);
                return HelperUtility.IsNotNull(response);
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.SaveYamlServiceFailed + ex.Message, CommerceConnectorConstants.YAMLService, TraceLevel.Error);
                return false;
            }
        }

        public dynamic GetSavedYamlData(int erpId, MediaConfigurationModel mediaConfiguration)
        {
            try
            {
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>
            {
                new SqlParameter("@ERPBaseDefinitionId", erpId)
            };

                DataTable response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetYAMLFileNameFromBaseDefinition", sqlParameters);
                string yamlFileName = new ZnodeViewRepository().ConvertDataTableToList<ZnodeErpbaseDefinition>(response)?.FirstOrDefault()?.YamlfileName;

                if (string.IsNullOrEmpty(yamlFileName)) return string.Empty;  

                string blobPath = $"{_blobFolderPath}/{yamlFileName}";  
                BlobContainerClient containerClient = HelperUtility.GetBlobContainerClient(mediaConfiguration);

                BlobClient blobClient = containerClient.GetBlobClient(blobPath);

                if (blobClient.Exists())
                {
                    var downloadResponse = blobClient.DownloadContent();
                    return downloadResponse.Value.Content.ToString();
                }
                else
                {
                    MongoLogging.LogMessage(CommerceConnectorConstants.BlobPathNotFound + blobPath, CommerceConnectorConstants.YAMLService, TraceLevel.Warning);
                    return string.Empty;
                }
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
