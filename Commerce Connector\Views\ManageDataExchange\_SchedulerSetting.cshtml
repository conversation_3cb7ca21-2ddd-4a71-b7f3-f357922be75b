﻿@using Znode.Libraries.Resources.CommerceConnector_Resources
@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel

<div class="mt-2">
    <h6 class="mb-3" data-test-selector="hdgSchedularSettings">@CommerceConnector_Resources.LabelScheduleSettings</h6>

    <div class="row">
        <div class="col-md-12 col-lg-6">
            <div class="form-group mb-3">
                <div class="control-label mb-2">
                    @Html.LabelFor(model => model.SchedulerConfigurationViewModel.SchedulerFrequency, new { @data_test_selector = "lblSchedulerFrequency" })
                </div>
                <div class="control-md">
                    @Html.DropDownListFor(model => model.SchedulerConfigurationViewModel.SchedulerFrequency, Model.SchedulerConfigurationViewModel.ScheduleFrequencyList, new { @id = "SchedulerFrequency", @class = "form-select", aria_label = "Scheduler Frequency" })
                    <span class="text-danger field-validation-error" id="SchedulerFrequencyError" data-test-selector="spnSchedulerFrequency" aria-label="Scheduler Frequency Error"></span>
                </div>
            </div>
        </div>
    </div>

    <div class="form-group">
        <div id="oneTimeFrequency" class="one-time-frequency">
            @Html.Partial("_OneTime", Model)
        </div>

        <div id="recurringFrequency" style="display:none">
            @Html.Partial("_Recurring", Model)
        </div>
    </div>
</div>
@Html.HiddenFor(model => model.SchedulerConfigurationViewModel.HangfireJobId)