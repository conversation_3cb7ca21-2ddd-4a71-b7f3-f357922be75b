﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();
    if (string.IsNullOrEmpty(token))
    {
        string referer = Context.Request.Headers["Referer"].ToString();

        if (!string.IsNullOrWhiteSpace(referer) && referer.Contains(CCAdminConstant.CCAdminToken))
        {

            Uri refererUri = new Uri(referer);
            var queryParams = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(refererUri.Query);

            if (queryParams.TryGetValue(CCAdminConstant.CCAdminToken, out var tokenRequest))
            {
                token = tokenRequest.ToString();
            }
        }

    }

}
@using (Html.BeginForm(null, null, FormMethod.Post, new
{
    action = Url.Action("ManageDataExchangedetails", "ManageDataExchange", new { CCAdminToken = token }),
    id = "frmRegister",
    onsubmit = "return ManageDataExchange.prototype.SubmitDataExchangeForm()"
}))
{
    <div class="col-md-12 nopadding dashboard-title">
        <div class="title-container d-flex justify-content-between align-items-center">

            <h1 data-test-selector="hdgManageDataExchange" aria-label="Manage Data Exchange">@CommerceConnector_Resources.ManageDataExchange @(!string.IsNullOrEmpty(Model.Name) ? " - " + Model.Name : "")</h1>
            <div class="alert-message">
                <div class="messageBoxContainer text-center" id="exchangeLibraryNotification"></div>
                @{
                    if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divSuccessMessage">
                            @TempData["SuccessMessage"]
                        </div>
                    }
                    else if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divFailureMessage">
                            @TempData["ErrorMessage"]
                        </div>
                    }
                }
            </div>
            <div class="mt-2">
                <button type="submit" id="manageDataExchange" class="btn-text btn-text-secondary me-2" data-test-selector="btnSave" aria-label="Save Manage Data Exchange">@CommerceConnector_Resources.Save</button>
                <a href="@Url.Action("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token })" class="btn-text btn-text-secondary" data-test-selector="lnkCancel" aria-label="Cancel Manage Data Exchange">@CommerceConnector_Resources.Cancel</a>
            </div>
        </div>
    </div>
    <div class="col-md-12 page-container">
        <!-- Left Panel -->
        <div class="row">
            <div class="col-md-3 col-lg-2">
                <div class="left-panel-side list-group" id="configTabs" aria-label="Configuration Navigation">
                    <ul class="left-panel-side-ul nav nav-tabs border-0 d-block" data-test-selector="listLeftPanelSideUl">
                        <li class="nav-item" data-test-selector="listConfigurationSet" data-section="BaseDef"><a class="nav-link active" href="/commerce-connector/ManageDataExchange/ManageDataExchangedetails/@Model.ERPBaseDefinitionId?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkConfigurationSet">@CommerceConnector_Resources.ConfigurationSet</a></li>
                        <li class="nav-item" data-test-selector="listConfigurationChangeLog" data-section="ChangeLog"><a class="nav-link" href="/commerce-connector/ManageDataExchange/GetConfigurationChangeLogList/@Model.ERPBaseDefinitionId?exchangeName=@Model.Name&@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkConfigurationSet">@CommerceConnector_Resources.ConfigurationChangeLog</a></li>
                    </ul>
                </div>
            </div>



            <!-- Right Panel -->
            <div class="col-md-9 col-lg-10" id="configContent">
                <div class="tab-content">
                    <div id="configurationSet" class="tab-pane active">
                        @Html.Partial("_BaseDefinition", Model)
                    </div>
                </div>
            </div>
        </div>
    </div>
}