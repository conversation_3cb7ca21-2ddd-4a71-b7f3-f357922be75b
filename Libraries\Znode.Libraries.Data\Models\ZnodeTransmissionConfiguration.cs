﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeTransmissionConfiguration
{
    public int TransmissionConfigurationId { get; set; }

    public int ErpbaseDefinitionId { get; set; }

    public string? SourceTransmissionMode { get; set; }

    public string? DestinationTransmissionMode { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeErpbaseDefinition ErpbaseDefinition { get; set; } = null!;

    public virtual ICollection<ZnodeApiconfiguration> ZnodeApiconfigurations { get; set; } = new List<ZnodeApiconfiguration>();

    public virtual ICollection<ZnodeSftpconfiguration> ZnodeSftpconfigurations { get; set; } = new List<ZnodeSftpconfiguration>();
}
