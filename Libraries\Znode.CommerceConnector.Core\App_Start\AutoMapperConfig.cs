﻿using AutoMapper;

using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Resources.CommerceConnector_Resources;

namespace Znode.CommerceConnector.Core
{
    public class AutoMapperConfig : Profile
    {
        public AutoMapperConfig()
        {
            RegisterMap();
        }
        protected void RegisterMap()
        {
            // Mapping between DataExchangeModel and DataExchangeViewModel
            CreateMap<DataExchangeModel, DataExchangeViewModel>()
            .ForMember(dest => dest.DataExchangeLibraryId, opt => opt.MapFrom(src => src.DataExchangeLibraryId))
            .ForMember(dest => dest.ExchangeName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.DataSource, opt => opt.MapFrom(src => src.DataSource));

            CreateMap<BaseDefinitionModel, BaseDefinitionViewModel>()
            .ForMember(dest => dest.ERPBaseDefinitionId, opt => opt.MapFrom(src => src.ERPBaseDefinitionId))
            .ForMember(dest => dest.DataExchangeLibraryId, opt => opt.MapFrom(src => src.DataExchangeLibraryId))
            .ForMember(dest => dest.Version, opt => opt.MapFrom(src => src.Version))
            .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => src.Tags))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.DataSource, opt => opt.MapFrom(src => src.DataSource))
            .ForMember(dest => dest.DataDestination, opt => opt.MapFrom(src => src.DataDestination))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dest => dest.ProcessorFileName, opt => opt.MapFrom(src => src.ProcessorFileName))
            .ForMember(dest => dest.MethodName, opt => opt.MapFrom(src => src.MethodName))
            .ForMember(dest => dest.TriggerOrigin, opt => opt.MapFrom(src => src.TriggerOrigin))
            .ForMember(dest => dest.ConfigurationChangeJsonData, opt => opt.MapFrom(src => src.ConfigurationChangeJsonData))
            .ForMember(dest => dest.Access, opt => opt.MapFrom(src => src.Access))
            .ReverseMap();

            CreateMap<TransmissionConfigurationModel, TransmissionConfigurationViewModel>()
            .ForMember(dest => dest.TransmissionConfigurationId, opt => opt.MapFrom(src => src.TransmissionConfigurationId ?? 0))
            .ForMember(dest => dest.TransmissionMode, opt => opt.MapFrom(src => src.TransmissionMode))
            .ForMember(dest => dest.TransmissionModeDestination, opt => opt.MapFrom(src => src.DestinationTransmissionMode))
            .ForMember(dest => dest.HttpsConfig, opt => opt.MapFrom(src => src.HttpsConfig)).ReverseMap();

            CreateMap<HttpsTransmissionConfigurationModel, HttpsTransmissionConfigurationViewModel>()
            .ForMember(dest => dest.TransmissionConfigurationId, opt => opt.MapFrom(src => src.TransmissionConfigurationId ?? 0))
            .ForMember(dest => dest.APIConfigurationId, opt => opt.MapFrom(src => src.APIConfigurationId ?? 0))
            .ForMember(dest => dest.HttpAction, opt => opt.MapFrom(src => src.HttpAction))
            .ForMember(dest => dest.Endpoint, opt => opt.MapFrom(src => src.Endpoint))
            .ForMember(dest => dest.AuthenticationType, opt => opt.MapFrom(src => src.AuthenticationType))
            .ForMember(dest => dest.GrantType, opt => opt.MapFrom(src => src.GrantType))
            .ForMember(dest => dest.AccessTokenUrl, opt => opt.MapFrom(src => src.AccessTokenUrl))

            .ForMember(dest => dest.ClientId, opt => opt.MapFrom(src => src.ClientId))
            .ForMember(dest => dest.ClientSecret, opt => opt.MapFrom(src => src.ClientSecret))
            .ForMember(dest => dest.Scope, opt => opt.MapFrom(src => src.Scope))

            .ForMember(dest => dest.LocationOfCredentials, opt => opt.MapFrom(src => src.LocationOfCredentials))
            .ForMember(dest => dest.Format, opt => opt.MapFrom(src => src.Format))
            .ForMember(dest => dest.Username, opt => opt.MapFrom(src => src.Username))
            .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.Password))
            .ForMember(dest => dest.APIKey, opt => opt.MapFrom(src => src.APIKey))
            .ForMember(dest => dest.QueryParams, opt => opt.MapFrom(src => src.QueryParams))
            .ForMember(dest => dest.HeaderParams, opt => opt.MapFrom(src => src.HeaderParams)).ReverseMap();

            CreateMap<SchedulerConfigurationModel, SchedulerConfigurationViewModel>()
                .ForMember(dest => dest.HangfireJobId, opt => opt.MapFrom(src => src.HangfireJobId ?? string.Empty))
                .ForMember(dest => dest.LastRunTime, opt => opt.MapFrom(src => src.LastRunTime));

            CreateMap<SchedulerConfigurationViewModel, SchedulerConfigurationModel>()
                .ForMember(dest => dest.HangfireJobId, opt => opt.MapFrom(src => src.HangfireJobId ?? string.Empty))
                .ForMember(dest => dest.LastRunTime, opt => opt.MapFrom(src => src.LastRunTime));

            CreateMap<SftpTransmissionConfigurationModel, SftpTransmissionConfigurationViewMOdel>()
            .ForMember(dest => dest.ConfigurationId, opt => opt.MapFrom(src => src.TransmissionConfigurationId ?? 0))
            .ForMember(dest => dest.SFTPConfigurationId, opt => opt.MapFrom(src => src.SFTPConfigurationId ?? 0))
            .ForMember(dest => dest.ServerAddress, opt => opt.MapFrom(src => src.ServerAddress))
            .ForMember(dest => dest.FolderPath, opt => opt.MapFrom(src => src.FolderPath))
            .ForMember(dest => dest.Filename, opt => opt.MapFrom(src => src.Filename))
            .ForMember(dest => dest.Username, opt => opt.MapFrom(src => src.Username))
            .ForMember(dest => dest.Password, opt => opt.MapFrom(src => src.Password))
            .ForMember(dest => dest.ActionAfterRetrieval, opt => opt.MapFrom(src => src.ActionAfterRetrieval))
            .ForMember(dest => dest.ConfigurationId, opt => opt.MapFrom(src => src.TransmissionConfigurationId))
            .ForMember(dest => dest.Format, opt => opt.MapFrom(src => src.Format))
            .ForMember(dest => dest.PortNumber, opt => opt.MapFrom(src => src.PortNumber)).ReverseMap();

           CreateMap<StatusActivationModel, StatusActivationViewModel>()
           .ForMember(dest => dest.ERPBaseDefinitionId, opt => opt.MapFrom(src => src.ERPBaseDefinitionId ?? 0))
           .ForMember(dest => dest.StatusId, opt => opt.MapFrom(src => src.StatusId))
           .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
           .ForMember(dest => dest.ErrorNotification, opt => opt.MapFrom(src => src.ErrorNotification))
           .ForMember(dest => dest.InformationNotification, opt => opt.MapFrom(src => src.InformationNotification))
           .ForMember(dest => dest.WarningNotification, opt => opt.MapFrom(src => src.WarningNotification)).ReverseMap();

           CreateMap<KeyValueModel, KeyValueViewModel>().ReverseMap();

            // Mapping between BaseExchangeModel and StandardDataExchangesViewModel
           CreateMap<BaseExchangeModel, StandardDataExchangesViewModel>()
           .ForMember(dest => dest.DataExchangeLibraryId, opt => opt.MapFrom(src => src.ERPBaseDefinitionId))
           .ForMember(dest => dest.ExchangeName, opt => opt.MapFrom(src => src.Name))
           .ForMember(dest => dest.Library, opt => opt.MapFrom(src => src.Library))
           .ForMember(dest => dest.Format, opt => opt.MapFrom(src => src.Format))
           .ForMember(dest => dest.Source, opt => opt.MapFrom(src => src.DataSource))
           .ForMember(dest => dest.Destination, opt => opt.MapFrom(src => src.DataDestination));

            CreateMap<StandardDataExchangeModel, StandardDataExchangesViewModel>()
            .ForMember(dest => dest.DataExchangeLibraryId, opt => opt.MapFrom(src => src.ERPBaseDefinitionId))
            .ForMember(dest => dest.ExchangeName, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Library, opt => opt.MapFrom(src => src.Library))
            .ForMember(dest => dest.Format, opt => opt.MapFrom(src => src.Format))
            .ForMember(dest => dest.Source, opt => opt.MapFrom(src => src.DataSource))
            .ForMember(dest => dest.Destination, opt => opt.MapFrom(src => src.DataDestination))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.IsActive))
            .ForMember(dest => dest.Schedule, opt => opt.MapFrom(src => src.Scheduler == CCAdminConstant.OnDemand ? CommerceConnector_Resources.LabelRunOnDemand : 
            src.Scheduler == CCAdminConstant.RealTime ? CommerceConnector_Resources.LabelRunOnRealTime : src.Scheduler));

            CreateMap<BaseDefinitionViewModel, BaseDefinitionModel>().ReverseMap();

            CreateMap<ConfigurationChangeLogModel, ConfigurationChangeLogViewModel>()
            .ForMember(dest => dest.ConfigurationLogID, opt => opt.MapFrom(src => src.ConfigurationLogID))
            .ForMember(dest => dest.FieldName, opt => opt.MapFrom(src => src.FieldName))
            .ForMember(dest => dest.OldValue, opt => opt.MapFrom(src => src.OldValue))
            .ForMember(dest => dest.NewValue, opt => opt.MapFrom(src => src.NewValue))
            .ForMember(dest => dest.ChangeDateTime, opt => opt.MapFrom(src => src.ChangeDateTime))
            .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.UserName));

            CreateMap<ImportLogsModel, ProcessingLogsViewModel>().ReverseMap();
            CreateMap<ImportLogsListResponse, ProcessingLogListViewModel>().ReverseMap();
            CreateMap<LogDetailsViewModel, ImportLogDetailsModel>().ReverseMap();
            CreateMap<ProcessorLogModel, ProcessingLogsViewModel>()
                .ForMember(dest => dest.ImportProcessLogId, opt => opt.MapFrom(src => src.ImportDataExchangeProcessLogId))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.StatusName))
                .ReverseMap();
            CreateMap<ProcessingLogDetailViewModel, ProcessingLogDetailsModel>()
                .ForMember(dest => dest.ERPBaseDefinitionId, opt => opt.MapFrom(src => src.ErpBaseDefinitionId))
                .ReverseMap();

            CreateMap<AddSelectedExchangeViewModel, AddSelectedExchangeModel>().ReverseMap();
        }
    }
}
