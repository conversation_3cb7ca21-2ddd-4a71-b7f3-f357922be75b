using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Runtime.Remoting;
using System.Text;
using System;
using System.IO;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.ECommerce.Utilities;
using System.Collections.Generic;
using Znode.CommerceConnector.Client;
using Constants = Znode.CommerceConnector.OutputHandler.Constants;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using MongoDB.Bson;

namespace Znode.CommerceConnector.Processor
{
    public class FSCRealtimeOrderHeader : IProcessor
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public FSCRealtimeOrderHeader(IOutputHandlerInitializer handler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = handler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

        public dynamic ProcessData(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            try
            {
                dynamic data = inputModel;
                MongoLogging.LogMessage("FSCRealTimeOrderHeaderCalled", ProcessorConstants.ProcessData, TraceLevel.Info);
                
                MongoLogging.LogMessage(ProcessorConstants.ParserFSCRealTimeOrderProcessorProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                //Process the data
                dynamic yamlModel = _inputHandler.GetYAMLDataHandler(erpId);
                data = _iParser.ParseYAMLData(data, erpId, yamlModel);

                MongoLogging.LogMessage("OutputHandler from FSCRealTimeOrderHeaderProcessor Called ", ProcessorConstants.ProcessData, TraceLevel.Info);

                WebHeaderCollection headers = OutputHandlerHeaders(erpId);
                //Upload the data
                return _outputHandler.OutputHandler(data, erpId, OutputHandlerRequestBody(data), headers, processorDetails,inputModel);
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerFSCRealTimeOrderProcessorProcessorFailed + ex.Message, ProcessorConstants.ProcessData, TraceLevel.Info);
                return false;
            }
        }

        private dynamic OutputHandlerRequestBody(dynamic data)
        {
            dynamic orderRequest = JsonConvert.DeserializeObject<dynamic>(data);
            return orderRequest;
        }

        private dynamic InputHandlerRequestBody()
        {
            return new NullableRequestBody();
        }

        private WebHeaderCollection InputHandlerHeaders(int erpId)
        {
            HTTPConfigurationModel httpConfigurationModel = _inputHandler.GetInputHandlerHTTPCredentials(erpId);
            if (HelperUtility.IsNotNull(httpConfigurationModel))
            {
                WebHeaderCollection headers = new WebHeaderCollection
            {
                { "Authorization", $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes($"{httpConfigurationModel.ClientId.Trim()}:{httpConfigurationModel.ClientSecret.Trim()}"))}" },
                { "X-API-Key", httpConfigurationModel.AccessTokenURL.Trim() },
            };
                return headers;
            }
            return new WebHeaderCollection();
        }


        private WebHeaderCollection OutputHandlerHeaders(int erpId)
        {
            HTTPConfigurationModel httpConfigurationModel = _outputHandler.GetOutputHandlerAPICredentials(erpId);
            WebHeaderCollection headers = new WebHeaderCollection
                    {
                        { Constants.Authorization, ("Bearer " + GetBearerToken(httpConfigurationModel)) }
                    };

            return headers;
        }

        public dynamic GetBearerToken(HTTPConfigurationModel httpConfigurationModel)
        {
            BearerTokenModel bearerTokenModel = new BearerTokenModel();
            var keyValues = new List<KeyValuePair<string, string>>()
            {
                new KeyValuePair<string, string>(Constants.client_id,httpConfigurationModel.ClientId),
                new KeyValuePair<string, string>(Constants.client_secret, httpConfigurationModel.ClientSecret),
                new KeyValuePair<string, string>(Constants.grant_type, httpConfigurationModel.GrantType),
                new KeyValuePair<string, string>(Constants.scope, httpConfigurationModel.Scope)
            };

            ApiClient apiClient = new ApiClient();
            RequestModel requestModel = apiClient.SetRequestModel(httpConfigurationModel.AccessTokenURL);
            requestModel.RequestBody = string.Join("&", keyValues.Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value ?? "")}"));
            requestModel.ContentType = "application/x-www-form-urlencoded";
            TokenResponseModel apiResponse = apiClient.PostRequest<TokenResponseModel>(requestModel, out string statusCode);
            return apiResponse?.access_token;
        }
    }
}
