﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeStatusActivationTable
{
    public int StatusActivationId { get; set; }

    public bool IsActive { get; set; }

    public string? InformationNotifications { get; set; }

    public string? WarningNotifications { get; set; }

    public string? ErrorNotifications { get; set; }

    public int? CreatedBy { get; set; }

    public DateTime? CreatedDate { get; set; }

    public int? ModifiedBy { get; set; }

    public DateTime? ModifiedDate { get; set; }

    public int? Erpid { get; set; }

    public virtual ZnodeBaseDefinitionTable? Erp { get; set; }
}
