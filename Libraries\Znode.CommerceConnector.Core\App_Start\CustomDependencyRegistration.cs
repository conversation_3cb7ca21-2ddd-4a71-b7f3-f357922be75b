﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.Client.Clients;
using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.Core.Agents;
using Znode.CommerceConnector.Core.IAgents;

namespace Znode.CommerceConnector.Core
{
    public static class CustomDependencyRegistration
    {
        /// <summary>
        /// Configure or register custom Dependency Injection (DI).
        /// </summary>
        /// <param name="builder"></param>
        public static void RegisterDI(this WebApplicationBuilder builder)
        {
            builder.Services.AddTransient<IExchangeLibraryClient, ExchangeLibraryClient>();
            builder.Services.AddTransient<IGetExchangeLibrariesAgent, GetExchangeLibrariesAgent>();
            builder.Services.AddTransient<ICCDataExchangeAgent, CCDataExchangeAgent>();
            builder.Services.AddTransient<IGetDataExchangeClient, GetDataExchangeClient>();
            builder.Services.AddTransient<IManageDataExchangClient, ManageDataExchangClient>();
            builder.Services.AddTransient<IManageDataExchangeAgent, ManageDataExchangeAgent>();
            builder.Services.AddTransient<IYAMLAgent, YAMLAgent>();
            builder.Services.AddTransient<IYAMLClient, YAMLClient>();
        }
    }
}
