﻿using Microsoft.Data.SqlClient;
using System.Data;

namespace Znode.CommerceConnector.InputHandler
{
    public interface IHandlerDataHelper
    {
        List<string> CreateTempTableWithData(DataSet tablesToInsert);
        string CreateTempTable(DataTable tableToinsert, Guid tableGuid);
        string GenerateTableColumns(DataTable tableDump);
        void InsertDataIntoTempTable(DataTable tableToInsert, string tableName);
        void InsertData(string tableName, DataTable fileData);
        SqlConnection GetSqlConnection();
        string GetInputTransmissionType(int erpId);
        string GetSFTPFormat(int erpId, string type);
        string GetAPIFormat(int erpId, string type);
        string GetYAMLFileNameByErpId(int erpId);
    }
}
