﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeTimeZone
{
    public int TimeZoneId { get; set; }

    public string TimeZoneDetailsCode { get; set; } = null!;

    public string? TimeZoneDetailsDesc { get; set; }

    public int DifferenceInSeconds { get; set; }

    public string? DaylightBeginsAt { get; set; }

    public string? DaylightEndsAt { get; set; }

    public int? DstinSeconds { get; set; }

    public bool IsDefault { get; set; }

    public bool? IsActive { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }
}
