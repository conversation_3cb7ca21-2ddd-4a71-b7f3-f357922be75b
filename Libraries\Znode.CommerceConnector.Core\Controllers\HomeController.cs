﻿using Microsoft.AspNetCore.Mvc;
using System.Net;

namespace Znode.CommerceConnector.Core.Controllers
{
    public class HomeController : Controller
    {
        [Route("Home/Error")]
        public IActionResult Error()
        {
            return View(); 
        }

        [Route("Home/PageNotFound/{statusCode}")]
        public IActionResult PageNotFound(int statusCode)
        {
            if(statusCode == (int)HttpStatusCode.Unauthorized)
            {
                return View("UnAuthorizedRequest");
            }
            return View();
        }
    }
}
