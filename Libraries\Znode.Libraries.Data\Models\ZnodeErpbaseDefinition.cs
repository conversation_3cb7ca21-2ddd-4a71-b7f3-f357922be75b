﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeErpbaseDefinition
{
    public int ErpbaseDefinitionId { get; set; }

    public string? Name { get; set; }

    public string? Format { get; set; }

    public string? Version { get; set; }

    public string? DataSource { get; set; }

    public string? DataDestination { get; set; }

    public string? TriggerOrigin { get; set; }

    public string? Description { get; set; }

    public string? Tags { get; set; }

    public string? Access { get; set; }

    public string? Library { get; set; }

    public string? Collection { get; set; }

    public string? ProcessorFileName { get; set; }

    public string? MethodName { get; set; }

    public bool IsCustomProcessingRequired { get; set; }

    public int? DataExchangeLibraryId { get; set; }

    public string? YamlfileName { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeDataExchangeLibrary? DataExchangeLibrary { get; set; }

    public virtual ICollection<ZnodeErpbaseDefinitionScheduler> ZnodeErpbaseDefinitionSchedulers { get; set; } = new List<ZnodeErpbaseDefinitionScheduler>();

    public virtual ICollection<ZnodeErpdataExchangeDataLog> ZnodeErpdataExchangeDataLogs { get; set; } = new List<ZnodeErpdataExchangeDataLog>();

    public virtual ZnodeErpdataExchangeStatus? ZnodeErpdataExchangeStatus { get; set; }

    public virtual ICollection<ZnodeImportDataExchangeLog> ZnodeImportDataExchangeLogs { get; set; } = new List<ZnodeImportDataExchangeLog>();

    public virtual ICollection<ZnodeImportDataExchangeProcessLog> ZnodeImportDataExchangeProcessLogs { get; set; } = new List<ZnodeImportDataExchangeProcessLog>();

    public virtual ZnodeTransmissionConfiguration? ZnodeTransmissionConfiguration { get; set; }
}
