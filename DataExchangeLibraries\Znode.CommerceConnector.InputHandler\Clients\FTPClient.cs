﻿using System.Diagnostics;
using System.IO;
using System.Net;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler.Clients
{
    public class FTPClient : IFTPClient
    {
        public byte[] DownloadData<T>(int erpId, int logId, FTPConfigurationModel fTPConfigurationModel, bool returnWithoutDeserialize = false)
        {
            MongoLogging.LogMessage(Constants.DownloadDataCalledinFTPClient, Constants.FTPClient, TraceLevel.Info);
            string host = fTPConfigurationModel?.ServerAddress.Trim();
            string username = fTPConfigurationModel?.UserName.Trim();
            string password = fTPConfigurationModel?.Password.Trim();
            string remoteFolder = fTPConfigurationModel?.FolderPath.Trim('/');
            string fileName = fTPConfigurationModel?.FileName.Trim();
            ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            try
            {
                string ftpUrl = $"ftp://{host}/{remoteFolder}/{fileName}";

                MongoLogging.LogMessage(Constants.FTPUrlinFTPClient + ftpUrl, Constants.FTPClient, TraceLevel.Info);

                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
                request.Method = WebRequestMethods.Ftp.DownloadFile;
                request.Credentials = new NetworkCredential(username, password);
                request.UseBinary = true;
                request.UsePassive = true;
                request.EnableSsl = true;
                request.KeepAlive = false;

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                using (Stream responseStream = response.GetResponseStream())
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    responseStream.CopyTo(memoryStream);
                    return memoryStream.ToArray();
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.DownloadDataInFTPClientFailed + ex.Message, Constants.FTPClient, TraceLevel.Info);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.DownloadDataInFTPClientFailed + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.FTPClient , TraceLevel.Error); 
                return new byte[0];
            }
        }
    }
}
