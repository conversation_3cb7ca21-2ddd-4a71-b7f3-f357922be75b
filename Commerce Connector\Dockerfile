# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

# This stage is used when running from VS in fast mode (Default for Debug configuration)
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443


# This stage is used to build the service project
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# ----- STEP 1: Install Node.js -----
# Install curl for downloading Node setup script
RUN apt-get update && apt-get install -y curl

# Install Node.js 18 (adjust version as needed)
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    node -v && npm -v

# ----- STEP 2: Copy package files and install Node dependencies -----
# Copy only package.json and package-lock.json for better Docker caching
COPY ["package.json", "package-lock.json", "./"]

# Install npm packages (includes jQuery and @types/jquery)
RUN npm install


COPY ["../NuGet.Znode.Base.Config", "nuget.config"]
COPY . .
RUN dotnet restore "./Commerce Connector/Znode.Engine.CommerceConnector.csproj"


WORKDIR "/src/Commerce Connector"
RUN dotnet build "./Znode.Engine.CommerceConnector.csproj" -c $BUILD_CONFIGURATION -o /app/build

# This stage is used to publish the service project to be copied to the final stage
FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Znode.Engine.CommerceConnector.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

# This stage is used in production or when running from VS in regular mode (Default when not using the Debug configuration)
FROM base AS final
COPY ["../docker.internal.pfx", "../app"]
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "Znode.Engine.CommerceConnector.dll"]