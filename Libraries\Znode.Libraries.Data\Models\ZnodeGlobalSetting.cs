﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeGlobalSetting
{
    public int ZnodeGlobalSettingId { get; set; }

    public string FeatureName { get; set; } = null!;

    public string? FeatureValues { get; set; }

    public string? FeatureSubValues { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }
}
