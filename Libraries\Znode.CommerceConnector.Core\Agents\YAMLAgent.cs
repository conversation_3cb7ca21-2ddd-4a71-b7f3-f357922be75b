﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Models;

namespace Znode.CommerceConnector.Core.Agents
{
    public class YAMLAgent : IYAMLAgent
    {
        private readonly IYAMLClient _yAMLClient;
        private readonly IMapper _mapper;

        public YAMLAgent(IYAMLClient yAMLClient, IMapper mapper)
        {
            _yAMLClient = yAMLClient;
            _mapper = mapper;
        }

        public dynamic DownloadSample()
        {
            MediaConfigurationModel mediaConfiguration = _yAMLClient.GetMediaConfiguration();
            dynamic result = _yAMLClient.DownloadSample(mediaConfiguration);
            return result;
        }

        public dynamic UploadYaml(IFormFile yamlFile)
        {
            using var reader = new StreamReader(yamlFile.OpenReadStream());
            var content = reader.ReadToEnd(); ;
            return content;
        }

        public dynamic SaveYaml(YAMLSaveRequestModel content)
        {
            MediaConfigurationModel model = _yAMLClient.GetMediaConfiguration();
            content.MediaConfigurationModel = _mapper.Map<MediaConfigurationModel>(model);

            bool isFileSaved = _yAMLClient.SaveYaml(content);
            return isFileSaved;
        }

        public dynamic GetSavedYamlData(int erpId)
        {
            MediaConfigurationModel mediaConfiguration = _yAMLClient.GetMediaConfiguration();
            dynamic savedFile = _yAMLClient.GetSavedYamlData(erpId, mediaConfiguration);
            return savedFile;
        }
    }
}
