﻿using Azure.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Routing;
using Newtonsoft.Json;
using System.Diagnostics;
using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.Models.WebGridModels;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.Libraries.Common.Logger;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Core.Controllers
{
    [Route("ManageDataExchange")]
    public class ManageDataExchangeController : Controller
    {
        private readonly IManageDataExchangeAgent _agent;
        private readonly ICCDataExchangeAgent _exchangeAgent;

        public ManageDataExchangeController(IManageDataExchangeAgent agent, ICCDataExchangeAgent exchangeAgent)
        {
            _agent = agent;
            _exchangeAgent = exchangeAgent;
        }

        /// <summary>
        /// Get Data Exchanges Details based on the ERP ID.
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("ManageDataExchangedetails/{id}", Name = "GetDataExchangeDetails")]
        public IActionResult ManageDataExchangedetails(int id)
        {
            var token = Request.Query[CCAdminConstant.CCAdminToken].ToString();

            if(string.IsNullOrEmpty(token))
            { 
                string referer = Request.Headers["Referer"].ToString();

                if (!string.IsNullOrWhiteSpace(referer) && referer.Contains(CCAdminConstant.CCAdminToken))
                {
                    Uri refererUri = new Uri(referer);
                    var queryParams = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(refererUri.Query);

                    if (queryParams.TryGetValue(CCAdminConstant.CCAdminToken, out var tokenRequest))
                    {
                        token = tokenRequest.ToString();
                    }
                }
            }
            token = Convert.ToString(TempData[CCAdminConstant.CCAdminToken]);

            if (!Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
            {
                Request.Headers.Add(CCAdminConstant.CCAdminToken, token);
            }
            BaseDefinitionViewModel baseDefinitionViewModel;
            var json = HttpContext.Session.GetString("ErrorModel");
            if (!string.IsNullOrEmpty(json))
            {
                baseDefinitionViewModel = JsonConvert.DeserializeObject<BaseDefinitionViewModel>(json);
                HttpContext.Session.Remove("ErrorModel"); 
            }
            else
            {
                baseDefinitionViewModel = _agent.GetDataExchangeDetailsByID(id);
            }
            return View(baseDefinitionViewModel);
        }

        /// <summary>
        /// Save Data Exchange Details against the ERP Id.
        /// </summary>
        /// <param name="id"></param>
        /// <param name="model"></param>
        /// <returns></returns>        
        [HttpPost("ManageDataExchange/ManageDataExchangedetails/{id}")]
        public IActionResult ManageDataExchangedetails(int id, BaseDefinitionViewModel baseDefinitionViewModel)
        {
            var token = Request.Query[CCAdminConstant.CCAdminToken].ToString();
           
            if (!Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
            {
                Request.Headers.Append(CCAdminConstant.CCAdminToken, token);
            }
            else
                token = Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();


            baseDefinitionViewModel.ERPBaseDefinitionId = id;
            bool isSaved = false;
            bool isValid = true;

            var scheduler = baseDefinitionViewModel.SchedulerConfigurationViewModel;
            var transmission = baseDefinitionViewModel.TransmissionConfigurations;

            scheduler = _agent.CheckSchedulerNameValidation(scheduler, out isValid);
            baseDefinitionViewModel.SchedulerConfigurationViewModel = scheduler;
            // Validate schedule config if needed
            if (isValid)
            {
                if (scheduler.SchedulerType == CCAdminConstant.Scheduled)
                {
                    scheduler = _agent.CheckValidation(scheduler, out isValid);
                    baseDefinitionViewModel.SchedulerConfigurationViewModel = scheduler;

                    if (scheduler.SchedulerFrequency == CCAdminConstant.OneTime)
                    {
                        RemoveModelStateKeys("SchedulerConfigurationViewModel.CronExpression");
                    }
                    else
                    {
                        RemoveModelStateKeys(
                            "SchedulerConfigurationViewModel.StartTime",
                            "SchedulerConfigurationViewModel.StartDate"
                        );
                    }
                }
                else if (scheduler.SchedulerType == CCAdminConstant.OnDemand || scheduler.SchedulerType == CCAdminConstant.RealTime)
                {
                    RemoveModelStateKeys(
                        "SchedulerConfigurationViewModel.CronExpression",
                        "SchedulerConfigurationViewModel.StartTime",
                        "SchedulerConfigurationViewModel.StartDate",
                        "TransmissionConfigurations.TransmissionModeDestination"
                    );
                }
            }

            // Remove invalid HTTPS config key/value pairs
            RemoveInvalidKeyValueEntries(transmission?.HttpsConfig?.QueryParams, "TransmissionConfigurations.HttpsConfig.QueryParams");
            RemoveInvalidKeyValueEntries(transmission?.HttpsConfig?.HeaderParams, "TransmissionConfigurations.HttpsConfig.HeaderParams");
            RemoveInvalidKeyValueEntries(transmission?.HttpsConfigDestination?.QueryParams, "TransmissionConfigurations.HttpsConfigDestination.QueryParams");
            RemoveInvalidKeyValueEntries(transmission?.HttpsConfigDestination?.HeaderParams, "TransmissionConfigurations.HttpsConfigDestination.HeaderParams");

            if (ModelState.IsValid && isValid)
            {
                isSaved = _agent.SaveDataExchangeDetails(baseDefinitionViewModel);
            }
            TempData[CCAdminConstant.CCAdminToken] = token;

            if (isSaved)
            {
                TempData["SuccessMessage"] = CCAdminConstant.SuccessDataExchange;
                return RedirectToAction("GetDataExchangeLibraryList", "DataExchange");
            }

            TempData["ErrorMessage"] = scheduler.HasError ? scheduler.ErrorMessage : CCAdminConstant.FailedDataExchange;
            baseDefinitionViewModel.SchedulerConfigurationViewModel.ScheduleFrequencyList = new List<SelectListItem>();
            baseDefinitionViewModel.SchedulerConfigurationViewModel.ScheduleTypeList = new List<SelectListItem>();
            baseDefinitionViewModel.TransmissionConfigurations.TransmissionModeOptions = new List<SelectListItem>();
            HttpContext.Session.SetString("ErrorModel", JsonConvert.SerializeObject(baseDefinitionViewModel));
            return RedirectToRoute("GetDataExchangeDetails", new { id = baseDefinitionViewModel.ERPBaseDefinitionId });
        }

        private void RemoveModelStateKeys(params string[] keys)
        {
            foreach (var key in keys)
            {
                ModelState.Remove(key);
                ModelState.Remove($"baseDefinitionViewModel.{key}");
            }
        }

        private void RemoveInvalidKeyValueEntries(List<KeyValueViewModel>? items, string prefix)
        {
            if (items == null) return;

            for (int i = items.Count - 1; i >= 0; i--)
            {
                if (string.IsNullOrWhiteSpace(items[i].Key) || string.IsNullOrWhiteSpace(items[i].Value))
                {
                    ModelState.Remove($"{prefix}[{i}].Key");
                    ModelState.Remove($"{prefix}[{i}].Value");
                    items.RemoveAt(i);
                }
            }
        }

        /// <summary>
        /// Get Data Exchanges Details based on the ERP ID.
        /// </summary>
        /// <param name="erpId"></param>
        /// <returns></returns>
        [HttpGet("GetBaseDefinitionDetailsByID/{erpId}")]
        public IActionResult GetBaseDefinitionDetailsByID(int erpId)
        {
            BaseDefinitionViewModel baseDefinitionViewModel = _agent.GetBaseDefinitionDetailsByID(erpId);
            ViewBag.isFromQuickView = true;
            return PartialView("_BaseDefinitionDetails", baseDefinitionViewModel);
        }

        [HttpGet("TestConnection")]
        public JsonResult TestConnection(string mode, string request)
        {
            bool status = false;
            if (!string.IsNullOrWhiteSpace(request))
            {
                status = _agent.TestConnection(mode, request);
            }
            return Json(new { status });
        }


        [HttpGet("GetConfigurationChangeLogList/{id}")]
        public virtual ActionResult GetConfigurationChangeLogList(int id, [FromQuery] string exchangeName)
        {
            ConfigurationChangeLogListViewModel viewModel = new ConfigurationChangeLogListViewModel();
            viewModel.ERPBaseDefinitionId = id;
            viewModel.ERPExchangeName = exchangeName;
            return View("_ConfigurationChangeLog", viewModel);
        }

        /// <summary>
        /// To render the dynamic grid.
        /// </summary>
        /// <param name="request">grid request model.</param>
        /// <returns>Json data</returns>
        [HttpPost("/ManageDataExchange/RenderConfigurationChangeGrid/{id}")]
        public IActionResult RenderConfigurationChangeGrid([FromBody] GridModel request, [FromRoute] int id)
        {
            if (HelperUtility.IsNotNull(request))
            {
                MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.ConfigurationChangeLogMethodStart, TraceLevel.Info);
                ConfigurationChangeLogListViewModel dataExchangeModel = _agent.GetConfigurationChangeList(request, id);
               
                GridResponseModel result = GridHelper.GenerateDynamicGrid(dataExchangeModel.ConfigurationChangeLogList, request);
                MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.ConfigurationChangeLogMethodEnd, TraceLevel.Info);
                return Json(new
                {
                    rows = result.Data,
                    columns = result.Columns,
                    totalCount = dataExchangeModel.TotalRows,
                    page = request.Page,
                    pageSize = request.PageSize,
                    filteredCount = result.FilteredCount,
                    pageCount = (int)Math.Ceiling((double)dataExchangeModel.TotalRows / (double)request.PageSize)
                });
            }
            return Json(new { rows = new GridResponseModel().Data });
        }


        [HttpGet]
        [HttpGet("AddDataExchange")]
        public virtual ActionResult AddDataExchange()
        {
            BaseDefinitionViewModel model = new BaseDefinitionViewModel();
            ViewBag.isFromAddNew = true;
            return View("ManageDataExchangedetails", model);
        }

        [HttpPost("CreateEditDataExchange")]
        public IActionResult CreateEditDataExchange(BaseDefinitionViewModel baseDefinitionViewModel)
        {
            bool status = false;
            int erpId = 0;
            string token = HttpContext.Request.Query[CCAdminConstant.CCAdminToken];

            erpId = _exchangeAgent.CreateEditBaseDataExchange(baseDefinitionViewModel);
            if (erpId > 0)
            {

                TempData["SuccessMessage"] = CCAdminConstant.SuccessDataExchange;
                var routeValues = new RouteValueDictionary
                {
                    { "id", erpId },
                    { CCAdminConstant.CCAdminToken, token }
                };

                return RedirectToRoute("GetDataExchangeDetails", routeValues);
            }
            else
            {
                ViewBag.isFromAddNew = true;
                TempData["ErrorMessage"] = CCAdminConstant.ProcessorFileAlreadyExist;
                return View("ManageDataExchangedetails", baseDefinitionViewModel);
            }
        }
    }
}
