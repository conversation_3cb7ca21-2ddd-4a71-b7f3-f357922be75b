﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Znode.CommerceConnector.Parser.Helper;

namespace Znode.CommerceConnector.Parser
{
    public static class CustomDependencyRegistration
    {
        public static void RegisterParserDI(this WebApplicationBuilder builder)
        {
            builder.Services.AddTransient<IParser, Parser>();
            builder.Services.AddTransient<ParserDataHelper>();           
        }
    }
}
