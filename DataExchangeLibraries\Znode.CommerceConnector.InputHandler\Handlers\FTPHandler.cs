﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class FTPHandler : IFTPHandler
    {
        IFTPClient _FTPClient;
        IHandlerDataHelper _handlerDataHelper;

        public FTPHandler(IFTPClient FTPClient, IHandlerDataHelper handlerDataHelper)
        {
            _FTPClient = FTPClient;
            _handlerDataHelper = handlerDataHelper;
        }

        // call FTP client
        public string FTPDataHandler(int erpId, int logId)
        {
            try
            {
                MongoLogging.LogMessage("FTPDataHandler method in FTPDataHandler called", "FTPDataHandler", TraceLevel.Info);
                FTPConfigurationModel FTPConfigurationModel = GetInputHandlerFTPCredentials(erpId);

                byte[] file = _FTPClient.DownloadData<bool>(erpId, logId, FTPConfigurationModel, false);

                //Parse csv data and save in temp table
                string tempTableName = ParseCSVData(file, erpId);
                return tempTableName;
            }
            catch (Exception ex)
            {
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.FTPHandlerFailed + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.FTPDataHandler, TraceLevel.Error);
                return string.Empty;
            }
        }

        public FTPConfigurationModel GetInputHandlerFTPCredentials(int erpId)
        {
            FTPConfigurationModel FTPConfigurationModel = GetFTPCredentials(erpId);
            return FTPConfigurationModel;
        }

        public FTPConfigurationModel GetFTPCredentials(int erpId)
        {
            List<FTPConfigurationModel> FTPCredentials = new List<FTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPCredentialsByERPId", sqlParameters);
            List<FTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<FTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }

        public string ParseCSVData(byte[] file, int erpId)
        {
            using (var memoryStream = new MemoryStream(file))
            using (var reader = new StreamReader(memoryStream))
            {
                DataTable dataTable = LoadCsvFromStream(reader);
                DataSet csvDataSet = new DataSet();
                csvDataSet.Tables.Add(dataTable);

                List<string> tempTableName = _handlerDataHelper.CreateTempTableWithData(csvDataSet);
                return tempTableName?.FirstOrDefault();
            }
        }

        private DataTable LoadCsvFromStream(TextReader reader)
        {
            DataTable dt = new DataTable();
            bool headersRead = false;

            string? line;
            while ((line = reader.ReadLine()) != null)
            {
                string[] values = line.Split(',');

                if (!headersRead)
                {
                    foreach (var header in values)
                        dt.Columns.Add(header.Trim());

                    headersRead = true;
                }
                else
                {
                    dt.Rows.Add(values);
                }
            }

            return dt;
        }
    }
}
