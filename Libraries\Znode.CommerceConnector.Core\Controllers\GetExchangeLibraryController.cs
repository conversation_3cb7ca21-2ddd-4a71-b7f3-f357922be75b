﻿using Microsoft.AspNetCore.Mvc;
using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.Models.WebGridModels;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.Libraries.ECommerce.Utilities;
using Znode.Libraries.Resources.CommerceConnector_Resources;

namespace Znode.CommerceConnector.Core
{
    public class GetExchangeLibraryController : Controller
    {
        private readonly IGetExchangeLibrariesAgent _getExchangeLibrariesAgent;

        public GetExchangeLibraryController(IGetExchangeLibrariesAgent getExchangeLibrariesAgent)
        {
            _getExchangeLibrariesAgent = getExchangeLibrariesAgent;
        }

        /// <summary>
        /// Render view of GetAvailableLibraries
        /// </summary>
        /// <returns>View</returns>
        [HttpGet]
        public IActionResult GetAvailableLibraries()
        => View();

        /// <summary>
        /// To render the dynamic grid.
        /// </summary>
        /// <param name="request">grid request model.</param>
        /// <returns>Json data</returns>
        [HttpPost]
        public IActionResult RenderLibraryGrid([FromBody] GridModel request)
        {
            if (HelperUtility.IsNotNull(request))
            {
                DataExchangeListViewModel librariesList = _getExchangeLibrariesAgent.GetLibrarieslist(request);
                GridResponseModel result = GridHelper.GenerateDynamicGrid(librariesList.DataExchangeList, request);
                return Json(new
                {
                    rows = result.Data,
                    columns = result.Columns,
                    totalCount = librariesList.TotalRows,
                    page = request.Page,
                    pageSize = request.PageSize,
                    filteredCount = result.FilteredCount,
                    pageCount = (int)Math.Ceiling((double)librariesList.TotalRows / (double)request.PageSize)
                });
            }
            return Json(new { rows = new GridResponseModel().Data });
        }

        [HttpPost]
        public virtual JsonResult AddSelectedExchange([FromBody]AddSelectedExchangeViewModel addSelectedExchangeViewModel)
        {
            if (addSelectedExchangeViewModel.SelectedIds?.Count > 0)
            {
                string message = string.Empty;
                bool status = _getExchangeLibrariesAgent.AddSelectedExchange(addSelectedExchangeViewModel, out message);
                return Json(new { status = status, message = status ? CommerceConnector_Resources.AddSelectedSuccessMessage : string.IsNullOrEmpty(message) ? CommerceConnector_Resources.AddSelectedErrorMessage: message });
            }
            return Json(new { status = false, message = CommerceConnectorConstants.NoIdsSelectedMessage });
        }

        public virtual JsonResult ValidateExchangeName([FromBody] string exchangeName)
        {
            if (!string.IsNullOrWhiteSpace(exchangeName))
            {
                string message;
                bool status = _getExchangeLibrariesAgent.ValidateExchangeName(exchangeName, out message);
                return Json(new { status, message });
            }
            return Json(new { status = false , message = CommerceConnector_Resources.ExchangeNameRequiredError });
        }
    }
}