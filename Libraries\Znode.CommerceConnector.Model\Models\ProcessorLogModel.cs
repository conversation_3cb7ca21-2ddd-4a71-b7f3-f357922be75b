﻿namespace Znode.CommerceConnector.Model
{
    public class ProcessorLogModel : BaseModel
    {
        public int ImportDataExchangeProcessLogId { get; set; }
        public int ERPBaseDefinitionId { get; set; }
        public DateTime? ProcessStartedDate { get; set; }
        public DateTime? ProcessCompletedDate { get; set; }
        public string StatusName { get; set; }
        public string TemplateName { get; set; }
        public string ImportName { get; set; }
        public int SuccessRecordCount { get; set; }
        public int FailedRecordcount { get; set; }
        public int TotalProcessedRecords { get; set; }
    }
}
