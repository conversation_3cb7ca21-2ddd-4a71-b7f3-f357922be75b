﻿namespace Znode.CommerceConnector.Core.ViewModels
{
    public class BaseDefinitionViewModel
    {
        public int ERPBaseDefinitionId { get; set; }
        public int DataExchangeLibraryId { get; set; }
        public string? Name { get; set; }
        public string? Format { get; set; }
        public string? Version { get; set; }
        public string? DataSource { get; set; }
        public string? DataDestination { get; set; }
        public string? TriggerOrigin { get; set; }
        public string? Description { get; set; }
        public string? Tags { get; set; }
        public string? Access { get; set; }
        public string? Library { get; set; }
        public string? Collection { get; set; }
        public string? ProcessorFileName { get; set; } = "DefaultProcessor";
        public string? MethodName { get; set; }
        public string? ConfigurationChangeJsonData { get; set; }
        public bool IsCustomProcessingRequired { get; set; } = false;
        public TransmissionConfigurationViewModel? TransmissionConfigurations { get; set; }
        public SchedulerConfigurationViewModel? SchedulerConfigurationViewModel { get; set; }
        public   AccessDropdownViewModel? accessDropdownModel { get; set; }

        public BaseDefinitionViewModel()
        {
            SchedulerConfigurationViewModel = new SchedulerConfigurationViewModel();
            accessDropdownModel = new AccessDropdownViewModel();
            StatusActivationViewModel = new StatusActivationViewModel();
        }
        public StatusActivationViewModel? StatusActivationViewModel { get; set; }
    }

}

