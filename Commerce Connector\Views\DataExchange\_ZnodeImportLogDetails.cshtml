﻿@using Znode.Libraries.Resources.CommerceConnector_Resources

<table class="table import-grid-table table-bordered w-25 my-2" aria-describedby="Log Details Table" data-test-selector="tblImportGridTable">
    <tbody>
        <tr>
            <td class="import-field fw-bold" data-test-selector="colProcessedRecords">@CommerceConnector_Resources.LabelTotalProcessedRecords</td>
            <td id="totalRecordsField" class="import-value fw-bold pl-2" data-test-selector="colProcessedRecordsValue"></td>
        </tr>
        <tr>
            <td class="import-field" data-test-selector="colSucceededRecords">@CommerceConnector_Resources.LabelSucceededRecords</td>
            <td id="succeededRecordsField" class="import-value pl-2" data-test-selector="colSucceededRecordsValue"></td>
        </tr>
        <tr>
            <td class="import-field" data-test-selector="colFailedRecords">@CommerceConnector_Resources.LabelFailedRecords</td>
            <td id="failedRecordsField" class="import-value pl-2" data-test-selector="colFailedRecordsValue"></td>
        </tr>
    </tbody>
</table>

<div class="controls">
    <div class="filter-search d-flex">
        <input type="text" id="importLogsGlobalSearch" maxlength="130" placeholder="Search..." data-test-selector="txtSearch" aria-label="Search Processing Log List" />
        <button id="importLogsGlobalSearchbtn" class="btn-search" aria-label="Processing Log List" data-test-selector="btnSearchIcon"><em class="z-search"></em></button>
    </div>
</div>
<div id="importLogDetailsGrid" class="ag-theme-alpine mt-3" data-test-selector="divImportLogDetailsGrid"></div>
<div id="logsShowPerPage" class="show-per-page d-flex mt-3" style="display: none;">
    <div id="importLogsPaginationControls" class="me-3" data-test-selector="divPaginationControl"></div>
    <div id="logsCustomRowInfo" class="total-record" data-test-selector="divCustomRowInfo" style="display: none;"></div>
    <div id="logsShowPerCount" class="show-page-count ps-3" style="display: none;">
        <div class="d-flex align-items-center">
            <label class="pe-3" data-test-selector="lblShow">@CommerceConnector_Resources.Show</label>
            <select id="importLogPageSizeSelect" data-test-selector="drpPageSizeSelect" aria-label="Select Number of Page">
                <option value="10" selected>10</option>
                <option value="25">25</option>
                <option value="50">50</option>
            </select>
        </div>
    </div>
</div>
<div id="logsNoRecordsMessage" class="text-center mt-3 font-weight-bold" style="display: none;" data-test-selector="divNoRecordsFound">
    @CommerceConnector_Resources.NoRecordsFound
</div>
@Html.Hidden("processLogId")