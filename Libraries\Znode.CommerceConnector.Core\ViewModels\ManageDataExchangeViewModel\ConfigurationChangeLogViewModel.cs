﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using Znode.CommerceConnector.Core.Helper;

using Znode.Libraries.Resources.CommerceConnector_Resources;

namespace Znode.CommerceConnector.Core
{
    public class ConfigurationChangeLogViewModel
    {
        public int? ConfigurationLogID { get; set; }

        [Display(Name = CCAdminConstant.FieldName, ResourceType = typeof(CommerceConnector_Resources))]
        public string? FieldName { get; set; }
        [Display(Name = CCAdminConstant.FieldOldValue, ResourceType = typeof(CommerceConnector_Resources))]

        public string? OldValue { get; set; }
        [Display(Name = CCAdminConstant.FieldNewValue, ResourceType = typeof(CommerceConnector_Resources))]
        public string? NewValue { get; set; }

        [Display(Name = CCAdminConstant.FieldDateChange, ResourceType = typeof(CommerceConnector_Resources))]
        public DateTime? ChangeDateTime { get; set; }

        [Display(Name = CCAdminConstant.UserName, ResourceType = typeof(CommerceConnector_Resources))]
        public string? UserName { get; set; }
    }
}
