﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="2.8.2" />
    <PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="2.8.2" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Libraries\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
    <ProjectReference Include="..\..\Libraries\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
    <ProjectReference Include="..\..\Libraries\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj" />
    <ProjectReference Include="..\..\Znode.CommerceConnector.Parser\Znode.CommerceConnector.Parser.csproj" />
    <ProjectReference Include="..\Znode.CommerceConnector.InputHandler\Znode.CommerceConnector.InputHandler.csproj" />
    <ProjectReference Include="..\Znode.CommerceConnector.OutputHandler\Znode.CommerceConnector.OutputHandler.csproj" />
  </ItemGroup>

</Project>
