﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using System.Net;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Model.Models;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Models;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.OutputHandler
{
    public class APIHandler : IAPIHandler
    {
        IAPIClient _apiClient;

        public APIHandler(IAPIClient apiClient)
        {
            _apiClient = apiClient;
        }

        public dynamic APIOutputDataHandler(string data, int erpId, dynamic requestBody, WebHeaderCollection headers, ProcessorDetails processorDetails, dynamic outputConfiguration, dynamic inputModel, bool isRealTime=false)
        {
            dynamic result = false;
            bool status = false;

            MongoLogging.LogMessage(Constants.APIOutputDataHandlerMethodCalled, Constants.APIHandler, TraceLevel.Info);

            result = _apiClient.SendData<dynamic>(erpId, processorDetails.LogId, outputConfiguration, data, requestBody, headers,inputModel, isRealTime);

            if (result.StatusCode == HttpStatusCode.Accepted.ToString() || result.StatusCode == HttpStatusCode.Created.ToString() || result.StatusCode == HttpStatusCode.Found.ToString() || result.StatusCode == HttpStatusCode.OK.ToString())
            {
                status = true;
            }
            else
            {
                status = false;
            }
            
            bool recordsUpdated = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, data, status);
            if (!recordsUpdated)
                MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.APIOutputDataHandler, TraceLevel.Error);

            if (!isRealTime)
                return status;

            return result.Data;
        }
    }
}
