﻿ @using Znode.Libraries.Resources.CommerceConnector_Resources

@{
    Layout = "_Layout"; // Or your default layout
}

<div class="d-flex justify-content-center align-items-center full-height mt-5">
    <div class="error-message text-center">
        <h1 data-test-selector="hdgError">@CommerceConnector_Resources.Error</h1>

        <p class="mb-2" data-test-selector="paraErrorText">@CommerceConnector_Resources.ErrorWhileProcessingRequest</p>

        <p data-test-selector="paraTryAgainLater">@CommerceConnector_Resources.TryAgainLater</p>

        @if (ViewBag.ErrorMessage != null)
        {
            <p data-test-selector="paraErrorMessage">@CommerceConnector_Resources.ErrorMessage: @ViewBag.ErrorMessage</p>
        }
    </div>
</div>