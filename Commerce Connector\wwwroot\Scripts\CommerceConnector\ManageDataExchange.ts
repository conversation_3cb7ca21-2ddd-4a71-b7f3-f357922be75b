﻿let editor;
let yamlContent: string = '';
const resources = {
    ServerAddressRequired: 'Server Address is required.',
    UsernameRequired: 'Username is required.',
    PasswordRequired: 'Password is required.',
    PortNumberRequired: 'Port Number is required.',
    HTTPActionRequired: 'API Action is required.',
    EndpointRequired: 'Endpoint is required.',
    ExchangeNameError:'Exchange name is required.',
    DataSourceError:'Source is required.',
    SpecialCharactersRegex: 'Special characters are not allowed.',
    VersionErrorRegEx:'Version must be a valid number (e.g., 1, 2.0, 3.45).',
    VersionError:'Version is required.',
    VersionGreaterThanOneError: 'Version must be greater than or equal to 1.',
    VersionIntegerPartError: 'Version must be a number with at most two digits.',
    VersionDecimalPartError: 'Version is invalid. Only two decimal places are allowed.',
    ProcessorFileNameRegEX: 'Processor File Name is invalid.',
    ProcessorFileNameError: 'Processor File Name is required.',
    DataDestinationError: 'Destination is required.',
    TriggerOriginError: 'Trigger Origin is required.',
    AuthTypeError: 'Authentication type is required.',
    APIActionError: 'API Action is required.',
    TransmissionModeError: 'Transmission mode is required.',
    NotificationError: 'Invalid email in Error Notification.',
    InformationError: 'Invalid email in Information Notification.',
    WarningError: 'Invalid email in Warning Notification.',
    RetrievalActionError: 'Retrieval Action is required.',
    FileFormatError: 'Format is required.',
    ManageDataExchangeError: 'Required fields are missing in configurations.',
    YAMLOldValue: 'YAML Old Mapping',
    YAMLNewValue: 'YAML is updated to New Mapping',
    YAMLFieldName: 'YAML',
    SFTPHandler: 'SFTPHandler',
    APIHandler: 'APIHandler',
    SFTPValue: 'SFTP',
    APIValue: 'API',
    CronRequiredError: 'Cron Expression is required.',
    CronErrorRegex: 'Enter a valid cron expression.'

};

const ParameterConfig = {
    source: {
        QueryParams: {
            indexInputId: "sourceQueryIndex",
            rowPrefix: "source-query-row",
            bodyId: "queryParamBodySource",
            inputNamePrefix: "TransmissionConfigurations.HttpsConfig.QueryParams",
            dataSelector: "txtQueryParams",
            spanId: "SourceQueryParameterError",
            headerId: "sourceQueryParamHeader"   
        },
        HeaderParams: {
            indexInputId: "sourceHeaderIndex",
            rowPrefix: "source-header-row",
            bodyId: "headerParamBodySource",
            inputNamePrefix: "TransmissionConfigurations.HttpsConfig.HeaderParams",
            dataSelector: "txtHeaderParams",
            spanId: "SourceHeaderParameterError",
            headerId: "sourceHeaderParamHeader"   
        }
    },
    destination: {
        QueryParams: {
            indexInputId: "destinationQueryIndex",
            rowPrefix: "destination-query-row",
            bodyId: "queryParamBodyDestination",
            inputNamePrefix: "TransmissionConfigurations.HttpsConfigDestination.QueryParams",
            dataSelector: "txtQueryParams",
            spanId: "destinationQueryParameterError",
            headerId: "destinationQueryParamHeader"   
        },
        HeaderParams: {
            indexInputId: "destinationHeaderIndex",
            rowPrefix: "destination-header-row",
            bodyId: "headerParamBodyDestination",
            inputNamePrefix: "TransmissionConfigurations.HttpsConfigDestination.HeaderParams",
            dataSelector: "txtHeaderParams",
            spanId: "destinationHeaderParameterError",
            headerId: "destinationHeaderParamHeader"   
        }
    }
};

class ManageDataExchange {

    public init(): void {
        $(document).ready(() => {
            let erpID = $("#erpID").val();
            if (Number(erpID) <= 0) {
                $("#erpExchangeForm").addClass("d-none");
                $("#noDataMessageForErpExchangeForm").removeClass("d-none");
            }
            const sourceDropdown = $("#transmissionSourceDropdown");
            const destDropdown = $("#transmissionDestinationDropdown");
            const sourceAPIDropdown = $("#SourceDropdownAuthType");
            const destAPIDropdown = $("#DestDropdownAuthType");
            if (sourceDropdown.length) {
                this.handleTransmissionSourceChange(sourceDropdown);
                sourceDropdown.on("change", () => this.handleTransmissionSourceChange(sourceDropdown));
            }

            if (destDropdown.length) {
                this.handleTransmissionDestinationChange(destDropdown);
                destDropdown.on("change", () => this.handleTransmissionDestinationChange(destDropdown));
            }

            if (sourceAPIDropdown.length && sourceDropdown.val() === "APIHandler") {
                this.HandleAuthType("Source", sourceAPIDropdown);
                sourceAPIDropdown.on("change", () => this.HandleAuthType("Source", sourceAPIDropdown));
            }

            if (destAPIDropdown.length && destDropdown.val() === "APIHandler") {
                this.HandleAuthType("Dest", destAPIDropdown);
                destAPIDropdown.on("change", () => this.HandleAuthType("Dest", destAPIDropdown));
            }

            ManageDataExchange.prototype.EnableDisableSettings($('#ScheduleType').val());
            ManageDataExchange.prototype.ToggleFrequency($('#SchedulerFrequency').val());

            $('#ScheduleType').on('change', function () {
                var selectedOption = $(this).find('option:selected');
                ManageDataExchange.prototype.EnableDisableSettings($(selectedOption).val());
            });

            $('input, textarea, select').each(function () {
                const inputValue = $(this).val(); // gets the current value
                $(this).attr('data-oldvalue', String(inputValue)); // sets it as a data attribute

            });

            $('input, textarea, select').on('change', function () {
                $(this).addClass('dirty');
            });

            $('#SchedulerFrequency').on('change', function () {
                var selectedOption = $(this).find('option:selected');
                ManageDataExchange.prototype.ToggleFrequency($(selectedOption).val());
            });

            $("#tableCronExpression").hide();
            $("#toggleCronTable").on("click", function () {
                $("#tableCronExpression").toggle();
            });

            // To restrict characters in Port number field.
            const $input = $('#SourcePortNumber, #DestPortNumber');
            $input.on('keydown', (e: JQuery.KeyDownEvent) => {
                const key = e.key;

                const isDigit = key.length === 1 && /\d/.test(key); // 0–9
                const isAllowed = ['Backspace', 'Tab', 'ArrowLeft', 'ArrowRight', 'Delete'].indexOf(key) !== -1;

                if (!isDigit && !isAllowed) {
                    e.preventDefault();
                }
            });

            $input.on('input', (e: JQuery.TriggeredEvent) => {
                const target = e.target as HTMLInputElement;
                target.value = target.value.replace(/\D/g, '');
            });

            // Trims leading/trailing spaces from textbox/textarea.
            $(document).on("blur", "input[type='text']:not([readonly]):not([disabled]), textarea:not([readonly]):not([disabled])", function () {
                const trimmed = $(this).val()?.toString().trim();
                $(this).val(trimmed);
            });

            // On backspace, error message will be removed.
            $('#schedulerName').on('keydown', (e) => {
                if (e.keyCode === 8) {
                    $('#SchedulerNameError').html('');
                }
            });

            let erpExchangeId = parseInt($("#erpExchangeId").val() as string) || 0;
            let isFromAddNew = $("#isFromAddNew").val() === "true";
            var isReadonly = (erpExchangeId === 0) && !isFromAddNew;
            document.querySelectorAll(".disable-on-condition").forEach(function (el) {
                if (isReadonly) {
                    if ($(el).attr('id') == 'isCustomProcessingRequired') {
                        el.setAttribute('disabled', 'disabled');
                    }
                    el.setAttribute('readonly', 'readonly');
                } else {

                    el.removeAttribute('readonly');
                    el.removeAttribute('disabled');
                }
            });
            

            $('#isCustomProcessingRequired').on('click', (e) => {
                if ($('#isCustomProcessingRequired').is(":checked")) {
                    $('#processorFileName').val('');
                    $('#processorFileName').closest(".row").removeClass('d-none');
                }
                else {
                    $('#processorFileName').closest(".row").addClass('d-none');
                    $('#processorFileName').val('DefaultProcessor');
                }
            });

            if ($('#yamlEditor').length) {

                editor = CodeMirror.fromTextArea(document.getElementById("yamlEditor") as HTMLTextAreaElement, {
                    mode: "yaml",
                    lineNumbers: true,
                });

                editor.on('change', () => {
                    const changeContent = editor.getValue();
                    if (changeContent === yamlContent) {
                        $('.yaml-textarea').removeClass('dirty');
                    } else {
                        $('.yaml-textarea').addClass('dirty');
                    }
                });

                document.getElementById("uploadBtn").addEventListener("click", () => {
                    // Trigger hidden file input click
                    document.getElementById("yamlUpload").click();
                });

                document.getElementById("yamlUpload").addEventListener("change", () => {
                    this.UploadYaml();
                });

                this.GetSavedYamlFile();
            }
        });
    }

    public HandleAuthType(section: "Source" | "Dest", $selectElement: any): void {
        let isValid;
        const selectedValue = $selectElement.val();
        const prefix = `#Div${section}`;
        const dropdownValue = `#${section}DropdownAuthType`;
        const authType = $(dropdownValue).val()?.toString() ?? null;
        const allDivs = [
            `${prefix}Endpoint`,
            `${prefix}GrantType`,
            `${prefix}AccessTokenUrl`,
            `${prefix}ClientId`,
            `${prefix}ClientSecret`,
            `${prefix}Scope`,
            `${prefix}LocationOfCredentials`,
            `${prefix}APIUsername`,
            `${prefix}APIPassword`,
            `${prefix}APIKey`,
            section === "Source" ? "#DivSourceTestConnection" : "#DivDestinationTestConnection"
        ];

        const authTypeToDivsMap: { [key: string]: string[] } = {
            Basic: [`${prefix}Endpoint`, `${prefix}APIUsername`, `${prefix}APIPassword`, `${prefix}APIKey`, section === "Source" ? "#DivSourceTestConnection" : "#DivDestinationTestConnection"],
            OAuth2: [`${prefix}Endpoint`, `${prefix}ClientId`, `${prefix}ClientSecret`, `${prefix}GrantType`, `${prefix}AccessTokenUrl`, `${prefix}Scope`],
            Bearer: [`${prefix}Endpoint`, `${prefix}AccessTokenUrl`, `${prefix}ClientId`, `${prefix}ClientSecret`, `${prefix}GrantType`, `${prefix}Scope`, `${prefix}APIUsername`, `${prefix}APIPassword`]
        };

        this.HandleAuthTypeShowHideFiled(selectedValue, allDivs, authTypeToDivsMap, section);
        if (!ManageDataExchange.prototype.APIHandlerValidations(section, authType, true)) {
            isValid = false;
        }
    }

    private HandleAuthTypeShowHideFiled(selectedValue: string, allDivs: string[], authTypeToDivsMap: { [key: string]: string[] }, type: "Source" | "Dest"): void {
        // Hide all first
        $(allDivs.join(",")).addClass("d-none");

        // Show relevant ones
        const divsToShow = authTypeToDivsMap[selectedValue];
        if (divsToShow) {
            $(divsToShow.join(",")).removeClass("d-none");
        }
    }

    public SubmitDataExchangeForm(): boolean {
        ZnodeGlobal.prototype.ShowLoader();
        const isValid = ManageDataExchange.prototype.ValidateForm();

        if (!isValid) {
            const collapseIds = ['collapseTransmission', 'collapseTransmissionDestination', 'collapseStatusActivation', 'collapseMapping'];
            const element = $(".messageBoxContainer").removeAttr("style");

            collapseIds.forEach(id => {
                $('#' + id).addClass('show');
                $('.accordion-button').css('box-shadow', 'inset 0 -1px 0 rgba(0, 0, 0, .125)');

            });
            ZnodeGlobal.prototype.HideLoader();
            ZnodeGlobal.prototype.ShowNotificationBar(element, resources.ManageDataExchangeError, 'error');
            return false;
        }
        var jsonObj = [];
        $(".dirty").each(function () {
            var FieldName = $(this).attr("aria-label");
            var OldValue = $(this).attr("data-oldvalue");
            var NewValue = $(this).val();

            if (OldValue === resources.SFTPHandler) OldValue = resources.SFTPValue;
            if (OldValue === resources.APIHandler) OldValue = resources.APIValue;

            if (NewValue === resources.SFTPHandler) NewValue = resources.SFTPValue;
            if (NewValue === resources.APIHandler) NewValue = resources.APIValue;

            if (FieldName == resources.YAMLFieldName) {
                OldValue = resources.YAMLOldValue;
                NewValue = resources.YAMLNewValue;
            }

            var item = {}
            item["FieldName"] = FieldName;
            item["OldValue"] = OldValue;
            item["NewValue"] = NewValue;
            jsonObj.push(item);
        });
        $("#ConfigurationChangeJsonData").val(JSON.stringify(jsonObj));
        return true;
    }


    ValidateForm(): any {
        let isValid = true;
        let isBaseDefinitionFormValid = true;
        const transmissionSource = String($("#transmissionSourceDropdown").val()).trim();
        const transmissionDestination = String($("#transmissionDestinationDropdown").val()).trim();
        const schedulerType = String($("#ScheduleType").val()).trim();
        // Validate source/destination base dropdowns
        if (!ManageDataExchange.prototype.ValidateField("#transmissionSourceDropdown", "#TransmissionSourceError", "Transmission mode is required.")) isValid = false;
        if ((schedulerType?.toLowerCase() === "ondemand" || schedulerType?.toLowerCase() === "scheduled") && !ManageDataExchange.prototype.ValidateField("#transmissionDestinationDropdown", "#TransmissionDestinationError", "Transmission mode is required.")) isValid = false;

        // Source validations
        if (transmissionSource === "APIHandler") {
            const sourceAuthType = ($("#SourceDropdownAuthType").val() ?? "").toString();
            const isAuthTypeValid = ManageDataExchange.prototype.ValidateField("#SourceDropdownAuthType", "#SourceAuthTypeError", "Authentication type is required.");
            const isActionValid = ManageDataExchange.prototype.ValidateField("#SourceHTTPAction", "#SourceHTTPActionError", "API Action is required.");
            const isAPIHandlerValid = ManageDataExchange.prototype.APIHandlerValidations("Source", sourceAuthType);
            const isSourceHTTPFormatValid = ManageDataExchange.prototype.ValidateField("#HTTPSourceDropdownFileFormat", "#HTTPSourceFileFormatError", "Format is required.");
            if (!isAuthTypeValid || !isActionValid || !isAPIHandlerValid || !isSourceHTTPFormatValid) {
                isValid = false;
            }
        }
        else if (transmissionSource === "SFTPHandler") {
            if (!ManageDataExchange.prototype.SFTPHandlerValidations("Source")) isValid = false;
            if (!ManageDataExchange.prototype.ValidateField("#SFTPSourceDropdownFileFormat", "#SFTPSourceFileFormatError", "Format is required.")) isValid = false;
        }

        // Destination validations
        if (transmissionDestination === "APIHandler" && (schedulerType?.toLowerCase() === "ondemand" || schedulerType?.toLowerCase() === "scheduled")) {
            const destAuthType = ($("#DestDropdownAuthType").val() ?? "").toString();
            const isAuthTypeValid = ManageDataExchange.prototype.ValidateField("#DestDropdownAuthType", "#DestDropdownAuthTypeError", "Authentication type is required.");
            const isActionValid = ManageDataExchange.prototype.ValidateField("#DestHTTPAction", "#DestHTTPActionError", "API Action is required.");
            const isAPIHandlerValid = ManageDataExchange.prototype.APIHandlerValidations("Dest", destAuthType);
            const isDestHTTPFormatValid = ManageDataExchange.prototype.ValidateField("#HTTPDestDropdownFileFormat", "#HTTPDestFileFormatError", "Format is required.");
            if (!isAuthTypeValid || !isActionValid || !isAPIHandlerValid || !isDestHTTPFormatValid) {
                isValid = false;
            }
        }
        else if (transmissionDestination === "SFTPHandler" && (schedulerType?.toLowerCase() === "ondemand" || schedulerType?.toLowerCase() === "scheduled")) {
            if (!ManageDataExchange.prototype.SFTPHandlerValidations("Dest")) isValid = false;
            if (!ManageDataExchange.prototype.ValidateField("#SFTPDestDropdownFileFormat", "#SFTPDestFileFormatError", "Format is required.")) isValid = false
        }
        // Global validations (Status and Notifications)
        const validations: [string, string, string, (selector: string, errorSelector: string, errorMessage: string) => boolean][] = [
            ["#Status", "#StatusError", "Status is required.", this.ValidateField],
            ["#InformationNotification", "#InformationNotificationError", "Invalid email in Information Notification.", this.validateEmailList],
            ["#WarningNotification", "#WarningNotificationError", "Invalid email in Warning Notification.", this.validateEmailList],
            ["#ErrorNotification", "#ErrorNotificationError", "Invalid email in Error Notification.", this.validateEmailList]
        ];

        for (const [selector, errorSelector, errorMessage, validatorFn] of validations) {
            if (!validatorFn(selector, errorSelector, errorMessage)) isValid = false;
        }

        if (schedulerType === "Scheduled") {
            const isSchedulerValid = ManageDataExchange.prototype.SchedulerConfigValidations();
            const frequencyValue = String($("#SchedulerFrequency").val()).trim();
            let isCronValid = true;
            if (frequencyValue === "Recurring") {
                const cronValue = String($("#txtCronExpression").val()).trim();

                if (!cronValue) {
                    $("#valCronExpressionError").text(resources.CronRequiredError);
                    isCronValid = false;
                } else if (!this.isValidCronExpression(cronValue)) {
                    $("#valCronExpressionError").text(resources.CronErrorRegex);
                    isCronValid = false;
                } else {
                    $("#valCronExpressionError").text("");
                    isCronValid = true;
                }
            }
            if (!isSchedulerValid || !isCronValid) {
                isValid = false;
            }
        }

        if ($("#addCustomBaseDefinitionForm").length) {
            isBaseDefinitionFormValid = this.ValidateCreateEditForm();
        }

        return isValid && isBaseDefinitionFormValid;
    }

    public isValidCronExpression(cronExpression) {
        const cronParts = cronExpression.trim().split(/\s+/);
        const validCronCharactersRegex = /^[\d\*,\/\-]+$/;
        if (cronParts.length !== 5) return false;

        return cronParts.every(part => validCronCharactersRegex.test(part));
    }

    ValidateField(selector: string, errorSelector: string, errorMessage: string) {

        const value = String($(selector).val()).trim();
        const grantTypeRegex = /^[a-z_]+$/i;
        const endpointRegex = /^(https?:\/\/)(www\.)?((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|(\d{1,3}(\.\d{1,3}){3}))(:\d+)?(\/[^\s]*)?$/i;
        const fileNameRegex = /^[\w\s.-]+\.(csv|json|xml|yml|yaml)$/i;
        const folderPathRegex = /^\/?([\w.-]+\/)*[\w.-]*$/i;
        const serverAddressRegex = /^((?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}|(?:\d{1,3}\.){3}\d{1,3})(:\d{1,5})?$/i;

        if (!value) {
            $(errorSelector).text(errorMessage);
            return false;
        }
        if ((selector === "#SourceServerAddress" || selector === "#DestServerAddress") && !serverAddressRegex.test(value)) {
            $(errorSelector).text("Enter a valid server address.");
            return false;
        }
        if ((selector === "#SourceFolderPath" || selector === "#DestFolderPath") && !folderPathRegex.test(value)) {
            $(errorSelector).text("Enter a valid folder path.");
            return false;
        }
        if ((selector === "#SourceFilename" || selector === "#DestFilename") && !fileNameRegex.test(value)) {
            $(errorSelector).text("Enter a valid file name (formats allowed: .csv, .json, .xml).");
            return false;
        }
        if ((selector === "#SourceEndpoint" || selector === "#DestEndpoint") && !endpointRegex.test(value)) {
            $(errorSelector).text("Enter a valid endpoint.");
            return false;
        }
        if ((selector === "#SourceGrantType" || selector === "#DestGrantType") && !grantTypeRegex.test(value)) {
            $(errorSelector).text("Enter a valid grant type.");
            return false;
        }
        if ((selector === "#schedulerName")) {
            const isValid = /^(?!.*<[^>]*>)[a-zA-Z0-9_\s]*$/.test(value);
            if (!isValid) {
                $(errorSelector).text("Enter a valid Scheduler name.");
                return false;
            }
            if (value.length > 100) {
                $(errorSelector).text("Maximum 100 characters allowed.");
                return false;
            }
            if (isValid) {
                $(errorSelector).text("");
            }
            return true;
        }
        else {
            $(errorSelector).text("");
            return true;
        }
    };


    SFTPHandlerValidations(prefix: "Source" | "Dest"): any {
        let isValid = true;
        const sftpValidations: [string, string, string][] = [
            [`#${prefix}ServerAddress`, `#${prefix}ServerAddressError`, "Server Address is required."],
            [`#${prefix}Username`, `#${prefix}UsernameError`, "Username is required."],
            [`#${prefix}Password`, `#${prefix}PasswordError`, "Password is required."],
            [`#${prefix}PortNumber`, `#${prefix}PortNumberError`, "Port is required."],
            [`#${prefix}Filename`, `#${prefix}FilenameError`, "File Name is required."],
            [`#${prefix}FolderPath`, `#${prefix}FolderPathError`, "Folder Path is required."],
            [`#${prefix}ActionAfterRetrieval`, `#${prefix}ActionAfterRetrievalError`, "Retrieval Action is required."]
        ];
        for (const [selector, errorSelector, errorMessage] of sftpValidations) {
            if (!ManageDataExchange.prototype.ValidateField(selector, errorSelector, errorMessage)) {
                isValid = false;
            }
        }
        return isValid;
    }


    APIHandlerValidations(prefix: "Source" | "Dest", authTypeValue: string | null, skipValidation = false): any {
        let isValid = true;

        type ValidationField = [string, string, string];
        const authValidations: Record<AuthType, ValidationField[]> = {
            Basic: [
                [`#${prefix}Endpoint`, `#${prefix}EndpointError`, "Endpoint is required."]
            ],
            OAuth2: [
                [`#${prefix}Endpoint`, `#${prefix}EndpointError`, "Endpoint is required."],
                [`#${prefix}GrantType`, `#${prefix}GrantTypeError`, "Grant type is required."],
                [`#${prefix}AccessTokenUrl`, `#${prefix}AccessTokenUrlError`, "Access token URL is required."],
                [`#${prefix}ClientId`, `#${prefix}ClientIdError`, "Client ID is required."],
                [`#${prefix}ClientSecret`, `#${prefix}ClientSecretError`, "Client Secret is required."]
            ],
            Bearer: [
                [`#${prefix}Endpoint`, `#${prefix}EndpointError`, "Endpoint is required."],
                [`#${prefix}AccessTokenUrl`, `#${prefix}AccessTokenUrlError`, "Access token URL is required."],
                [`#${prefix}GrantType`, `#${prefix}GrantTypeError`, "Grant type is required."],
                [`#${prefix}ClientId`, `#${prefix}ClientIdError`, "Client ID is required."],
                [`#${prefix}ClientSecret`, `#${prefix}ClientSecretError`, "Client Secret is required."]
            ]
        };
        type AuthType = "Basic" | "OAuth2" | "Bearer";

        const normalizedAuthType = (authTypeValue ?? "").trim().toLowerCase();

        let matchedAuthType: AuthType | undefined;

        switch (normalizedAuthType.toLowerCase()) {
            case "basic": matchedAuthType = "Basic"; break;
            case "oauth2": matchedAuthType = "OAuth2"; break;
            case "bearer": matchedAuthType = "Bearer"; break;
            default:
                return false;
        }
        const validations = [
            ...(authValidations[matchedAuthType] || [])
        ];

        for (const [selector, errorSelector, errorMessage] of validations) {
            if (skipValidation) {
                $(errorSelector).text("");
                continue;
            }
            if (!ManageDataExchange.prototype.ValidateField(selector, errorSelector, errorMessage)) {
                isValid = false;
            }
        }
        return isValid;
    }

    APIFormatValidations(prefix: "Source" | "Dest"): any {
        let isValid = true;
        const errorSelector = $(`#${prefix}EndpointError`);
        $(errorSelector).html('');
        var endpoint = String($(`#${prefix}Endpoint`).val()).trim();
        var urlR = /^(https?:\/\/)(www\.)?((([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})|(\d{1,3}(\.\d{1,3}){3}))(:\d+)?(\/[^\s]*)?$/;
        if (endpoint.match(urlR)) {
            $(errorSelector).text("");
            return true;
        } else {
            $(errorSelector).text("Enter a valid endpoint.");
            return false;
        }
    }

    validateEmailList(selector: string, errorSelector: string, errorMessage: string): any {
        const value = String($(selector).val()).trim();

        if (!value) {
            $(errorSelector).text("");
            return true;
        }

        const emailRegex = /^(?=[a-zA-Z0-9]*[a-zA-Z])[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        const invalidEmails = String(value).split(',').map(e => e.trim()).filter(e => e && !emailRegex.test(e));

        if (invalidEmails.length > 0) {
            $(errorSelector).text(`Invalid Email format${invalidEmails.length > 1 ? 's' : ''}`);
            return false;
        }

        $(errorSelector).text("");
        return true;
    };

    public handleTransmissionSourceChange($selectElement: any): void {
        const selector = $selectElement.val();
        const selectedValue = selector + "_Source";
        $("#APIHandler_Source, #SFTPHandler_Source, #DivSourceTestConnection").addClass("d-none");
        if (selector === "SFTPHandler") {
            $('#DivSourceTestConnection').removeClass("d-none");
        }
        if (selector === "APIHandler") {
            if ($("#" + selectedValue).find("#SourceDropdownAuthType").val() === "Basic") {
                $('#DivSourceTestConnection').removeClass("d-none");
            }
            this.HandleAuthType("Source", $("#SourceDropdownAuthType"));
        }
        const target = $("#" + selectedValue);
        if (target.length && $.trim(target.html() || "").length > 0) {
            target.removeClass("d-none");
        }
    }

    public handleTransmissionDestinationChange($selectElement: any): void {
        const selector = $selectElement.val();
        const selectedValue = selector + "_Destination";
        $("#APIHandler_Destination, #SFTPHandler_Destination, #DivDestinationTestConnection").addClass("d-none");
        if (selector === "SFTPHandler") {
            $('#DivDestinationTestConnection').removeClass("d-none");
        }
        if (selector === "APIHandler") {
            if ($("#" + selectedValue).find("#DestDropdownAuthType").val() === "Basic") {
                $('#DivDestinationTestConnection').removeClass("d-none");
            }
            this.HandleAuthType("Dest", $("#DestDropdownAuthType"));
        }
        const target = $("#" + selectedValue);
        if (target.length && $.trim(target.html() || "").length > 0) {
            target.removeClass("d-none");
        }
    }

    public togglePassword(button: HTMLElement): void {
        const input = button.parentElement?.querySelector('input');
        const icon = button.querySelector('i');

        if (input) {
            const isPassword = input.getAttribute('type') === 'password';
            input.setAttribute('type', isPassword ? 'text' : 'password');

            if (icon) {
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            }
        }
    }

    public addParameters(type: "source" | "destination", param: "QueryParams" | "HeaderParams"): void {
        const { indexInputId, rowPrefix, bodyId, inputNamePrefix, dataSelector, spanId } = ParameterConfig[type][param];
        const indexInput = document.getElementById(indexInputId) as HTMLInputElement;
        let currentIndex = parseInt(indexInput.value, 10);
        if (currentIndex === 0) {
            currentIndex = 1;
        }

        const row = `
<div class="row align-items-center" id="${rowPrefix}-${currentIndex}">
    <div class="col-md-5 my-2"><input type="text" name="${inputNamePrefix}[${currentIndex}].Key" data-test-selector="${dataSelector}Key" class="form-control" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="${spanId}-${currentIndex}" class="text-danger"></span></div>
    <div class="col-md-5 my-2"><input type="text" name="${inputNamePrefix}[${currentIndex}].Value" data-test-selector="${dataSelector}Value" class="form-control" /></div>
    <div class="col-1 my-2 px-0">
         <button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('${rowPrefix}-${currentIndex}')"><em class="z-close-circle"></em></button>
    </div>
</div>`;

        $(`#${bodyId}`).append(row);
        indexInput.value = (currentIndex + 1).toString();

        this.toggleHeaderVisibility(ParameterConfig[type][param]);
    }

    public ReindexRows(): void {
        (["source", "destination"] as const).forEach(type => {
            (["QueryParams", "HeaderParams"] as const).forEach(param => {
                const { bodyId, rowPrefix, inputNamePrefix, spanId, indexInputId, dataSelector } = ParameterConfig[type][param];

                const container = document.getElementById(bodyId);
                if (!container) return;

                const rows = container.querySelectorAll(".row");
                rows.forEach((row, index) => {
                    const newRowId = `${rowPrefix}-${index}`;
                    row.id = newRowId;

                    const inputs = row.querySelectorAll("input");
                    inputs.forEach(input => {
                        if (input.name.endsWith(".Key")) {
                            input.name = `${inputNamePrefix}[${index}].Key`;
                            input.setAttribute("data-test-selector", `${dataSelector}Key`);
                        } else if (input.name.endsWith(".Value")) {
                            input.name = `${inputNamePrefix}[${index}].Value`;
                            input.setAttribute("data-test-selector", `${dataSelector}Value`);
                        }
                    });

                    const span = row.querySelector("span");
                    if (span) {
                        span.id = `${spanId}-${index}`;
                    }

                    const btn = row.querySelector("button");
                    if (btn) {
                        btn.setAttribute("onclick", `ManageDataExchange.prototype.removeRow('${newRowId}')`);
                    }
                });

                const indexInput = document.getElementById(indexInputId) as HTMLInputElement;
                if (indexInput) {
                    indexInput.value = rows.length.toString();
                }
            });
        });
    }


    public ValidateUniqueKeys(): void {
        let hasDuplicateKey = false;

        const configs = [
            "TransmissionConfigurations.HttpsConfig.QueryParams",
            "TransmissionConfigurations.HttpsConfig.HeaderParams",
            "TransmissionConfigurations.HttpsConfigDestination.QueryParams",
            "TransmissionConfigurations.HttpsConfigDestination.HeaderParams"
        ];

        configs.forEach(prefix => {
            const keyInputs = $(`input[name^="${prefix}"][name$=".Key"]`);
            const keysSeen: Record<string, number> = {};

            keyInputs.each(function () {
                const val = String($(this).val()).trim();
                if (val) {
                    keysSeen[val] = (keysSeen[val] || 0) + 1;
                }
            });

            keyInputs.each(function () {
                const val = String($(this).val()).trim();
                const name = $(this).attr("name");
                const indexMatch = name?.match(/\[(\d+)\]\.Key/);
                const index = indexMatch ? indexMatch[1] : "0";
                const spanId = prefix.includes("Destination")
                    ? (prefix.includes("Query") ? "destinationQueryParameterError" : "destinationHeaderParameterError")
                    : (prefix.includes("Query") ? "SourceQueryParameterError" : "SourceHeaderParameterError");

                const error = $(`#${spanId}-${index}`);
                error.text("");

                if (val && keysSeen[val] > 1) {
                    error.text('Please enter a unique key.');
                    hasDuplicateKey = true;
                }
            });
        });

        $('#manageDataExchange').prop('disabled', hasDuplicateKey);
    }

    public removeRow(rowId: string): void {
        $("#" + rowId).remove();
        this.ReindexRows();
        this.ValidateUniqueKeys();
        this.toggleHeaderVisibility(this.getConfigFromRowId(rowId));
    }

    public EnableDisableSettings(selectedOption): void {
        if (selectedOption != undefined && selectedOption != null && selectedOption != '') {
            if (selectedOption?.toLowerCase() == "scheduled") {
                $('#schedulerSetting').show();
                $('#accordionTransmissionDestination').show();
            }
            else if (selectedOption?.toLowerCase() == "ondemand") {
                $('#schedulerSetting').hide();
                $('#accordionTransmissionDestination').show();
            }
            else if (selectedOption?.toLowerCase() == "realtime") {
                $('#schedulerSetting').hide();
                $('#accordionTransmissionDestination').hide();
            }
        }
        else {
            $('#schedulerSetting').hide();
        }
    }

    public ToggleFrequency(selectedOption): void {
        if (selectedOption != undefined && selectedOption != null && selectedOption != '') {
            if (selectedOption?.toLowerCase() == "onetime") {
                $('#recurringFrequency').hide();
                $('#oneTimeFrequency').show();
            }
            else if (selectedOption?.toLowerCase() == "recurring") {
                $('#oneTimeFrequency').hide();
                $('#recurringFrequency').show();
            }
        }
        else {
            $('#oneTimeFrequency').hide();
            $('#recurringFrequency').hide();
        }
    }

    public ValidateAndTestConnection(type: "Source" | "Dest", element: any) {
        ZnodeGlobal.prototype.ShowLoader();
        var dataForm = $(element).closest('.accordion-body');
        dataForm.find('.text-danger').html('');
        var mode = dataForm.find('[data-test-selector="drpTransmissionModeOptions"]').val();
        var request: string;
        if (mode == "SFTPHandler") {
            const fields = [
                { name: 'ServerAddress', resourceKey: 'ServerAddressRequired' },
                { name: 'Username', resourceKey: 'UsernameRequired' },
                { name: 'Password', resourceKey: 'PasswordRequired' },
                { name: 'PortNumber', resourceKey: 'PortNumberRequired' }
            ];

            const fieldValues = this.validateFields(fields, dataForm, type, resources);
            if (!fieldValues) return;

            request = JSON.stringify({
                ServerAddress: fieldValues['ServerAddress'],
                UserName: fieldValues['Username'],
                Password: fieldValues['Password'],
                PortNumber: fieldValues['PortNumber']
            });
        }
        else if (mode == "APIHandler") {
            const basicAuthfields = [
                { name: 'HTTPAction', resourceKey: 'HTTPActionRequired' },
                { name: 'Endpoint', resourceKey: 'EndpointRequired' }
            ];
            var apiAuthType = dataForm.find(`#${type}DropdownAuthType`).val();

            const queryParams = [];
            const paramsType = type == "Dest" ? "Destination" : type;
            const queryParamId = "queryParamBody" + paramsType; 
            dataForm.find(`#${queryParamId} .row`).each(function () {
                const key = $(this).find('[data-test-selector="txtQueryParamsKey"]').val();
                const value = $(this).find('[data-test-selector="txtQueryParamsValue"]').val();
                queryParams.push({ Key: key, Value: value });
            });

            const headerParams = [];
            const headerParamId = "headerParamBody" + paramsType;
            dataForm.find(`#${headerParamId} .row`).each(function () {
                const key = $(this).find('[data-test-selector="txtHeaderParamsKey"]').val();
                const value = $(this).find('[data-test-selector="txtHeaderParamsValue"]').val();
                headerParams.push({ Key: key, Value: value });
            });

            if (apiAuthType == "Basic") {
                const fieldValues = this.validateFields(basicAuthfields, dataForm, type, resources);
                const apiFormatValid = fieldValues ? this.APIFormatValidations(type) : false;
                if (!fieldValues || !apiFormatValid) return;
                var basicAuthApiJson = {
                    HttpAction: fieldValues["HTTPAction"],
                    Endpoint: fieldValues["Endpoint"],
                    APIKey: dataForm.find(`#${type}APIKey`).val(),
                    Username: dataForm.find(`#${type}APIUsername`).val(),
                    Password: dataForm.find(`#${type}APIPassword`).val(),
                    AuthenticationType: apiAuthType,
                    QueryParams: queryParams,
                    HeaderParams: headerParams
                }
                request = JSON.stringify(basicAuthApiJson);
            }
            // TODO: Planned for phase 2
            else if (apiAuthType == "OAuth2" || apiAuthType == "Bearer") {
                return;
            }
        }
        if (request != null && request != undefined && request != "") {
            $(element).prop('disabled', true);
            $.ajax({
                url: '/commerce-connector/ManageDataExchange/TestConnection',
                method: 'GET',
                contentType: 'application/json',
                data: { "mode": mode, "request": request },
                success: (response) => {
                    var message = response.status ? 'Connection established successfully' : 'Connection failed';
                    var element: any = $(".messageBoxContainer");
                    $(".messageBoxContainer").removeAttr("style");
                    ZnodeGlobal.prototype.HideLoader();
                    ZnodeGlobal.prototype.ShowNotificationBar(element, message, response.status ? 'success' : 'error');
                },
                error: function (err) {
                    ZnodeGlobal.prototype.HideLoader();
                    console.error('Error loading page:', err);
                },
                complete: function () {
                    ZnodeGlobal.prototype.HideLoader();
                    $(element).prop('disabled', false);
                }
            });
        }
    }

    public validateFields(fields, dataForm, type, resources) {
        const values = {};
        let hasError = false;

        fields.forEach(field => {
            const errorId = `#${type}${field.name}Error`;
            dataForm.find(errorId).html('');
        });

        fields.forEach(({ name, resourceKey }) => {
            const value = dataForm.find(`#${type}${name}`).val().trim();
            values[name] = value;

            if (!value) {
                hasError = true;
                const errorMessage = resources[resourceKey];
                dataForm.find(`#${type}${name}Error`).html(errorMessage);
            }
        });

        return hasError ? null : values;
    }

    public ValidateCreateEditForm() {
        let isValid = true;
        let exchangeName = ($('#exchangeName').val() as string).trim();
        let dataSource = ($('#dataSource').val() as string).trim();
        let version = ($('#version').val() as string).trim();
        let processorFileName = ($('#processorFileName').val() as string).trim();
        let dataDestination = ($('#dataDestination').val() as string).trim();
        let triggerOrigin = ($('#triggerOrigin').val() as string).trim();
        const versionRegex = /^\d{1,2}(\.\d{0,2})?$/;
        const fileNameRegex = /^[a-zA-Z][a-zA-Z0-9]*$/;
        const validTextRegEx = /^[a-zA-Z0-9 ]+$/;
        if (($('#exchangeName').val() as string).trim() && validTextRegEx.test(exchangeName)) {
            $('#exchangeNameError').text('');
        }
        else if (!validTextRegEx.test(exchangeName) && exchangeName !== '') {
            $('#exchangeNameError').text(resources.SpecialCharactersRegex);
            isValid = false;
        }
        else {
            $('#exchangeNameError').text(resources.ExchangeNameError);
            isValid = false;
        }

        if (($('#dataSource').val() as string).trim() && validTextRegEx.test(dataSource)) {
            $('#dataSourceError').text('');
        }
        else if (!validTextRegEx.test(dataSource) && dataSource !== '') {
            $('#dataSourceError').text(resources.SpecialCharactersRegex);
            isValid = false;
        }
        else {
            $('#dataSourceError').text(resources.DataSourceError);
            isValid = false;
        }

        if (!version) {
            $('#versionError').text(resources.VersionError);
            isValid = false;
        }else if (!/^\d+(\.\d+)?$/.test(version)) {

            $('#versionError').text(resources.VersionErrorRegEx);
            isValid = false;
        } else if (parseFloat(version) <= 0) {
            $('#versionError').text(resources.VersionGreaterThanOneError);
            isValid = false;
        } else if (!versionRegex.test(version)) {
            const [integerPart = '', decimalPart = ''] = version.split('.');

            if (integerPart.length > 2) {
                $('#versionError').text(resources.VersionIntegerPartError);
            } else if (decimalPart.length > 2) {
                $('#versionError').text(resources.VersionDecimalPartError);
            } else {
                $('#versionError').text(resources.VersionErrorRegEx);
            }
            isValid = false;
        } else {
            $('#versionError').text('');
        }

        if (!processorFileName) {
            $('#processorFileNameError').text(resources.ProcessorFileNameError);
            isValid= false;
        }else if (!fileNameRegex.test(processorFileName)) {
            $('#processorFileNameError').text(resources.ProcessorFileNameRegEX);
            isValid = false;
        } else {
            $('#processorFileNameError').text('');
        }


        if (($('#dataDestination').val() as string).trim() && validTextRegEx.test(dataDestination)) {
            $('#dataDestinationError').text('');
        }
        else if (!validTextRegEx.test(dataDestination) && dataDestination !== '') {
            $('#dataDestinationError').text(resources.SpecialCharactersRegex);
            isValid = false;
        }
        else {
            $('#dataDestinationError').text(resources.DataDestinationError);
            isValid = false;
        }

        if (($('#triggerOrigin').val() as string).trim() && validTextRegEx.test(triggerOrigin)) {
            $('#triggerOriginError').text('');
        }
        else if (!validTextRegEx.test(triggerOrigin) && triggerOrigin !== '') {
            $('#triggerOriginError').text(resources.SpecialCharactersRegex);
            isValid = false;
        }
        else {
            $('#triggerOriginError').text(resources.TriggerOriginError);
            isValid = false;
        }
        return isValid;
    }

    SchedulerConfigValidations(): any {
        let isValid = true;
        const schedulerValidations: [string, string, string][] = [
            [`#schedulerName`, `#SchedulerNameError`, ""],
            [`#SchedulerFrequency`, `#SchedulerFrequencyError`, "Frequency is required."],
        ];
        for (const [selector, errorSelector, errorMessage] of schedulerValidations) {
            if (!ManageDataExchange.prototype.ValidateField(selector, errorSelector, errorMessage)) {
                isValid = false;
            }
        }
        return isValid;
    }

    public DownloadTemplate() {
        window.location.href = '/commerce-connector/YAML/DownloadSample';
    }

    public saveYaml(): any{
        ZnodeGlobal.prototype.ShowLoader();
        let erpID = $("#erpID").val();
        let Name = $("#Name").val();

        const content = editor.getValue();
        const requestData = {
            ERPId: erpID,
            DataExchangeName: Name,
            FileContent: content
        };

        fetch('/commerce-connector/Yaml/SaveYaml', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestData)
        })
            .then(res => res.json())
            .then(data => {
                var message = data.status ? 'Mappings updated successfully.' : 'Failed to update mappings.';
                var element: any = $(".messageBoxContainer");
                $(".messageBoxContainer").removeAttr("style");
                ZnodeGlobal.prototype.HideLoader();
                ZnodeGlobal.prototype.ShowNotificationBar(element, message, data.status ? 'success' : 'error');
                return false;
            });
        ZnodeGlobal.prototype.HideLoader();
        return false;
    }

    public UploadYaml() {
        ZnodeGlobal.prototype.ShowLoader();
        const fileInput = document.getElementById("yamlUpload") as HTMLInputElement;
        const file = fileInput.files[0]

        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        if (fileExtension === 'yaml' || fileExtension === 'yml') {
            const formData = new FormData();
            formData.append("yamlFile", file);

            fetch('/commerce-connector/Yaml/UploadYaml', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        editor.setValue(data.content);
                        yamlContent = data.content;
                        $('.yaml-textarea').removeClass('dirty');
                        ZnodeGlobal.prototype.HideLoader();
                    } else {
                        var message = data.success ? 'File uploaded succesfully.' : 'Failed to upload file.';
                        var element: any = $(".messageBoxContainer");
                        $(".messageBoxContainer").removeAttr("style");
                        ZnodeGlobal.prototype.HideLoader();
                        ZnodeGlobal.prototype.ShowNotificationBar(element, message, data.success ? 'success' : 'error');
                    }
                })
                .catch(error => {
                    var message = 'Something went wrong.';
                    var element: any = $(".messageBoxContainer");
                    $(".messageBoxContainer").removeAttr("style");
                    ZnodeGlobal.prototype.HideLoader();
                    ZnodeGlobal.prototype.ShowNotificationBar(element, message,'error');
                });
        }
        else {
            var message = 'Only YAML/YML file formats are allowed.';
            var element: any = $(".messageBoxContainer");
            $(".messageBoxContainer").removeAttr("style");
            ZnodeGlobal.prototype.HideLoader();
            ZnodeGlobal.prototype.ShowNotificationBar(element, message, 'error');
        }
    }

    public GetSavedYamlFile() {
        let erpID = $("#erpID").val();
        fetch(`/commerce-connector/Yaml/GetSavedYAMlData?erpID=${erpID}`, {
            method: 'GET'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    editor.setValue(data.content);
                    yamlContent = data.content;
                    $('.yaml-textarea').removeClass('dirty');
                } else {
                    var message = 'Failed to fetch the file.';
                    var element: any = $(".messageBoxContainer");
                    $(".messageBoxContainer").removeAttr("style");
                    ZnodeGlobal.prototype.ShowNotificationBar(element, message, 'error');
                }
            })
            .catch(error => {
                var message = 'Something went wrong.';
                var element: any = $(".messageBoxContainer");
                $(".messageBoxContainer").removeAttr("style");
                ZnodeGlobal.prototype.ShowNotificationBar(element, message, 'error');
            });
    }

    public toggleHeaderVisibility(config) {
        const header = document.getElementById(config.headerId);
        const container = document.getElementById(config.bodyId);
        
        if (!header || !container) return;

        const rows = container.querySelectorAll(`[id^="${config.rowPrefix}"]`);
        const hasRows = rows.length > 0;

        header.style.display = hasRows ? 'flex' : 'none';
        header.classList.toggle('d-none', !hasRows);
    }

    private getConfigFromRowId(rowId: string): any {
        if (rowId.startsWith('source-query-row')) {
            return ParameterConfig.source.QueryParams;
        } else if (rowId.startsWith('source-header-row')) {
            return ParameterConfig.source.HeaderParams;
        } else if (rowId.startsWith('destination-query-row')) {
            return ParameterConfig.destination.QueryParams;
        } else if (rowId.startsWith('destination-header-row')) {
            return ParameterConfig.destination.HeaderParams;
        }
        return null;
    }
}

$(document).ready(() => {
    $(document).on('input change', '#exchangeName, #dataSource, #processorFileName, #version, #dataDestination, #triggerOrigin, #SourceDropdownAuthType, #DestDropdownAuthType, #SourceHTTPAction, #DestHTTPAction, #transmissionSourceDropdown, #transmissionDestinationDropdown, #WarningNotification, #ErrorNotification, #InformationNotification, #SourceActionAfterRetrieval, #DestActionAfterRetrieval, #SFTPSourceDropdownFileFormat, #SFTPDestDropdownFileFormat, #HTTPSourceDropdownFileFormat, #HTTPDestDropdownFileFormat, #txtCronExpression', function () {
        const fieldId = $(this).attr('id')!;
        const value = ($(this).val() as string).trim();
        const versionRegex = /^\d{1,2}(\.\d{0,2})?$/;
        const fileNameRegex = /^[a-zA-Z][a-zA-Z0-9]*$/;
        const frequencyValue = String($("#SchedulerFrequency").val()).trim();

        const validationMap = {
            exchangeName: { errorSelector: '#exchangeNameError', errorMessage: resources.ExchangeNameError },
            dataSource: { errorSelector: '#dataSourceError', errorMessage: resources.DataSourceError },
            dataDestination: { errorSelector: '#dataDestinationError', errorMessage: resources.DataDestinationError },
            triggerOrigin: { errorSelector: '#triggerOriginError', errorMessage: resources.TriggerOriginError },
            SourceDropdownAuthType: { errorSelector: '#SourceAuthTypeError', errorMessage: resources.AuthTypeError },
            DestDropdownAuthType: { errorSelector: '#DestDropdownAuthTypeError', errorMessage: resources.AuthTypeError },
            SourceHTTPAction: { errorSelector: '#SourceHTTPActionError', errorMessage: resources.APIActionError },
            DestHTTPAction: { errorSelector: '#DestHTTPActionError', errorMessage: resources.APIActionError },
            transmissionSourceDropdown: { errorSelector: '#TransmissionSourceError', errorMessage: resources.TransmissionModeError },
            transmissionDestinationDropdown: { errorSelector: '#TransmissionDestinationError', errorMessage: resources.TransmissionModeError },
            SourceActionAfterRetrieval: { errorSelector: '#SourceActionAfterRetrievalError', errorMessage: resources.RetrievalActionError },
            DestActionAfterRetrieval: { errorSelector: '#DestActionAfterRetrievalError', errorMessage: resources.RetrievalActionError },
            SFTPSourceDropdownFileFormat: { errorSelector: '#SFTPSourceFileFormatError', errorMessage: resources.FileFormatError },
            SFTPDestDropdownFileFormat: { errorSelector: '#SFTPDestFileFormatError', errorMessage: resources.FileFormatError },
            HTTPSourceDropdownFileFormat: { errorSelector: '#HTTPSourceFileFormatError', errorMessage: resources.FileFormatError },
            HTTPDestDropdownFileFormat: { errorSelector: '#HTTPDestFileFormatError', errorMessage: resources.FileFormatError }
        };

        if (fieldId === 'version') {
            if (value === '') {
                $('#versionError').text(resources.VersionError);
                return;
            }
            if (!/^\d+(\.\d+)?$/.test(value)) {
                $('#versionError').text(resources.VersionErrorRegEx);
                return;
            }

            if (parseFloat(value) <= 0) {
                $('#versionError').text(resources.VersionGreaterThanOneError);
                return;
            }

            if (!versionRegex.test(value)) {
                const [integerPart = '', decimalPart = ''] = value.split('.');

                if (integerPart.length > 2) {
                    $('#versionError').text(resources.VersionIntegerPartError);
                } else if (decimalPart.length > 2) {
                    $('#versionError').text(resources.VersionDecimalPartError);
                } else {
                    $('#versionError').text(resources.VersionErrorRegEx);
                }
                return;
            }
            $('#versionError').text('');
        }
        if (fieldId === 'processorFileName') {
            if (value === '') {
                $('#processorFileNameError').text(resources.ProcessorFileNameError);
            }
            else if (!fileNameRegex.test(value)) {
                $('#processorFileNameError').text(resources.ProcessorFileNameRegEX);
            } else {
                $('#processorFileNameError').text('');
            }
        }

        if (fieldId === 'txtCronExpression' && frequencyValue === "Recurring") {
            const cronValue = String($("#txtCronExpression").val()).trim();

            if (!cronValue) {
                $("#valCronExpressionError").text(resources.CronRequiredError);
            } else if (!ManageDataExchange.prototype.isValidCronExpression(cronValue)) {
                $("#valCronExpressionError").text(resources.CronErrorRegex);
            } else {
                $("#valCronExpressionError").text("");
            }
        }

        if (['WarningNotification', 'InformationNotification', 'ErrorNotification'].indexOf(fieldId) !== -1) {
            const errorSelector = `#${fieldId}Error`;
            const errorMessage = {
                WarningNotification: resources.WarningError,
                InformationNotification: resources.InformationError,
                ErrorNotification: resources.NotificationError
            }[fieldId];
            ManageDataExchange.prototype.validateEmailList(`#${fieldId}`, errorSelector, errorMessage);
            return;
        }

        const validation = validationMap[fieldId];
        if (validation) {
            $(validation.errorSelector).text(value === '' ? validation.errorMessage : '');
        }
    });
});
const dataExchange = new ManageDataExchange();
dataExchange.init();