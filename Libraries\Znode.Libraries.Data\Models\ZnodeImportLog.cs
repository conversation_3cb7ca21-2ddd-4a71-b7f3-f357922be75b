﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeImportLog
{
    public int CustomImportLogId { get; set; }

    public int CustomImportProcessLogId { get; set; }

    public int Erpid { get; set; }

    public string ImportLogMessage { get; set; } = null!;

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeImportProcessLog CustomImportProcessLog { get; set; } = null!;

    public virtual ZnodeBaseDefinitionTable Erp { get; set; } = null!;
}
