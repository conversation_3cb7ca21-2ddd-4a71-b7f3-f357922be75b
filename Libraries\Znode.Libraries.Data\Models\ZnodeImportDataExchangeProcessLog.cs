﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeImportDataExchangeProcessLog
{
    public int ImportDataExchangeProcessLogId { get; set; }

    public int ErpbaseDefinitionId { get; set; }

    public string StatusName { get; set; } = null!;

    public string TemplateName { get; set; } = null!;

    public DateTime ProcessStartedDate { get; set; }

    public DateTime? ProcessCompletedDate { get; set; }

    public int? SuccessRecordCount { get; set; }

    public int? FailedRecordCount { get; set; }

    public int? TotalProcessedRecords { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeErpbaseDefinition ErpbaseDefinition { get; set; } = null!;

    public virtual ICollection<ZnodeImportDataExchangeLog> ZnodeImportDataExchangeLogs { get; set; } = new List<ZnodeImportDataExchangeLog>();
}
