﻿namespace Znode.CommerceConnector.Model
{
    public class HttpsTransmissionConfigurationModel
    {
        public int? APIConfigurationId { get; set; }

        public string? HttpAction { get; set; }
        public string? Endpoint { get; set; }
        public string? AuthenticationType { get; set; }
        public string? GrantType { get; set; }
        public string? AccessTokenUrl { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? Scope { get; set; }
        public string? LocationOfCredentials { get; set; }
        public int? TransmissionConfigurationId { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string? APIKey { get; set; }
        public List<KeyValueModel> QueryParams { get; set; } = new();
        public List<KeyValueModel> HeaderParams { get; set; } = new();
        public string? Format { get; set; }
    }
}
