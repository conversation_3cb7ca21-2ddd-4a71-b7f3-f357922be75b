{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"ZnodeCommerceConnectorDB": "Server=AC-SVR047\\SQL2022;Initial Catalog=wbst_cmct_dv;Persist Security Info=False;User ID=cmct;Password=***************;MultipleActiveResultSets=False;Encrypt=False;TrustServerCertificate=True;Connection Timeout=30;", "ZnodeMongoDBForLog": "mongodb://localhost:27017/Znode_10xFeature_Docker_LogMessage", "HangfireConnection": "Server=AC-SVR047\\SQL2022;Initial Catalog=hgfr_cmct_dv;Persist Security Info=False;User ID=cmct;Password=***************;MultipleActiveResultSets=False;Encrypt=False;TrustServerCertificate=True;Connection Timeout=30;"}, "AllowedHosts": "*", "appsettings": {"CommerceConnectorApi": "http://znode10xcommerceconnectorapi.znode.svc.cluster.local", "YAMLFilePath": "Data/CCYAMLFiles", "ZnodeAPI": "https://apigateways-z10-lt.amla.io", "ZnodeAuthorizationKey": "Basic YXBpLXoxMC1sdC5hbWxhLmlvfGEyYzMyZDI2LWZiNTYtNGUyZC1hYTQ0LTEwYWU0MDFhMDk3MA=="}}