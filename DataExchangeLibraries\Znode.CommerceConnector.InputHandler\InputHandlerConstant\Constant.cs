﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.InputHandler
{
    public static class Constants
    {
        public static string DefaultLocaleCode = "1";
        public static string Source = "Source";
        public static string Destination = "Destination";
        public static string Authorization = "Authorization";
        public static string APIKey = "X-API-Key";
        public static string GET = "GET";
        public static string PUT = "PUT";
        public static string POST = "POST";
        public static string DELETE = "DELETE";
        public static string PATCH = "PATCH";
        public static string CallInputHandler = "CallInputHandler";
        public static string CallInputHandlerMethodCalledInInputHandlerInitializer = "CallInputHandler method in InputHandlerInitializer called";
        public static string TranmissionModeNotFound = "Transmission Mode not found";
        public static string SFTPHandler = "SFTPHandler";
        public static string SFTPDataHandlerMethodCalledInSFTPHandler = "SFTPDataHandler method in SFTPDataHandler called";
        public static string JSONFormat = "Format is JSON";
        public static string CSVFormat = "Format is CSV";
        public static string XMLFormat = "Format is XML";
        public static string JSON = "JSON";
        public static string CSV = "CSV";
        public static string XML = "XML";
        public static string TemptableGeneratedInInputHandler = "TempTable generated in input handler ";
        public static string FileNotFound = "File not found ";
        public static string SFTPDataHandlerMethodInSFTPDataHandlerFailed = "SFTPDataHandler method in SFTPDataHandler failed ";
        public static string GetSFTPCredentialsMethodInSFTPHandlerCalled = "GetSFTPCredentials method in SFTPDataHandler called";
        public static string GetSFTPCredentialsMethodInSFTPHandlerFailed = "GetSFTPCredentials method in SFTPDataHandler failed";
        public static string ParseCSVDataMethodInSFTPHandlerCalled = "ParseCSVData method in SFTPDataHandler called";
        public static string ParseCSVDataMethodTempTableName = "ParseCSVData method tempTableName ";
        public static string ParseCSVDataMethodInSFTPHandlerFailed = "ParseCSVData method in SFTPDataHandler failed";
        public static string ConvertJsonToDataTableCalled = "ConvertJsonToDataTable called";
        public static string ConvertJsonToDataTableFailed = "ConvertJsonToDataTable failed ";
        public static string ConnectedToSFTPServer = "Connected to SFTP server";
        public static string SpecifiedFileNotFoundOnServer = "Specified file not found on SFTP server ";
        public static string FailedToConnectToSFTPServer = "Failed to connect to SFTP server";
        public static string DownloadFailed = "DownloadData failed ";
        public static string DownloadData = "DownloadData";
        public static string GetYAMLDataHandlerMethodCalled = "GetYAMLDataHandler method in InputHandlerInitializer called";
        public static string GetYAMLDataHandlerMethodFailed = "GetYAMLDataHandler method in InputHandlerInitializer failed ";
        public static string GetYAMLFileNameByErpIdCalled = "GetYAMLFileNameByErpId method called in handlerdatahelper ";
        public static string GetYAMLFileNameByErpIdFailed = "GetYAMLFileNameByErpId method failed in handlerdatahelper ";
        public static string GetAPIFormatCalled = "GetAPIFormat method called handlerdatahelper ";
        public static string GetAPIFormatFailed = "GetAPIFormat method failed handlerdatahelper ";
        public static string HandlerDataHelper = "HandlerDataHelper";
        public static string GetFormatCalled = "GetFormat method called in SFTP inputhandler ";
        public static string GetFormatFailed = "GetFormat method failed in SFTP inputhandler ";
        public static string GetYAMLData = "GetYAMLData";
        public static string ReadingYamlFile = "Reading YML file from blob ";
        public static string BlobFileNotFound = "File not found in blob storage ";
        public static string ReadingYamlFileFromBlobFailed = "Failed to read YML file from blob ";
        public static string APIDataHandler = "APIDataHandler";
        public static string APIDataHandlerMethodCalledInInputHandler = "APIDataHandler method in APIDataHandler called";
        public static string APIDataHandlerMethodFailedInInputHandler = "APIDataHandler method in APIDataHandler failed";
        public static string GetInputHandlerHTTPCredentialsCalledInAPIInputHandler = "GetInputHandlerHTTPCredentials method in APIHandler called";
        public static string GetInputHandlerHTTPCredentialsFailedInAPIInputHandler = "GetInputHandlerHTTPCredentials method in APIHandler failed";
        public static string DownloadDataCalledCalledInAPIInputHandler = "Basic DownloadData method in api client called";
        public static string DownloadDataMethodFailedInAPIInputHandler = "Basic DownloadData method in api client failed. ";
        public static string CheckParamsMethod = "CheckParams";
        public static string CheckParamsMethodCalled = "CheckParams method in input handler called";
        public static string CheckParamsMethodFailed = "CheckParams method in input handler failed ";
        public static string DownloadDataInAPIClientFailed = "DownloadData method in API client failed. ErrorMessage: ";
        public static string DownloadDataInFTPClientFailed = "DownloadData method in FTP client failed. ErrorMessage: ";
        public static string DownloadedFileNotFound = "Downloaded file not found in APIHandler.";
        public static string FTPHandlerFailed = "FTPHandler method in InputHandler failed. ErrorMessage: ";
        public static string SFTPFileNotFound = "SFTP file not found in SFTPHandler.";
        public static string APIConnectionFailed = "API connection is failed in APIClient. ";
        public static string FTPClient = "FTPClient";
        public static string DownloadDataCalledinFTPClient = "DownloadData method in FTPClient called";
        public static string FTPUrlinFTPClient = "ftpUrl in DownloadData FTPClient ";
        public static string FTPDataHandler = "FTPDataHandler";
    }
}
