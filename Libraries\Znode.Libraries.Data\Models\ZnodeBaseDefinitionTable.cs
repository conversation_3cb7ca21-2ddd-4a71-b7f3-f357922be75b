﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeBaseDefinitionTable
{
    public int Erpid { get; set; }

    public string? Name { get; set; }

    public string? Format { get; set; }

    public string? Version { get; set; }

    public string? DataSource { get; set; }

    public string? DataDestination { get; set; }

    public string? TriggerOrigin { get; set; }

    public string? Description { get; set; }

    public string? Tags { get; set; }

    public string? Access { get; set; }

    public string? Library { get; set; }

    public string? Collection { get; set; }

    public string? ProcessorFileName { get; set; }

    public string? MethodName { get; set; }

    public bool IsCustomProcessingRequired { get; set; }

    public int? ErpexchangeId { get; set; }

    public string? YamlfileName { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeDataExchangeLibraryTable? Erpexchange { get; set; }

    public virtual ICollection<ZnodeErpExchangeConfigurationDataLog> ZnodeErpExchangeConfigurationDataLogs { get; set; } = new List<ZnodeErpExchangeConfigurationDataLog>();

    public virtual ICollection<ZnodeImportLog> ZnodeImportLogs { get; set; } = new List<ZnodeImportLog>();

    public virtual ICollection<ZnodeImportProcessLog> ZnodeImportProcessLogs { get; set; } = new List<ZnodeImportProcessLog>();

    public virtual ICollection<ZnodeScheduleConfigurationTable> ZnodeScheduleConfigurationTables { get; set; } = new List<ZnodeScheduleConfigurationTable>();

    public virtual ZnodeStatusActivationTable? ZnodeStatusActivationTable { get; set; }

    public virtual ZnodeTransmissionConfigurationTable? ZnodeTransmissionConfigurationTable { get; set; }
}
