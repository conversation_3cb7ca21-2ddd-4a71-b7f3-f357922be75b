﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Model
{
    public class SchedulerConfigurationModel
    {
        public int ERPBaseDefinitionSchedulerId { get; set; }

        public string? SchedulerType { get; set; }

        public string? SchedulerName { get; set; }

        public string? SchedulerFrequency { get; set; }

        public DateTime? StartDate { get; set; }

        public string? CronExpression { get; set; } = null!;

        public string? ProcessorFileName { get; set; }

        public int? ERPBaseDefinitionId { get; set; }

        public string HangfireJobId { get; set; } = string.Empty;

        public bool IsInstantJob { get; set; }

        public DateTime? LastRunTime { get; set; }
    }
}
