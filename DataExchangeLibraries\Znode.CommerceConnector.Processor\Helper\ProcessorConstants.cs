﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Processor.Helper
{
    public static class ProcessorConstants
    {
        public const string ProcessData = "ProcessData";
        public const string InputHandlerInventoryProcessorCalled = "Input Handler from Inventory Processor called";
        public const string FSCRealTimeOrderProcessorProcessorCalled = "FSCRealTimeOrderProcessor Processor called";
        public const string InputHandlerInventoryProcessorFailed = "Input Handler from Inventory Processor failed";
        public const string InputHandlerFSCRealTimeOrderProcessorProcessorFailed = "Input Handler from FSCRealTimeOrderProcessor Processor failed";
        public const string InputHandlerOrderProcessorCalled = "Input Handler from Order Processor called";
        public const string InputHandlerOrderProcessorFailed = "Input Handler from Order Processor failed";
        public const string InvalidTempTableNameNotConstructedProperly = "tempTableName invalid. TempTable not constructed properly, please check logs.";
        public const string ParserInventoryProcessorCalled = "Parser called from Inventory Processor called";
        public const string ParserFSCRealTimeOrderProcessorProcessorCalled = "Parser called from FSCRealTimeOrderProcessor Processor called";
        public const string ParserOrderProcessorCalled = "Parser called from Order Processor called";
        public const string OutputHandlerInventoryProcessorCalled = "Output Handler from Inventory Processor called";
        public const string OutputHandlerFSCRealTimeOrderProcessorProcessorCalled = "Output Handler from FSCRealTimeOrderProcessor Processor called";
        public const string OutputHandlerOrderProcessorCalled = "Output Handler from Order Processor called";
        public const string Boolean = "Boolean";
        public const string CallOutputHandlerMethodCalledInOutputHandlerInitializer = "CallOutputHandler method in OutputHandlerInitializer called";
        public const string InputHandlerProcessorCalled = "Input Handler from {0} Processor called";
        public const string ParserProcessorCalled = "Parser called from {0} Processor called";
        public const string OutputHandlerProcessorCalled = "Output Handler from {0} Processor called";
        public const string InputHandlerProcessorFailed = "Input Handler from {0} Processor failed";
        public const string client_id = "client_id";
        public const string client_secret = "client_secret";
        public const string grant_type = "grant_type";
        public const string scope = "scope";
        public static string DataNotAvailable = "Data is not available for processing.";
    }
}
