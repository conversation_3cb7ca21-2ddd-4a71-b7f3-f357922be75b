﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Parser.Helper
{
    public static class Constants
    {
        public static string ParseData = "ParseData";
        public static string ParseDataMethodInParserCalled = "ParseData method in Parser called";
        public static string ParserDataHelper = "ParserDataHelper";
        public static string GetFormatMethodCalledInSFTPInputHandler = "GetFormat method called in SFTP inputhandler ";
        public static string GetFormatMethodFailedInSFTPInputHandler = "GetFormat method failed in SFTP inputhandler ";
        public static string GetImportTemplateFailed = "GetImportTemplate Method failed  ";
        public static string Source = "Source";
        public static string Destination = "Destination";
        public static string CSV = "CSV";
        public static string JSON = "JSON";
        public static string XML = "XML";
        public static string ParseYAMLData = "ParseYAMLData";
        public static string ParseYAMLDataMethodCalledinParser = "ParseYAMLData method in CSVParser called";
        public static string ParseYAMLDataMethodFailedinParser = "ParseYAMLData method in CSVParser failed ";
        public static string CSVMethodCalled = "ParseYAMLData method : format : CSV";
        public static string JSONMethodCalled = "ParseYAMLData method : format : JSON";
        public static string XMLMethodCalled = "ParseYAMLData method : format : XML";
        public static string TemplateNotFound = "YAML Template not found";
    }
}