﻿using System.ComponentModel.DataAnnotations;
using System.Reflection;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.Models.WebGridModels;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Core.Helper
{
    public static class GridHelper
    {
        /// <summary>
        /// Generate dynamic grid along with filtered and sorted data.
        /// </summary>
        /// <typeparam name="T">generic type</typeparam>
        /// <param name="list">List of data to show on grid</param>
        /// <param name="request">grid request model</param>
        /// <returns>GridResponseModel</returns>
        public static GridResponseModel GenerateDynamicGrid<T>(List<T> list, GridModel request)
        {
            var data = list.AsEnumerable();
            return new GridResponseModel
            {
                Data = data.Cast<dynamic>().ToList(),
                Columns = GenerateWebGridColumn<T>(),
                TotalCount = list.Count,
                PageSize = request.PageSize,
                FilteredCount = data.Count()
            };
        }

        /// <summary>
        /// Bind grid column data to get the datatype, custom display name, etc. of columns.
        /// </summary>
        /// <typeparam name="T">generic type</typeparam>
        /// <returns>List of WebGridColumn</returns>
        public static List<WebGridColumn> GenerateWebGridColumn<T>()
        {
            return typeof(T).GetProperties()
                .Select(p => new WebGridColumn
                {
                    Field = HelperUtility.ToCamelCase(p.Name),
                    Title = p.GetCustomAttribute<DisplayAttribute>()?.Name ?? p.Name,
                    Sorter = HelperUtility.GetSorterType(p.PropertyType),
                    DataType = HelperUtility.GetDataType(p.PropertyType),
                    Sortable = HelperUtility.IsColumnSortable(p.Name)
                }).ToList();
        }
    }
}