﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var isFromAddNew = ViewBag.isFromAddNew ?? false;
    var isFromQuickView = ViewBag.isFromQuickView ?? false;
}

@if ((isFromAddNew || Model?.DataExchangeLibraryId == 0) && !isFromQuickView)
{
    @Html.Partial("_CreateEditBaseDefinitionDetails", Model)
}
else
{
    @Html.Partial("_NativeBaseDefinitionDetails", Model)
}