﻿using Newtonsoft.Json;
using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Text;
using System.Text.Json;
using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Model.Models;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Processor
{
    public class InforAPItoZnodeInventoryProcessor : IProcessor
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public InforAPItoZnodeInventoryProcessor(IOutputHandlerInitializer handler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = handler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

        public dynamic ProcessData(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            try
            {
                dynamic data = "";
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);

                WebHeaderCollection headers = InputHandlerHeaders(erpId);
                //Download the data
                data = _inputHandler.InputHandler(erpId, processorDetails.LogId,InputHandlerRequestBody(), headers, inputModel);
                Type outputType = ((object)data).GetType();

                if (outputType.Name == ProcessorConstants.Boolean && data == false)
                {
                    MongoLogging.LogMessage(ProcessorConstants.InvalidTempTableNameNotConstructedProperly, ProcessorConstants.ProcessData, TraceLevel.Error);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, string.Empty, false, ProcessorConstants.InvalidTempTableNameNotConstructedProperly);
                    if (!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, ProcessorConstants.ProcessData, TraceLevel.Error);
                    return false;
                }
                else
                {
                    MongoLogging.LogMessage(ProcessorConstants.ParserInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                    //Process the data
                    dynamic yamlModel = _inputHandler.GetYAMLDataHandler(erpId);
                    data = _iParser.ParseYAMLData(data, erpId, yamlModel);

                    if (HelperUtility.IsNotNull(data) && data != "")
                    {
                        MongoLogging.LogMessage(ProcessorConstants.OutputHandlerInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                        //Upload the data

                        ImportModel importModel = new ImportModel();

                        HTTPConfigurationModel httpConfigurationModel = _outputHandler.GetOutputHandlerAPICredentials(erpId);

                        if (HelperUtility.IsNotNull(httpConfigurationModel))
                            importModel = OutputHandlerRequestBody(data);

                        return _outputHandler.OutputHandler(data, erpId, OutputHandlerRequestBody(data), new WebHeaderCollection(), processorDetails, inputModel);
                        
                    }
                    MongoLogging.LogMessage(ProcessorConstants.OutputHandlerInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, string.Empty, false, ProcessorConstants.DataNotAvailable);
                    if (!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, ProcessorConstants.ProcessData, TraceLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerInventoryProcessorFailed + ex.Message, ProcessorConstants.ProcessData, TraceLevel.Info);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, string.Empty, false, ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, ProcessorConstants.ProcessData, TraceLevel.Error);
                return false;
            }
        }

        private dynamic OutputHandlerRequestBody(dynamic data)
        {
            byte[] byteArray = Encoding.UTF8.GetBytes(data);

            using (var outputStream = new MemoryStream())
            {
                using (var brotliStream = new BrotliStream(outputStream, CompressionLevel.Optimal))
                {
                    brotliStream.Write(byteArray, 0, byteArray.Length);
                    brotliStream.Flush();
                }
                byteArray = outputStream.ToArray();
            }

            ImportModel importModel = new ImportModel
            {
                ImportType = "Inventory",
                ImportData = byteArray,
                LocaleCode = "1",
                TouchPointName = "InventoryTouchpoint",
                Properties = new Dictionary<string, string>()
            };
            return importModel;
        }

        private dynamic InputHandlerRequestBody()
        {
            InforAPIRequestModel request = new InforAPIRequestModel
            {
                companyNumber = 1,
                operatorInit = "ECOM",
                operatorPassword = "MIS#1ecom",
                customerNumber = 10882,
                ediPartnerCode = "",
                shipTo = "",
                getPriceBreaks = true,
                useDefaultWhse = true,
                sendFullQtyOnOrder = true,
                checkOtherWhseInventory = true,
                pricingMethod = "full",
                tOemultprcinV2 = new tOemultprcinV2
                {
                    tOemultprcinV2List = new List<tOemultprcinV2Item>
                    {
                        new tOemultprcinV2Item
                        {
                            seqNo = 1,
                            whse = "",
                            prod = "10029421",
                            qtyOrd = 1,
                            unit = "each"
                        }
                    }
                }
            };
            return new { request = request };
        }

        private WebHeaderCollection InputHandlerHeaders(int erpId)
        {
            HTTPConfigurationModel httpConfigurationModel = _inputHandler.GetInputHandlerHTTPCredentials(erpId);
            WebHeaderCollection headers = new WebHeaderCollection
            {
                { "Authorization", $"Bearer {GetBearerToken(httpConfigurationModel)}"},
                { "Content-Type", "application/json" },
            };
            return headers;
        }

        public dynamic GetBearerToken(HTTPConfigurationModel httpConfigurationModel)
        {
            BearerTokenModel bearerTokenModel = new BearerTokenModel();
            var keyValues = new List<KeyValuePair<string, string>>()
            {
                new KeyValuePair<string, string>("client_id",httpConfigurationModel.ClientId),
                new KeyValuePair<string, string>("username", "MACGREGORS_TST#ydW884eZx6sipypn6FST4cVCdQi_dBAt7m17eSacfWeNSRyJEYvMLMS-FT45Y2pjBHU-OG8CxcThhNavIPHn9g"),
                new KeyValuePair<string, string>("password", "Y5aWiyzHXzaHCZdjF7Osc9O7Hhp4WA18l5n7c-DDOkZvGqHctPCJ4njpcJMLuieAXKfEGAf1MlMn6O6qZOOlNA"),
                new KeyValuePair<string, string>("client_secret", httpConfigurationModel.ClientSecret),
                new KeyValuePair<string, string>("grant_type", httpConfigurationModel.GrantType)
            };

            ApiClient apiClient = new ApiClient();
            RequestModel requestModel = apiClient.SetRequestModel(httpConfigurationModel.AccessTokenURL);
            requestModel.RequestBody = new FormUrlEncodedContent(keyValues).ReadAsStringAsync().Result;
            requestModel.ContentType = "application/x-www-form-urlencoded";
            TokenResponseModel apiResponse = apiClient.PostRequest<TokenResponseModel>(requestModel, out string statusCode);
            return apiResponse?.access_token;
        }

    }
}
