﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeHeaderParameterTable
{
    public int HeaderId { get; set; }

    public int TransmissionId { get; set; }

    public string? Key { get; set; }

    public string? Value { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public string? Type { get; set; }

    public virtual ZnodeApiconfigurationTable Transmission { get; set; } = null!;
}
