﻿.left-panel-side {
    position: fixed;
    height: 100%;
    color: $base-color-white;
    top: 113px;
    left: 0;
    background: $aside-panel-bgcolor;
    z-index: 101;
    overflow-y: auto;
    -moz-transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
    -o-transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
    -webkit-transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
    transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
    padding-top: 15px;
    box-shadow: inset -10px 0px 8px -8px $box-border-shadow;
    width: inherit;

    li {
        a {
            padding: 10px 10px 10px 20px;
            color: $aside-tab-text-fg;
            display: block;
            text-transform: uppercase;
            font-size: $base-font-size;
            font-family: $base-font-family-condensed;

            &:hover, &:focus, &:active {
                background-color: $aside-tab-selected-bg;
                color: $base-color-primary;
                -moz-transition: all 0.2s linear 0s;
                -webkit-transition: all 0.2s linear 0s;
                transition: all 0.2s linear 0s;
                box-shadow: 0 4px 8px -2px $box-border-shadow;
                border: none;
                border-radius: 0;
            }
        }

        .active {
            color: $base-color-primary;
            background-color: $aside-tab-selected-bg;
            -moz-transition: all 0.2s linear 0s;
            -webkit-transition: all 0.2s linear 0s;
            transition: all 0.2s linear 0s;
            box-shadow: 0 4px 8px -2px $box-border-shadow;
            border: none;
            border-radius: 0;
        }
    }

    .left-panel-side-ul {
        padding-left: 15px !important;
    }

    .nav-tabs .nav-link {
        margin-bottom: 0;
        border: none;
    }
}
