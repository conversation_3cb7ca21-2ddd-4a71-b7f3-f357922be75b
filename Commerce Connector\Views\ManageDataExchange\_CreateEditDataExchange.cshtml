﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{

    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();
    if (string.IsNullOrEmpty(token))
        token = Context.Request.Query[CCAdminConstant.CCAdminToken].ToString();

}

<form action="/commerce-connector/ManageDataExchange/CreateEditDataExchange?@CCAdminConstant.CCAdminToken=@token"
      method="post"
      id="frmAddNewData"
      onsubmit="return ManageDataExchange.prototype.ValidateCreateEditForm()">


    <div class="col-md-12 nopadding dashboard-title">
        <div class="title-container d-flex justify-content-between">

            <h1 data-test-selector="hdgManageDataExchange" aria-label="Manage Data Exchange">@CommerceConnector_Resources.ManageDataExchange @(!string.IsNullOrEmpty(Model.Name) ? " - " + Model.Name : "")</h1>
            <div class="alert-message">
                <div class="messageBoxContainer text-center" id="exchangeLibraryNotification"></div>
                @{
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divErrorMessage">@TempData["ErrorMessage"]</div>
                    }
                }
            </div>
            <div class="mt-2">
                <button type="submit" id="saveBaseDataExchange" class="btn-text btn-text-secondary me-2" data-test-selector="btnSave" aria-label="Save Base Data Exchange">@CommerceConnector_Resources.Save</button>
                <a href="@Url.Action("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token })" class="btn-text btn-text-secondary" data-test-selector="lnkCancel" aria-label="Cancel Manage Data Exchange">@CommerceConnector_Resources.Cancel</a>
            </div>
        </div>
    </div>

    <!-- Left Panel -->
    <div class="col-md-12 page-container">

        <div class="row">
            <div class="col-md-3 col-lg-2">
                <div class="left-panel-side list-group" id="configTabs" aria-label="Configuration Navigation">
                    <ul class="left-panel-side-ul nav nav-tabs border-0 d-block" data-test-selector="listLeftPanelSideUl">
                        <li type="button" class="nav-item active" data-test-selector="listConfigurationSet" data-section="BaseDef"><a class="nav-link active">@CommerceConnector_Resources.ConfigurationSet</a></li>
                    </ul>
                </div>
            </div>
            <div class="col-md-9 col-lg-10 px-3">
                @Html.Partial("_BaseDefinition.cshtml", Model)
            </div>
        </div>
    </div>
</form>
