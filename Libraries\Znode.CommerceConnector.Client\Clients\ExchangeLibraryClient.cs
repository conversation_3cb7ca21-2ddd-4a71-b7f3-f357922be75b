﻿using Newtonsoft.Json;
using Znode.CommerceConnector.Client.Clients;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client
{
    public class ExchangeLibraryClient : BaseClient, IExchangeLibraryClient
    {
        public DataExchangeListModel GetLibrariesList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex, int? pageSize)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.List();
            endpoint += BuildEndpointQueryString(null, filters, sorts, pageIndex, pageSize);
            DataExchangeListModel standardDataExchangeListModel = apiClient.GetRequest<DataExchangeListModel>(endpoint);
            return standardDataExchangeListModel;
        }

        /// <summary>
        /// Inserts data into ZNodeBaseDefinition table based on ids.
        /// </summary>
        /// <param name="ids"></param>
        /// <returns>bool result</returns>

        public bool AddSelectedExchange(AddSelectedExchangeModel addSelectedExchangeModel)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.AddSelectedExchange();

            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(addSelectedExchangeModel);
           
            TrueFalseResponse response = apiClient.PostRequest<TrueFalseResponse>(requestModel, out string statusCode);

            return response.IsSuccess;
        }

        /// <summary>
        /// Validate whether Exchange name exists or not in Base Definition. 
        /// </summary>
        /// <param name="exchangeName"></param>
        /// <returns>Will return true if Exchange name is proper and not exists.</returns>
        public bool ValidateExchangeName(string exchangeName)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.ValidateExchangeName(exchangeName);           
            TrueFalseResponse response = apiClient.GetRequest<TrueFalseResponse>(endpoint);

            return response.IsSuccess;
        }
    }
}
