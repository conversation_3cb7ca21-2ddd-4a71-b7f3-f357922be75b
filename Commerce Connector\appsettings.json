{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"ZnodeMongoDBForLog": "mongodb://localhost:27017/Znode_10xFeature_Docker_LogMessage"}, "AllowedHosts": "*", "appsettings": {"CommerceConnectorApi": "https://localhost:7176", "ZnodeAPI": "https://apigateways-z10-lt.amla.io", "ZnodeAuthorizationKey": "Basic YXBpLXoxMC1sdC5hbWxhLmlvfGEyYzMyZDI2LWZiNTYtNGUyZC1hYTQ0LTEwYWU0MDFhMDk3MA==", "EnableTokenAuth": "false"}, "Cors": {"AllowedOrigins": ["http://localhost:7176"]}}