﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>f236f988-9be5-4ec8-8bbb-49b73a8229a6</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Scripts\**" />
    <Content Remove="Scripts\**" />
    <EmbeddedResource Remove="Scripts\**" />
    <None Remove="Scripts\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="wwwroot\Scripts\CommerceConnector\AddSelectedExchange.ts" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="BuildBundlerMinifier" Version="3.2.449" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.3.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.TypeScript.MSBuild" Version="5.8.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Libraries\Znode.CommerceConnector.Core\Znode.CommerceConnector.Core.csproj" />
    <ProjectReference Include="..\Libraries\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj" />
    <ProjectReference Include="..\Libraries\Znode.Libraries.Resources\Znode.Libraries.Resources.csproj" />
  </ItemGroup>

  <ItemGroup>
    <TypeScriptCompile Include="wwwroot\Scripts\CommerceConnector\AddSelectedExchange.ts" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="wwwroot\Scripts\Bundles\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Views\DataExchange\DataExchange.cshtml">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
