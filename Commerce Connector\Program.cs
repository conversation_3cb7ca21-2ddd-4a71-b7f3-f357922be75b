﻿using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Core;
using Znode.Libraries.Common.Helper;

var builder = WebApplication.CreateBuilder(args);



builder.Services.AddMvc();
builder.Services.AddAutoMapper(typeof(AutoMapperConfig).Assembly);
builder.Services.AddControllers();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(20);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});
builder.RegisterDI();

builder.Services.AddAntiforgery(x =>
{
    x.SuppressXFrameOptionsHeader = false;
});
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});

builder.Configuration
       .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
       .AddEnvironmentVariables();
BaseEndpoint._staticServiceProvider = builder.Services.BuildServiceProvider();

var app = builder.Build();

BaseEndpoint.Configure(app.Services);
app.UsePathBase("/commerce-connector");
app.UseExceptionHandler("/Home/Error");
app.UseStatusCodePagesWithReExecute("/Home/PageNotFound/{0}");

if (!app.Environment.IsDevelopment())
{
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

if (BaseEndpoint.EnableTokenAuth)
{
    app.UseMiddleware<TokenAccessMiddleware>();
}

app.UseSession();
app.UseAuthorization();

app.Use(async (context, next) =>
{
    if (!context.Response.Headers.ContainsKey("X-Frame-Options"))
    {
        context.Response.Headers.Add("X-Frame-Options", "Allow");
    }
    await next();
});

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=DataExchange}/{action=GetDataExchangeLibraryList}");

app.Run();