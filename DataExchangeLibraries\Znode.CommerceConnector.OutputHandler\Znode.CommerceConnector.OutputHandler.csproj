﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentFTP" Version="52.1.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="SSH.NET" Version="2025.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Libraries\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
    <ProjectReference Include="..\..\Libraries\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\..\Libraries\Znode.CommerceConnector.Client\Znode.CommerceConnector.Client.csproj" />
	  <ProjectReference Include="..\..\Libraries\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
	  <ProjectReference Include="..\..\Libraries\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
	</ItemGroup>
</Project>
