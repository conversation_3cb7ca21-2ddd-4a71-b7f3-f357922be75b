﻿using Microsoft.AspNetCore.Mvc.Rendering;

using System.ComponentModel.DataAnnotations;
using Znode.CommerceConnector.Core.Helper;
using Znode.Libraries.Resources.CommerceConnector_Resources;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class SchedulerConfigurationViewModel : BaseViewModel
    {
        public int ERPBaseDefinitionSchedulerId { get; set; }

        [Display(Name = CCAdminConstant.LabelScheduleType, ResourceType = typeof(CommerceConnector_Resources))]
        [Required(ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.ReqSchedulerTypeErrorMessage)]
        public string? SchedulerType { get; set; }

        [Display(Name = CCAdminConstant.LabelScheduleName, ResourceType = typeof(CommerceConnector_Resources))]
        [MaxLength(100, ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.SchedulerNameMaxLengthErrorMessage)]
        [Required(ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.ReqSchedulerNameErrorMessage)]
        public string? SchedulerName { get; set; }

        [Display(Name = CCAdminConstant.LabelFrequency, ResourceType = typeof(CommerceConnector_Resources))]
        public string? SchedulerFrequency { get; set; }

        [Display(Name = CCAdminConstant.LabelRunDate, ResourceType = typeof(CommerceConnector_Resources))]
        [Required(ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.ReqRunDateErrorMessage)]
        public DateTime? StartDate { get; set; }

        [Display(Name = CCAdminConstant.LabelRunTime, ResourceType = typeof(CommerceConnector_Resources))]
        [Required(ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.ReqRunTimeErrorMessage)]

        public string StartTime { get; set; }

        [Display(Name = CCAdminConstant.LabelCronExpression, ResourceType = typeof(CommerceConnector_Resources))]
        [MaxLength(100, ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.CronMaxLengthErrorMessage)]
        [Required(ErrorMessageResourceType = typeof(CommerceConnector_Resources), ErrorMessageResourceName = CCAdminConstant.InvalidCronExpressionErrorMessage)]

        public string? CronExpression { get; set; } = null!;

        public string? ProcessorFileName { get; set; }

        public int ERPBaseDefinitionId { get; set; }

        public string? HangfireJobId { get; set; } = string.Empty;
        public DateTime? LastRunTime { get; set; }

        public List<SelectListItem> ScheduleTypeList { get; set; } = new List<SelectListItem>
        {
            new SelectListItem { Value = CCAdminConstant.Scheduled, Text = CommerceConnector_Resources.LabelRunOnSchedule, Selected = true },
            new SelectListItem { Value = CCAdminConstant.OnDemand, Text = CommerceConnector_Resources.LabelRunOnDemand },
            new SelectListItem { Value = CCAdminConstant.RealTime, Text = CommerceConnector_Resources.LabelRunOnRealTime },
        };

        public List<SelectListItem> ScheduleFrequencyList { get; set; } = new List<SelectListItem>
        {
            new SelectListItem { Value = CCAdminConstant.OneTime, Text = CommerceConnector_Resources.LabelOneTime, Selected = true},
            new SelectListItem { Value = CCAdminConstant.Recurring, Text = CommerceConnector_Resources.LabelRecurring },
        };
    }
}
