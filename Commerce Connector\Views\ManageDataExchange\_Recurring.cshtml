﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel

@* Helptext for recurring *@

<div class="cron-expression-info p-3">
    @Html.Partial("_RecurringHelpText.cshtml")
</div>
<div class="row">
    <div class="col-md-12 col-lg-6 mt-3">
        <div class="control-label mb-2">
            @Html.LabelFor(model => model.SchedulerConfigurationViewModel.CronExpression, new { @class = "required", @data_test_selector = "lblCronExpression" })
        </div>
        <div class="control-md">
            @Html.TextBoxFor(model => model.SchedulerConfigurationViewModel.CronExpression, string.Empty, new { @data_test_selector = "txtCronExpression", @id = "txtCronExpression", @autocomplete = "off", @maxlength = 100, aria_label = "Scheduler Cron Expression" })
            <span id="valCronExpressionError" class="text-danger field-validation-error"></span>
        </div>
        @Html.HiddenFor(x => x.SchedulerConfigurationViewModel.CronExpression, new { @id = "hdnCronExpr" })

    </div>
</div>