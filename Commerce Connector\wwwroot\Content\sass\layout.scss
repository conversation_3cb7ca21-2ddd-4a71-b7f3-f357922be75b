﻿//All layout Code Such as <PERSON><PERSON>, <PERSON>er, Main Container, Aside Panel, Filter, General CSS, Report.

body {
    padding-top: 43px;
    padding-bottom: 25px;
    background-color: $base-color-white;
}

// Header Section.
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    z-index: 9988;
    line-height: 26px;
    color: $base-color-white;
    background: $base-color-primary;
    border-bottom: 1px solid $base-color-white;
    border-top: 2px solid $base-color-secondary;
}

header .logo {
    display: block;
    color: $base-color-white;
    font-size: $base-font-size + 8;
    text-transform: uppercase;
    width: fit-content;
    padding: 12px 6px;
}

/*Search CSS*/

.filter-search {
    width: auto;

    input[type="text"] {
        width: 200px;
        height: 34px;
        padding-right: 18px;

        &::placeholder {
            font-style: italic; /* Italic only for placeholder */
        }
    }

    .btn-search {
        border-left: 1px solid $border-bgcolor-primary;
        background-color: $base-color-white;
        border-color: $border-bgcolor-primary;
        border-style: solid;
        border-width: 1px 1px 1px 0;
        color: $aside-tab-text-fg;
        height: 34px;
        line-height: 34px;
        padding-right: 6px;
        font-size: $base-font-size + 2;
        margin-left: -3px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
    }
}

.wrapper {
    min-height: 100%;

    .table {
        tbody tr td, thead tr th {
            vertical-align: middle;
            height: auto;
        }
    }
}

.title-container {
    height: 70px;
    padding: 14px 16px;
    position: fixed;
    z-index: 999;
    background-color: $base-color-white;
    width: 100%;
    box-shadow: 0 5px 5px -5px $box-border-shadow;

    h1 {
        font-size: $base-font-size + 10;
        margin: 0;
        color: $base-color-primary;
        line-height: 49px;
        font-family: $base-font-family-bold;
        text-transform: uppercase;
    }

    .total-count {
        font-size: $base-font-size;
        vertical-align: middle;
        padding: 1px 0 0 7px;

        .btn {
            display: inline-block;
            padding: 6px 10px;
            background: $base-color-secondary;
            line-height: 10px;
            vertical-align: middle;
            border-radius: 3px;
            margin: 0;
            margin-top: -3px;
            color: $base-color-white;
            border: $base-color-secondary;
        }
    }
}

// Footer Section.
footer {
    z-index: 500;
    border-top: 1px solid $border-bgcolor-default;
    text-align: center;
    color: $base-color-primary;
    height: 25px;
    line-height: 24px;
    padding: 0 15px;
    font-size: $base-font-size;
    border-radius: 0;

    .footer-container {
        p {
            margin-bottom: 0;
        }
    }
}
