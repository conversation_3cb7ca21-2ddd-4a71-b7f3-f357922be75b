{"name": "@types/jquery", "version": "3.5.32", "description": "TypeScript definitions for jquery", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jquery", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu", "url": "https://github.com/leonard-thieu"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "githubUsername": "choff<PERSON>", "url": "https://github.com/choffmeister"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>ei"}, {"name": "<PERSON><PERSON>", "githubUsername": "tasoili", "url": "https://github.com/tasoili"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seanski"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "G<PERSON>uz", "url": "https://github.com/Guuz"}, {"name": "<PERSON>", "githubUsername": "k<PERSON><PERSON>lin", "url": "https://github.com/ksummerlin"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "basarat", "url": "https://github.com/basarat"}, {"name": "<PERSON>", "githubUsername": "n<PERSON><PERSON><PERSON>", "url": "https://github.com/nwolverson"}, {"name": "<PERSON>", "githubUsername": "derekcicerone", "url": "https://github.com/derekcicerone"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/AndrewGaspar"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seikichi"}, {"name": "<PERSON>", "githubUsername": "benjamin<PERSON>man", "url": "https://github.com/benjaminjackman"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/JoshStrobl"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "githubUsername": "DickvdBrink", "url": "https://github.com/DickvdBrink"}, {"name": "<PERSON>", "githubUsername": "King2500", "url": "https://github.com/King2500"}, {"name": "<PERSON>", "githubUsername": "terrymun", "url": "https://github.com/terrymun"}, {"name": "<PERSON>", "githubUsername": "martin-badin", "url": "https://github.com/martin-badin"}, {"name": "<PERSON>", "githubUsername": "princefishthrower", "url": "https://github.com/princefishthrower"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jquery"}, "scripts": {}, "dependencies": {"@types/sizzle": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "6be8791febca4b139d557a33a9f61b3ae57b9fc52f3a4c4aba963f773e3f7766", "typeScriptVersion": "4.8"}