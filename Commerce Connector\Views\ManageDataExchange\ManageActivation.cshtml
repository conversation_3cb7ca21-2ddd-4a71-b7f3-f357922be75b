﻿ @model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="row">

    <div class="col-md-12 col-lg-6 mb-3">
        <label asp-for="StatusActivationViewModel.InformationNotification" class="form-label" data-test-selector="lblInformationNotifications" aria-label="Information Notification"></label>
        <input asp-for="StatusActivationViewModel.InformationNotification" id="InformationNotification" class="form-control" placeholder="Enter multiple emails, separated by commas" data-test-selector="txtInformationNotification" aria-label="Information Notification Email" />
        <span id="InformationNotificationError" class="text-danger field-validation-error" </span>
    </div>

    <div class="col-md-12 col-lg-6 mb-3">
        <label asp-for="StatusActivationViewModel.WarningNotification" class="form-label" data-test-selector="lblWarningNotification" aria-label="Warning Notification"></label>
        <input asp-for="StatusActivationViewModel.WarningNotification" id="WarningNotification" class="form-control" placeholder="Enter multiple emails, separated by commas" data-test-selector="txtWarningNotification" aria-label="Warning Notification Email" />
        <span id="WarningNotificationError" class="text-danger field-validation-error"></span>
    </div>

    <div class="col-md-12 col-lg-6 mb-3">
        <label asp-for="StatusActivationViewModel.ErrorNotification" class="form-label" data-test-selector="lblErrorNotification" aria-label="Error Notification"></label>
        <input asp-for="StatusActivationViewModel.ErrorNotification" id="ErrorNotification" class="form-control" placeholder="Enter multiple emails, separated by commas" data-test-selector="txtErrorNotification" aria-label="Error Notification Email" />
        <span id="ErrorNotificationError" class="text-danger field-validation-error"></span>
    </div>
</div>


