﻿namespace Znode.CommerceConnector.Model
{
    public class SFTPConfigurationModel
    {
        public int TransmissionId { get; set; }
        public int ConfigurationId { get; set; }
        public string ServerAddress { get; set; }
        public string FolderPath { get; set; }
        public string FileName { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string ActionAfterRetrival { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
        public string Type { get; set; }
        public int PortNumber { get; set; }
    }
}
