﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.3.0" />

    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="NCrontab.Signed" Version="3.3.3" />
    <PackageReference Include="YamlDotNet" Version="16.3.0" />
    <PackageReference Include="Znode10.Libraries.Common.Helper" Version="1.0.0" />
    <PackageReference Include="Znode10.Libraries.Common.Logger" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Znode.CommerceConnector.Client\Znode.CommerceConnector.Client.csproj" />
    <ProjectReference Include="..\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
    <ProjectReference Include="..\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
    <ProjectReference Include="..\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj" />
    <ProjectReference Include="..\Znode.Libraries.Resources\Znode.Libraries.Resources.csproj" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
</Project>
