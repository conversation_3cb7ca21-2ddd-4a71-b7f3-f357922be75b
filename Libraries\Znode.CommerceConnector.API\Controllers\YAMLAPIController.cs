﻿using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.API.Services;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace Znode.CommerceConnector.API.Controllers
{
    public class YAMLAPIController : BaseController
    {
        public readonly IYAMLService _yAMLService;

        public YAMLAPIController(IYAMLService yAMLService)
        {
            _yAMLService = yAMLService;
        }

        [Route("DownloadSample")]
        [HttpPost]
        public IActionResult DownloadSample([FromBody] MediaConfigurationModel mediaConfiguration)
        {
            IActionResult response;
            try
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.DownloadSampleAPIControllerCalled, CommerceConnectorConstants.APIController, TraceLevel.Info);
                dynamic data = _yAMLService.DownloadSample(mediaConfiguration);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.DownloadSampleAPIControllerFailed, CommerceConnectorConstants.APIController, TraceLevel.Error);
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        [Route("SaveYaml")]
        [HttpPost]
        public IActionResult SaveYaml([FromBody] YAMLSaveRequestModel fileContent)
        {
            IActionResult response;
            try
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.SaveYAMLAPIControllerCalled, CommerceConnectorConstants.APIController, TraceLevel.Info);
                bool result = _yAMLService.SaveYaml(fileContent);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.SaveYAMLAPIControllerFailed, CommerceConnectorConstants.APIController, TraceLevel.Error);
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        [Route("GetSavedYamlData")]
        [HttpPost]
        public IActionResult GetSavedYamlData(int erpId,[FromBody] MediaConfigurationModel mediaConfiguration)
        {
            IActionResult response;
            try
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.GetYamlDataAPIControllerCalled, CommerceConnectorConstants.APIController, TraceLevel.Info);
                dynamic data = _yAMLService.GetSavedYamlData(erpId, mediaConfiguration);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.GetYamlDataAPIControllerCalled, CommerceConnectorConstants.APIController, TraceLevel.Error);
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }
    }
}
