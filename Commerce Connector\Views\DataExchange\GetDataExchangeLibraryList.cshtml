﻿@model Znode.CommerceConnector.Core.ViewModels.StandardDataExchangesListViewModel
@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources
@using Znode.CommerceConnector.Core.ViewModels
@{
    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();
}
<div class="data-exchange page-content"> 
    <div class="controls">
        <div class="filter-search d-flex">
            <input type="text" id="globalSearch" maxlength="130" placeholder="Search..." data-test-selector="txtSearch" aria-label="Search List" />
            <button id="globalSearchbtn" class="btn-search" data-test-selector="btnSearchIcon"><em class="z-search"></em></button>
        </div>
    </div>

    <div id="dataExchangeGrid" class="ag-theme-alpine mt-3" data-test-selector="divDataExchangeGrid"></div>

    <div class="show-per-page d-flex mt-3" style="display: none;">
        <div id="paginationControls" class="me-3" data-test-selector="divPaginationControl"></div>
        <div id="customRowInfo" class="total-record" data-test-selector="divCustomRowInfo" style="display: none;"></div>
        <input type="hidden" id="hdnAdminToken" value="@token" />
        <div class="show-page-count ps-3" style="display: none;">
            <div class="d-flex align-items-center">
                <label class="pe-3" data-test-selector="lblShow">@CommerceConnector_Resources.Show</label>
                <select id="pageSizeSelect" data-test-selector="drpPageSizeSelect" aria-label="Select Number of Page">
                    <option value="10" selected>10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                </select>
            </div>
        </div>
    </div>
    <div id="noRecordsMessage" class="text-center mt-3" style="display: none;" data-test-selector="divNoRecordsFound">
        @CommerceConnector_Resources.NoRecordsFound
    </div>

    <div class="modal fade" id="baseDefinitionModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div id="baseDefinitionContent" class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" data-test-selector="hdgViewDataExchange">@CommerceConnector_Resources.LabelViewDataExchange</h3>
                    <button type="button" class="btn-text-icon" data-bs-dismiss="modal" data-test-selector="btnBack">
                        <em class='z-back'></em>
                        @CommerceConnector_Resources.LabelBack
                    </button>
                </div>
                <div class="modal-body" id="target-exchange-to-display" data-test-selector="divDataExchangeGridDisplay"></div>
            </div>
        </div>
    </div>
</div>