﻿using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;
using System.Net;
using System.Xml.Serialization;

using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client
{
    public class ApiClient
    {
        #region Public Methods

        public RequestModel SetRequestModel(string endpoint)
        {
            RequestModel requestModel = new RequestModel();
            requestModel.DestinationURL = endpoint;
            requestModel.ContentType = "application/json";
            if (endpoint.ToLower().Contains(BaseEndpoint.ZnodeApi.ToLower()))
            {
                requestModel.HeaderCollection = new WebHeaderCollection();
                requestModel.HeaderCollection.Add("Authorization", BaseEndpoint.ZnodeAuthorizationKey);
            }
            return requestModel;
        }

        /// <summary>
        /// Post request to an destinationURL.
        /// </summary>
        /// <typeparam name="T">The type of resource being created.</typeparam>
        /// <param name="requestModel">request model to pass parameter to web request</param>
        /// <returns>Response from web request.</returns>
        public T PostRequest<T>(RequestModel requestModel, out string statusCode, bool returnWithoutDeserialize = false)
        {
            T response;
            statusCode = "";
            try
            {
                if (requestModel == null || string.IsNullOrEmpty(requestModel.DestinationURL) || string.IsNullOrEmpty(requestModel.ContentType))
                {
                    return default(T);
                }

                requestModel.RequestType = "POST";
                response = GetWebResponse<T>(requestModel, out statusCode, returnWithoutDeserialize);
            }
            catch (Exception ex)
            {                
                throw new Exception($"Exception in ApiClient.PostRequest {ex.Message}");
            }
            return response;
        }

        /// <summary>
        /// Put request to an destinationURL.
        /// </summary>
        /// <typeparam name="T">The type of resource being created.</typeparam>
        /// <param name="requestModel">request model to pass parameter to web request</param>
        /// <returns>Response from web request.</returns>
        public T PutRequest<T>(RequestModel requestModel, out string statusCode)
        {
            T response;
            statusCode = "";
            try
            {
                if (requestModel == null || string.IsNullOrEmpty(requestModel.DestinationURL) || string.IsNullOrEmpty(requestModel.ContentType))
                {
                    return default(T);
                }

                requestModel.RequestType = "PUT";
                response = GetWebResponse<T>(requestModel, out statusCode);
            }
            catch (Exception ex)
            {                
                throw new Exception($"Exception in ApiClient.PostRequest {ex.Message}");
            }
            return response;
        }

        /// <summary>
        /// Get request to an destinationURL.
        /// </summary>
        /// <typeparam name="T">The type of resource being created.</typeparam>
        /// <param name="requestModel">request model to pass parameter to web request</param>
        /// <returns>Response from web request.</returns>
        public T GetRequest<T>(string endpoint, bool returnWithoutDeserialize = false)
        {
            T response;
            try
            {
                RequestModel requestModel = SetRequestModel(endpoint);
                if (requestModel == null || string.IsNullOrEmpty(requestModel.DestinationURL) || string.IsNullOrEmpty(requestModel.ContentType))
                {
                    return default(T);
                }

                requestModel.RequestType = "GET";
                response = GetWebResponse<T>(requestModel, out string statusCode, returnWithoutDeserialize);
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ApiClient.GetRequest {ex.Message}");
            }
            return response;
        }

        /// <summary>
        /// Delete request to an destinationURL.
        /// </summary>
        /// <typeparam name="T">The type of resource being created.</typeparam>
        /// <param name="requestModel">request model to pass parameter to web request</param>
        /// <returns>Response from web request.</returns>
        public T DeleteRequest<T>(RequestModel requestModel, out string statusCode)
        {
            T response;
            statusCode = "";
            try
            {
                if (requestModel == null || string.IsNullOrEmpty(requestModel.DestinationURL) || string.IsNullOrEmpty(requestModel.ContentType))
                {
                    return default(T);
                }

                requestModel.RequestType = "DELETE";
                response = GetWebResponse<T>(requestModel, out statusCode);
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ApiClient.GetRequest {ex.Message}");
            }
            return response;
        }
        public T PatchRequest<T>(RequestModel requestModel, out string statusCode)
        {
            T response;
            statusCode = "";
            try
            {
                if (requestModel == null || string.IsNullOrEmpty(requestModel.DestinationURL) || string.IsNullOrEmpty(requestModel.ContentType))
                {
                    return default(T);
                }

                requestModel.RequestType = "PATCH";
                response = GetWebResponse<T>(requestModel, out  statusCode);
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ApiClient.GetRequest {ex.Message}");
            }
            return response;
        }
        #endregion


        #region Private methods

        /// <summary>
        /// This method will deserialize Json response stream
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="response">deserialize object of T</param>
        /// <returns></returns>
        private T DeserializeResponseStream<T>(WebResponse response)
        {
            if (response == null)
            {
                return default(T);
            }

            using (Stream body = response.GetResponseStream())
            {
                if (body == null)
                {
                    return default(T);
                }

                using (StreamReader stream = new StreamReader(body))
                {
                    using (JsonTextReader jsonReader = new JsonTextReader(stream))
                    {
                        JsonSerializer jsonSerializer = new JsonSerializer();
                        try
                        {
                            return jsonSerializer.Deserialize<T>(jsonReader);
                        }
                        catch (JsonReaderException ex)
                        {
                            throw new Exception(ex.Message);
                        }
                        catch (Exception ex)
                        {
                            throw new Exception(ex.Message);
                        }
                    }
                }

            }
        }

        /// <summary>
        /// This method will set the request and provide response from web request
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="requestModel">request model to pass parameter to web request</param>
        /// <returns></returns>
        private T GetWebResponse<T>(RequestModel requestModel, out string statusCode, bool returnWithoutDeserialize = false)
        {
            T response;
            statusCode = "";
            try
            {               
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(requestModel.DestinationURL);

                byte[] bytes;
                request.Method = requestModel.RequestType;
                if (!Equals(requestModel.HeaderCollection, null))
                {
                    request.Headers = requestModel.HeaderCollection;
                }
                request.ContentType = requestModel.ContentType;
                

                request.ServerCertificateValidationCallback += (sender, certificate, chain, sslPolicyErrors) => true;
                if (!string.IsNullOrEmpty(requestModel.RequestBody))
                {
                    bytes = System.Text.Encoding.ASCII.GetBytes(requestModel.RequestBody);
                    request.ContentLength = bytes.Length;

                    Stream requestStream = request.GetRequestStream();
                    requestStream.Write(bytes, 0, bytes.Length);
                    response = GetResponseFromRequest<T>(request, out statusCode, requestModel, returnWithoutDeserialize);
                    requestStream.Close();
                }
                else
                {
                    response = GetResponseFromRequest<T>(request, out  statusCode, requestModel, returnWithoutDeserialize);
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            return response;
        }

        /// <summary>
        /// This method will provide response from web request
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="request">Web request which will provide response</param>
        /// <param name="requestModel"></param>
        /// <returns>Response from web request</returns>
        private T GetResponseFromRequest<T>(HttpWebRequest request, out string statusCode, RequestModel requestModel, bool returnWithoutDeserialize = false)
        {
            T response = Activator.CreateInstance<T>();
            statusCode = "";

            if (request == null)
            {
                return response;
            }

            try
            {
                request.Timeout = 600000;
                using (HttpWebResponse webResponse = (HttpWebResponse)request.GetResponse())
                {
                    if (webResponse.StatusCode == HttpStatusCode.Accepted || webResponse.StatusCode == HttpStatusCode.Created || webResponse.StatusCode == HttpStatusCode.Found || webResponse.StatusCode == HttpStatusCode.OK)
                    {
                        if (returnWithoutDeserialize)
                        {
                            StreamReader reader = new StreamReader(webResponse.GetResponseStream());
                            string str = reader.ReadLine();
                            return (T)Convert.ChangeType(str, typeof(T));
                        }

                        if (requestModel.ContentType == "application/json")
                        {
                            response = DeserializeResponseStream<T>(webResponse);
                            statusCode = webResponse.StatusCode.ToString();
                        }
                        else if (requestModel.ContentType == "application/xml")
                        {
                            Stream responseStream = webResponse.GetResponseStream();
                            string responseStr = new StreamReader(responseStream).ReadToEnd();
                            XmlSerializer serializer = new XmlSerializer(typeof(T));
                            StringReader rdr = new StringReader(responseStr);
                            response = (T)serializer.Deserialize(rdr);
                        }
                        else if(requestModel.ContentType == "application/x-www-form-urlencoded")
                        {
                            response = DeserializeResponseStream<T>(webResponse);
                            statusCode = webResponse.StatusCode.ToString();
                        }
                    }
                }
            }
            catch (WebException ex)
            {
                var webException = ((System.Net.HttpWebResponse)ex.Response);
                if (HelperUtility.IsNull(webException))
                {
                    throw new Exception(ex.Message);
                }         
                using (WebResponse Exresponse = ex.Response)
                {                   
                    HttpWebResponse httpResponse = (HttpWebResponse)Exresponse;
                    using (Stream data = Exresponse.GetResponseStream())
                    using (var reader = new StreamReader(data))
                    {
                        string text = reader.ReadToEnd();
                        throw new Exception(text);
                    }
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception(ex.Message);
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            return response;
        }

        public T GetRequest<T>(RequestModel requestModel, out string statusCode, bool returnWithoutDeserialize = false)
        {
            T response;
            statusCode = "";
            try
            {
                if (requestModel == null || string.IsNullOrEmpty(requestModel.DestinationURL) || string.IsNullOrEmpty(requestModel.ContentType))
                {
                    return default(T);
                }

                requestModel.RequestType = "GET";
                response = GetWebResponse<T>(requestModel, out  statusCode, returnWithoutDeserialize);
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ApiClient.GetRequest {ex.Message}");
            }
            return response;
        }
        #endregion
    }
}
