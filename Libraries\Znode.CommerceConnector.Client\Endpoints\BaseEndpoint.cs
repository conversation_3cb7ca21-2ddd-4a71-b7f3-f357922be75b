﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Znode.CommerceConnector.Client.Endpoints
{
    public class BaseEndpoint
    {

        public static IServiceProvider _staticServiceProvider;
        private static IConfiguration _configuration => _staticServiceProvider.GetService<IConfiguration>();

        // Method to assign the service provider from Program.cs or Startup.cs
        public static void Configure(IServiceProvider serviceProvider)
        {
            _staticServiceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        public static string CommerceConnectorApi =>_configuration["appsettings:CommerceConnectorApi"];
        public static string ZnodeApi =>_configuration["appsettings:ZnodeAPI"];
        public static string ZnodeAuthorizationKey =>_configuration["appsettings:ZnodeAuthorizationKey"];
        public static bool EnableTokenAuth => Convert.ToBoolean( _configuration["appsettings:EnableTokenAuth"]);
    }
}
