﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeErpdataExchangeParameter
{
    public int ErpdataExchangeParameterId { get; set; }

    public int ApiconfigurationId { get; set; }

    public string? Key { get; set; }

    public string? Value { get; set; }

    public string? Type { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeApiconfiguration Apiconfiguration { get; set; } = null!;
}
