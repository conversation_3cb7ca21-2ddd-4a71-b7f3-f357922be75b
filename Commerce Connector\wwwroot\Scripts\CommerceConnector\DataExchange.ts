﻿declare var agGrid: any;

class DataExchange {
    private gridInitialized = false;
    private gridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0 // disables AG Grid's internal sorting
        },
        rowData: [],
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,
        onGridReady: (params: any) => {
            this.gridOptions.api = params.api;
            this.gridOptions.columnApi = params.columnApi;

            setTimeout(() => {
                params.api.sizeColumnsToFit();
            }, 100);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const lastSorted = sortState.find((c: any) => c.sort);
                if (!lastSorted || !lastSorted.sort) {
                    const previous = DynamicGrid.prototype.currentSortField;
                    if (previous) {
                        params.columnApi.applyColumnState({
                            state: [{ colId: previous, sort: 'asc' }],
                            defaultState: { sort: null }
                        });
                        return;
                    }
                }

                DynamicGrid.prototype.setSorting(lastSorted?.colId || null, lastSorted?.sort || null);
                this.LoadDataExchangeGrid(1);
            });
        }
    };

    ConvertTimeTo12HourFormat(date: Date, isSecondsRequired: boolean): string {
        let hours = date.getHours();
        let minutes = date.getMinutes();
        const period = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12;
        const paddedHours = (hours < 10 ? '0' : '') + hours.toString();
        const paddedMinutes = (minutes < 10 ? '0' : '') + minutes.toString();
        if (isSecondsRequired) {
            return `${paddedHours}:${paddedMinutes}:00 ${period}`;
        }
        else {
            return `${paddedHours}:${paddedMinutes} ${period}`;
        }
    }
    LoadDataExchangeGrid(page: number) {
        ZnodeGlobal.prototype.ShowLoader();
        var token = $('#hdnAdminToken').val();
        const request = {
            page: page,
            pageSize: Number($('#pageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#globalSearch").val()
        };

        $.ajax({
            url: '/commerce-connector/DataExchange/RenderDataExchangeGrid',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(request),
            headers: {
                'CCAdminToken': String(token || '')
            },
            success: (res) => {
                const rowCount = res?.rows?.length || 0;

                if (rowCount === 0) {
                    if (this.gridInitialized) {
                        this.gridOptions.api.setRowData([]);
                    }

                    this.showNoRecordsMsg("dataExchangeGrid");
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    this.showRecords("dataExchangeGrid");
                }
                DynamicGrid.prototype.setTotalRows(res.totalCount);

                if (!this.gridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#dataExchangeGrid');
                    new agGrid.Grid(gridDiv!, this.gridOptions);
                    this.gridInitialized = true;
                }
                if (!this.gridOptions.columnDefs?.length && res.columns) {
                    const dynamicCols = res.columns.map(c => {
                        const isExchangeName = c.field === "exchangeName";
                        const isStatus = c.field === "status";

                        const fieldName = c.field?.toLowerCase();
                        const column: any = {
                            field: c.field,
                            headerName: c.title,
                            sortable: c.sortable === true,
                            filter: false,
                            flex: 1,
                            maxWidth: isStatus ? 130 : undefined,
                            hide: ["dataExchangeLibraryId", "exchangeId", "format", "lastRunResult"].indexOf(c.field) !== -1,
                            headerTooltip: c.title,
                            cellRenderer: isExchangeName
                                ? function (params: any) {
                                    const id = params.data?.dataExchangeLibraryId;
                                    const name = params.value;
                                    const nameTooltip = name.replace(/ /g, '\u00A0');//Added to replace normal spaces with non-breaking spaces for visual consistency.
                                    return `<a href="#" class="exchange-link" data-id="${id}" title="${nameTooltip}" data-bs-toggle="modal" data-bs-target="#baseDefinitionModal">${name}</a>`;
                                }
                                : fieldName === "status"
                                    ? function (params: any) {
                                        const status = params.value === true || params.value === "true";
                                        return status
                                            ? `<i class="fas fa-check" data-test-selector="iconCheck"></i>`
                                            : `<i class="fas fa-times" data-test-selector="iconTimes"></i>`;
                                    }
                                    : undefined

                        };
                        if (['lastruntime'].indexOf(fieldName) > -1) {
                            column.valueFormatter = (params) => {
                                if (!params.value) return '';
                                const date = new Date(params.value);
                                if (isNaN(date.getTime())) return params.value;
                                const year = date.getFullYear();
                                const month = (date.getMonth() + 1 < 10 ? '0' : '') + (date.getMonth() + 1);
                                const day = (date.getDate() < 10 ? '0' : '') + date.getDate();
                                const timeStr = this.ConvertTimeTo12HourFormat(date, true);

                                return `${month}/${day}/${year} ${timeStr}`;
                            };
                        }
                        if (['status'].indexOf(fieldName) === -1) {
                            if (!(['lastruntime'].indexOf(fieldName) === -1)) {
                                column.tooltipValueGetter = (params) => ZnodeGlobal.prototype.ConvertDateTimeToLocal(params.value);
                            }
                            else {
                                column.tooltipField = fieldName;
                            }
                        }
                        return column;
                    });
                    const actionCol = {
                        headerName: "Actions",
                        width: 182,
                        sortable: false,
                        headerTooltip: "Actions",
                        cellRenderer: function (params) {
                            const id = params.data?.dataExchangeLibraryId;
                            const name = params.data?.exchangeName;
                            const Status = params.data?.Status === true || params.data?.status === true;
                            const toggleIcon = Status ? 'fas fa-ban' : 'fas fa-check-circle';
                            const toggleTitle = Status ? 'Disable' : 'Enable';
                            const isEnabled = !Status
                            return `
                                <a href="#" data-action="edit" data-id="${id}" data-admintoken="${token}" title="Edit"><i class="fa fa-edit"></i></a>
                                <a href="#" data-action="run" data-id="${id}" data-admintoken="${token}" title="Run"><i class="fa fa-play ml-2"></i></a>
                              <a href="#" data-action="toggle" title="${toggleTitle}" data-admintoken="${token}" onclick="DataExchange.prototype.OnEnableDisable('${id}', ${isEnabled},'${token}')">
                <i class="${toggleIcon} ml-2"></i>
            </a>
                                <a href="#" data-action="history" data-id="${id}" data-admintoken="${token}" data-name="${name}" title="History"><i class="fa fa-history ml-2"></i></a>
                                <a href="#" data-action="delete" data-id="${id}" data-admintoken="${token}" title="Delete"><i class="fa fa-trash ml-2"></i></a>
                            `;
                        }
                    };

                    this.gridOptions.api.setColumnDefs([...dynamicCols, actionCol]);
                }

                this.gridOptions.api.setRowData(res.rows);
                setTimeout(() => {
                    this.gridOptions.api.sizeColumnsToFit();
                }, 100);
                // Auto-size columns to fit content
                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, '', (page) => {
                    this.LoadDataExchangeGrid(page);
                });
                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount);
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    showRecords(element) {
        $("#noRecordsMessage").hide();
        $(".show-per-page").show();
        $(".show-page-count").show();
        $("#customRowInfo").show();
        $("#" + element).show();
        $("#paginationControls").show();
    }

    showNoRecordsMsg(element) {
        $("#" + element).hide();
        $("#paginationControls").hide();
        $(".show-per-page").hide();
        $(".show-page-count").hide();
        $("#customRowInfo").hide();
        $("#noRecordsMessage").show();
    }

    OnEdit(id, adminToken) {
        window.location.href = `/commerce-connector/ManageDataExchange/ManageDataExchangedetails/${id}?CCAdminToken=` + adminToken;
    }

    OnRun(id, adminToken) {
        ZnodeGlobal.prototype.ShowLoader();
        $.ajax({
            url: `/commerce-connector/DataExchange/TriggerDataExchangeSchedular?erpId=${id}&CCAdminToken=` + adminToken,
            type: 'POST',
            success: (response) => {
                var element: any = $(".messageBoxContainer");
                $(".messageBoxContainer").removeAttr("style");
                ZnodeGlobal.prototype.ShowNotificationBar(element, response.message, response.status ? 'success' : 'error');
                this.LoadDataExchangeGrid(1);
            },
            error: function (err) {
                console.error('Task Scheduler failed to trigger. Please check logs.', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    OnHistory(id, exchangeName, adminToken) {
        window.location.href = `/commerce-connector/DataExchange/GetLogHistory?erpId=${id}&exchangeName=${exchangeName}&CCAdminToken=` + adminToken;
    }

    OnDelete(id, adminToken) {
        if (confirm("Are you sure you want to delete ?")) {
            ZnodeGlobal.prototype.ShowLoader();
            $.ajax({
                url: `/commerce-connector/DataExchange/DeleteDataExchangeSchedularById?erpId=${id}&CCAdminToken=` + adminToken,
                type: 'POST',
                success: (response) => {
                    var element: any = $(".messageBoxContainer");
                    $(".messageBoxContainer").removeAttr("style");
                    ZnodeGlobal.prototype.ShowNotificationBar(element, response.message, response.status ? 'success' : 'error');
                    this.LoadDataExchangeGrid(1);
                },
                error: () => {
                    alert("Error deleting the record.");
                    ZnodeGlobal.prototype.HideLoader();
                }
            });
        }
    }

    OnEnableDisable(id, IsEnabled, ccAdminToken?: string) {
        window.location.href = `/commerce-connector/DataExchange/EnableDisableDataExchangeScheduler?erpId=${id}&IsEnabled=${IsEnabled}&CCAdminToken=` + ccAdminToken;
    }

    public BrowseExchangeDetails(erpId: number): void {
        const target = "#target-exchange-to-display";
        $(target).hide(); // Reset
        $('#baseDefinitionContent').hide();
        ZnodeGlobal.prototype.DisplayPopup(
            '/commerce-connector/ManageDataExchange/GetBaseDefinitionDetailsByID/' + erpId,
            {},
            'target-exchange-to-display', 'baseDefinitionContent'
        );
    }
}
$(document).ready(function () {

    var successMessage = document.getElementById('triggerMessage');
    if (successMessage) {
        setTimeout(function () {
            successMessage.style.display = 'none';
        }, 3000);
    }
    const instance = new DataExchange();
    $("#dataExchangeGrid").hide();
    $("#noRecordsMessage").hide();
    if ($("#dataExchangeGrid").length > 0) {
        instance.LoadDataExchangeGrid(1);
        $('#pageSizeSelect').on('change', () => {
            this.currentPageSize = + $('#pageSizeSelect').val();
            instance.LoadDataExchangeGrid(1);
        });

        $('#globalSearchbtn').on('click', () => {
            instance.LoadDataExchangeGrid(1)
        });
        $('#globalSearch').keydown(function (e) {
            if (e.keyCode === 13) {
                instance.LoadDataExchangeGrid(1);
            }
        });
    }

    $(document).on('click', '[data-action]', function () {
        const id = $(this).data('id');
        const action = $(this).data('action');
        const exchangeName = $(this).data('name');
        const ccAdminToken = $(this).data('admintoken');
        switch (action) {
            case 'edit': instance.OnEdit(id, ccAdminToken); break;
            case 'run': instance.OnRun(id, ccAdminToken); break;
            case 'history': instance.OnHistory(id, exchangeName, ccAdminToken); break;
            case 'delete': instance.OnDelete(id, ccAdminToken); break;
        }
    });

    $(document).on('click', '.exchange-link', function (e) {
        e.preventDefault();
        const id = $(this).data('id');
        instance.BrowseExchangeDetails(id);
    });
});