﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeErpExchangeConfigurationDataLog
{
    public int ErpExchangeConfigurationDataLogId { get; set; }

    public int Erpid { get; set; }

    public string? FieldName { get; set; }

    public string OldValue { get; set; } = null!;

    public string NewValue { get; set; } = null!;

    public DateTime ChangeDateTime { get; set; }

    public string UserName { get; set; } = null!;

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeBaseDefinitionTable Erp { get; set; } = null!;
}
