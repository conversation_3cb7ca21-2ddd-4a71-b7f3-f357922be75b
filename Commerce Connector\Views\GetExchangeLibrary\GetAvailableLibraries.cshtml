﻿@model IEnumerable<Znode.CommerceConnector.Core.ViewModels.DataExchangeViewModel>
@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();

}
<!-- Page Content -->

<div class="col-md-12 nopadding dashboard-title">
    <div class="title-container d-flex justify-content-between">
        <h1 data-test-selector="hdgDataExchangeLibrary">@CommerceConnector_Resources.DataExchangeLibrary</h1>
        <div class="mt-2">
            <button type="button" id="add-selected-btn" style="display: none;" class="btn-text btn-text-secondary me-2" onclick="AddSelectedExchange.prototype.AddSelectedIdsToTheTable()" data-test-selector="lnkAddSelected" aria-label="Add Data Exchange Library Selected" disabled>@CommerceConnector_Resources.AddSelected</button>
            <a href="/commerce-connector?@CCAdminConstant.CCAdminToken=@token" class="btn-text btn-text-secondary" data-test-selector="lnkCancel" aria-label="Cancel Data Exchange Library">@CommerceConnector_Resources.Cancel</a>
        </div>
    </div>
</div>
<div class="col-md-12 page-container">
    <div class="data-exchange-library page-content">
            <div class="controls">
                <div class="filter-search d-flex">
                    <input type="text" id="globalSearch" maxlength="130" placeholder="Search..." data-test-selector="txtSearch" />
                    <button id="globalSearchbtn" class="btn-search" data-test-selector="btnSearchIcon"><em class="z-search"></em></button>
                </div>
            </div>
        <p class="data-exchange-note mt-3 fst-italic" data-test-selector="paraSingleDataExchange">@CommerceConnector_Resources.SingleDataExchange</p>
        <div id="availableLibrariesGrid" class="ag-theme-alpine mt-3" data-test-selector="divAvailableLibrariesGrid"></div>
        <div class="show-per-page d-flex mt-3" style="display: none;">
            <div id="paginationControls" class="me-3"></div>
            <div id="customRowInfo" class="total-record" data-test-selector="divCustomRowInfo" style="display: none;"></div>
            <div class="show-page-count ps-3" style="display: none;">
                <div class="d-flex align-items-center">
                    <label class="pe-3" data-test-selector="lblShow">@CommerceConnector_Resources.Show</label>
                    <select id="pageSizeSelect" data-test-selector="drpPageSizeSelect">
                        <option value="10" selected>10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
        </div>
        <div id="noRecordsMessage" class="text-center mt-3" style="display: none;">
            @CommerceConnector_Resources.NoRecordsFound
        </div>
        <div class="modal fade" id="availableLibraryModal" tabindex="-1">
            <div id="libraryModalDialog" class="modal-dialog modal-dialog-centered modal-lg">
                <div id="availableLibraryContent" class="modal-content">
                    <div class="modal-header">
                        <h3 class="modal-title" data-test-selector="hdgAvailableLibrary">@CommerceConnector_Resources.DataExchangePopupTitle</h3>
                        <button type="button" id="modalCloseBtn" class="btn-text-icon" aria-label="Go Back to Available Library List" data-bs-dismiss="modal" data-test-selector="btnBack">
                            <em class='z-back'></em>
                            @CommerceConnector_Resources.LabelBack
                        </button>
                    </div>
                    <div class="modal-body" id="target-library-to-display" data-test-selector="divAvailableLibrary">
                        @Html.Partial("_AvailableLibraryPopup")
                    </div>

                    <div class="modal-footer border-top-0 pt-0 d-flex justify-content-between">
                        <p class="fst-italic data-exchange-note" data-test-selector="paraDataExchangePopupNote">Note: @CommerceConnector_Resources.DataExchangePopupNote</p>
                        <button type="button" id="btnSaveExchange" class="btn-text btn-text-secondary pull-right margin-top" onclick="AddSelectedExchange.prototype.SaveDataExchange(event);" data-dismiss='modal'>@CommerceConnector_Resources.Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>