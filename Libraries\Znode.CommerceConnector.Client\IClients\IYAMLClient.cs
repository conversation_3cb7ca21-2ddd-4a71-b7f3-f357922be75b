﻿using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Models;

namespace Znode.CommerceConnector.Client.IClients
{
    public interface IYAMLClient
    {
        public dynamic DownloadSample(MediaConfigurationModel mediaConfiguration);
        dynamic SaveYaml(YAMLSaveRequestModel content);
        dynamic GetSavedYamlData(int erpId, MediaConfigurationModel mediaConfiguration);
        MediaConfigurationModel GetMediaConfiguration();
    }
}
