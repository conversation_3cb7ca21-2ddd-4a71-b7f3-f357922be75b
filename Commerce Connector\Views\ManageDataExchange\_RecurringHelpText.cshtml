﻿@using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="cron-expression">
    <label class="pb-2" data-test-selector="lblCronExpression" aria-label="Cron Expression">@CommerceConnector_Resources.WhatIsCronExpression</label>
    <p data-test-selector="paraCronExpressionData" aria-label="Cron Expression Data">
        A cron expression is a definition of the schedule onto which the recurring jobs will be
        executed. It’s a combination of a string that has 5 fields, separated by white space,
        that represents a schedule. It consists of the following format,
        &lt;minutes&gt; &lt;hours&gt; &lt;days of month&gt; &lt;months&gt; &lt;days of week&gt;,
        use the specified format to enter the cron expression for scheduling a recurring Job.
    </p>
</div>

<div class="commonly-used-cron-expressions">
    <label class="pb-2" data-test-selector="lblCommonlyUsedCronExpressions" aria-label="Commonly Used Cron Expressions">
        @CommerceConnector_Resources.CommonlyUsedCronExpressions
        <span class="z-angle-down ms-1" title="Show/Hide" id="toggleCronTable" data-test-selector="spnAngleDownIcon"></span>
    </label>
    <div class="hide" id="tableCronExpression">
        <div class="row">
            <div class="col-12 col-md-6">
                <div class="row">
                    <div class="col-12 col-md-9">Every minute</div>
                    <div class="col-12 col-md-3">* * * * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every 5th minute</div>
                    <div class="col-12 col-md-3">*/5 * * * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every 15th minute</div>
                    <div class="col-12 col-md-3">*/15 * * * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every hour, on the hour</div>
                    <div class="col-12 col-md-3">0 * * * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every 15 minutes between 1:00 AM and 3:00 AM every day</div>
                    <div class="col-12 col-md-3">*/15 1-3 * * *</div>
                </div>
            </div>
            <div class="col-12 col-md-6">
                <div class="row">
                    <div class="col-12 col-md-9">Every 8 hours</div>
                    <div class="col-12 col-md-3">0 */8 * * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every day at 1:00 AM</div>
                    <div class="col-12 col-md-3">0 1 * * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every Saturday at 2:30 AM</div>
                    <div class="col-12 col-md-3">30 2 * * 6</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every month on the 1st at 12:15 AM</div>
                    <div class="col-12 col-md-3">15 0 1 * *</div>
                </div>
                <div class="row">
                    <div class="col-12 col-md-9">Every year on January 1st at 12:15 AM</div>
                    <div class="col-12 col-md-3">15 0 1 1 *</div>
                </div>
            </div>
        </div>
    </div>
</div>