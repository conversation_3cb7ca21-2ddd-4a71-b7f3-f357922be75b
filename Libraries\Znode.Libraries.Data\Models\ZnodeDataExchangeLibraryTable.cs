﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeDataExchangeLibraryTable
{
    public int ErpexchangeId { get; set; }

    public string? Name { get; set; }

    public string? Format { get; set; }

    public string? Version { get; set; }

    public string? DataSource { get; set; }

    public string? DataDestination { get; set; }

    public string? TriggerOrigin { get; set; }

    public string? Description { get; set; }

    public string? Tags { get; set; }

    public string? Access { get; set; }

    public string? Library { get; set; }

    public string? Collection { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public string? ProcessorFileName { get; set; }

    public virtual ICollection<ZnodeBaseDefinitionTable> ZnodeBaseDefinitionTables { get; set; } = new List<ZnodeBaseDefinitionTable>();
}
