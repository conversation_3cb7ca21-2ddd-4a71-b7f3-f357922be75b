﻿using Microsoft.AspNetCore.Mvc.Rendering;

using System.ComponentModel.DataAnnotations;
using Znode.CommerceConnector.Core.Helper;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class TransmissionConfigurationViewModel
    {
        public int? TransmissionConfigurationId { get; set; }

        [Display(Name = CCAdminConstant.TransmissionMode)]
        [Required(ErrorMessage = CCAdminConstant.TransmissionModeRequired)]
        public string? TransmissionMode { get; set; }

        [Display(Name = CCAdminConstant.TransmissionMode)]
        [Required(ErrorMessage = CCAdminConstant.TransmissionModeRequired)]
        public string? TransmissionModeDestination { get; set; }
        public string? TransmissionRole { get; set; }
        public List<SelectListItem> TransmissionModeOptions { get; set; } = new List<SelectListItem>
        {
             new SelectListItem { Text = "-- Select --", Value = "" },
             new SelectListItem { Text = "API", Value = "APIHandler" },
             new SelectListItem { Text = "SFTP", Value = "SFTPHandler" },
         };
        public int? ErpId { get; set; }

        public SftpTransmissionConfigurationViewMOdel? SftpConfig { get; set; }
        public HttpsTransmissionConfigurationViewModel? HttpsConfig { get; set; }

        public SftpTransmissionConfigurationViewMOdel? SftpConfigDestination { get; set; }
        public HttpsTransmissionConfigurationViewModel? HttpsConfigDestination { get; set; }
    }

}
