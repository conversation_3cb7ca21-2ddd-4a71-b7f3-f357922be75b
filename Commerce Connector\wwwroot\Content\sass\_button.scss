﻿.btn-text{
  @include btn-text;
  font-size:$base-font-size + 2;padding:0 15px;height:34px;line-height:34px;overflow:hidden;float:left;border-radius:2px;font-family:$base-font-family-condensed-bold;text-transform:uppercase;letter-spacing:0.5px;
  background:$base-color-tertiary;color:$base-color-primary;border:1px solid $base-color-primary;
  &:hover{background-color:$base-color-primary;color:$base-color-white;}
}

.btn-text-primary{background-color:$base-color-primary !important;border:none;color:$base-color-white;
    &:hover,&:focus,&:active{color:$base-color-white;background-color:$base-bgcolor-button-hover}
}
.btn-text-secondary{background-color:$base-color-secondary!important;border:none;color:$base-color-white;
    &:hover,&:active,&:focus{color:$base-color-white;background-color:$base-color-button-secondary !important;}
}

.btn-text-icon{background-color:transparent;height:34px;font-family:$base-font-family-condensed;color:$base-color-primary;border:0;font-size:$base-font-size + 2;display:inline-block;line-height:34px;text-transform:uppercase;margin-left:10px;vertical-align:middle;outline:none !important;
    &:hover,&:focus,&:active{color:$base-color-secondary;}
    i, em{padding-right:6px;vertical-align:middle;display:inline-block;margin-top:-2px;}

}

.btn-text-group{position:relative;display:inline-block;
    .btn-text{border-top-right-radius:0;border-bottom-right-radius:0;}
    .btn-text-dropdown{border:0;border-left:1px solid $base-color-white;height:34px;padding:15px 8px;border-top-left-radius:0;border-bottom-left-radius:0;background-color:$base-color-secondary;border-top-right-radius:2px;border-bottom-right-radius:2px;
        &:hover{background:darken($base-color-secondary,10%);}
        .caret{color:$base-color-white;vertical-align:top;}
    }
    .dropdown-menu{margin-top:0 !important;min-width:145px;}
    .dropdown-menu > li > a {padding: 5px 11px !important;}
    .btn-dropdwn-lst{background:transparent;font-family:$base-font-family-condensed; border:0;font-size:14px;vertical-align:middle;outline:none !important;color:$base-color-primary;font-weight:normal;white-space:nowrap;clear:both;}
}
.dropdown-button{display:inline-block;padding-left:10px;line-height:34px;}

button[disabled], html input[disabled]{cursor:not-allowed;background-color:$btn-disabled;
        &:hover{background-color:$btn-disabled-hover;}
}

.btn-narrow-icon{border:1px solid $base-color-secondary;border-radius:3px;display: inline-block !important;height:34px;width:34px;line-height:34px !important;margin-left: 5px;padding: 0 10px;text-align: center;background-color:$base-color-white;outline:none !important;
    &:hover{color:$base-color-secondary;border:1px solid $base-color-secondary;}
    &[class^="z-"], &[class*=" z-"]{line-height:25px;}
}

.GetBannerList .banner-btn-group .btn-text-group .dropdown-menu {
    min-width: 116px;
}