﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.CommerceConnector.Core.ViewModels
 @using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var isFromAddNew = ViewBag.isFromAddNew ?? false;
}
<input id="erpID" value=@Model.ERPBaseDefinitionId type="hidden"/>
<input id="Name" value='@Model.Name' type="hidden" />
<div class=" dashboard-panel-heading">
    <h3 data-test-selector="hdgBaseDefinition" aria-label="Base Definition">@CommerceConnector_Resources.BaseDefinition</h3>
</div>
@if (isFromAddNew)
{
    @Html.Partial("_BaseDefinitionDetails", Model)
}
else
{
    @Html.Partial("_ManageConfigurationDetails", Model)
}

