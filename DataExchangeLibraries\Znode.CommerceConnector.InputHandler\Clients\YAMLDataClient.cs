﻿using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Hosting;
using System.Diagnostics;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class YAMLDataClient : IYAMLDataClient
    {
        private readonly IWebHostEnvironment _env;
        private readonly string _blobFolderPath;

        public YAMLDataClient(IWebHostEnvironment env)
        {
            _env = env;
            _blobFolderPath = HelperMethods.YAMlFilepath ?? "Data/CCYAMLFiles";
        }

        public dynamic GetYAMLData(string fileName, MediaConfigurationModel mediaConfiguration)
        {
            string blobPath = $"{_blobFolderPath}/{fileName}";
            try
            {
                MongoLogging.LogMessage(Constants.ReadingYamlFile + blobPath, Constants.GetYAMLData, TraceLevel.Info);
                BlobContainerClient containerClient = HelperUtility.GetBlobContainerClient(mediaConfiguration);
                BlobClient blobClient = containerClient.GetBlobClient(blobPath);
                if (blobClient.Exists())
                {
                    var downloadResponse = blobClient.DownloadContent();
                    return downloadResponse.Value.Content.ToArray();
                }
                else
                {
                    MongoLogging.LogMessage(Constants.BlobFileNotFound + blobPath, Constants.GetYAMLData, TraceLevel.Warning);
                    return new byte[0];
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.ReadingYamlFileFromBlobFailed + blobPath + " | Exception: " + ex.Message, Constants.GetYAMLData, TraceLevel.Error);
                return new byte[0];
            }
        }
    }
}
