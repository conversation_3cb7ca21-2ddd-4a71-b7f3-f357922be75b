﻿using System.Collections.Specialized;
using System.Web;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Helpers
{
    public class QueryStringParser
    {
        private readonly string _queryString;
        private readonly string znodeApiUriItemSeparator = ",";
        private readonly string znodeApiUriKeyValueSeparator = "~";
        private readonly string znodeCommaReplacer = "^";

        public FilterCollection Filters
        {
            get { return GetTuples("filter"); }
        }

        public NameValueCollection Sorts
        {
            get { return GetKeyValuePairs("sort"); }
        }

        public NameValueCollection Page
        {
            get { return GetKeyValuePairs("page"); }
        }

        public QueryStringParser(string queryString)
        {
            _queryString = queryString;
        }

        private NameValueCollection GetKeyValuePairs(string param, bool keepExactCasing = false)
        {
            var keyValuePairs = new NameValueCollection();
            var query = HttpUtility.ParseQueryString(keepExactCasing ? _queryString : _queryString.ToLower());

            foreach (var key in query.AllKeys)
            {
                if (key.ToLower() == param)
                {
                    var value = query.Get(key);
                    var items = value.Split(znodeApiUriItemSeparator.ToCharArray());

                    foreach (var item in items)
                    {
                        if (item.Contains(znodeApiUriKeyValueSeparator))
                        {
                            var set = item.Split(znodeApiUriKeyValueSeparator.ToCharArray());
                            if (keepExactCasing)
                                keyValuePairs.Add(set[0], HttpUtility.HtmlDecode(set[1]).ToLower());
                            else
                                keyValuePairs.Add(set[0].ToLower(), HttpUtility.HtmlDecode(set[1]));
                        }
                        else
                        {
                            // Just make the value the same as the key, for consistency of code in other places
                            if (keepExactCasing)
                                keyValuePairs.Add(item, item.ToLower());
                            else
                                keyValuePairs.Add(item.ToLower(), item.ToLower());
                        }
                    }

                    break;
                }
            }

            return keyValuePairs;
        }

        private FilterCollection GetTuples(string param, bool keepExactCasing = false)
        {
            var filters = new FilterCollection();
            var query = HttpUtility.ParseQueryString(keepExactCasing ? _queryString : _queryString.ToLower());

            foreach (var key in query.AllKeys)
            {
                if (key.ToLower() == param)
                {
                    var value = query.Get(key);
                    var items = value.Split(znodeApiUriItemSeparator.ToCharArray());

                    foreach (var item in items)
                    {
                        if (item.Contains(znodeApiUriKeyValueSeparator))
                        {
                            var tuple = item.Split(znodeApiUriKeyValueSeparator.ToCharArray());
                            var filterKey = keepExactCasing ? tuple[0].Trim() : tuple[0].ToLower().Trim();
                            var filterOperator = keepExactCasing ? tuple[1].Trim() : tuple[1].ToLower().Trim();
                            var filterValue = keepExactCasing ? tuple[2].Trim() : tuple[2].ToLower().Trim();

                            filters.Add(new FilterTuple(filterKey, filterOperator, HttpUtility.HtmlDecode(filterValue?.Replace(znodeCommaReplacer, ","))));
                        }
                    }

                    break;
                }
            }

            return filters;
        }
    }
}