﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Diagnostics;
using System.Net;
using System.Text.RegularExpressions;
using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.InputHandler.HandlerHelper;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class APIClient : IAPIClient
    {
        public APIClient(IServiceProvider serviceProvider)
        {
            BaseEndpoint._staticServiceProvider = serviceProvider;
        }
        public dynamic GetData<T>(int erpId, int logId, HTTPConfigurationModel httpConfigurationModel, dynamic requestBody, WebHeaderCollection headerCollection, dynamic inputModel , bool returnWithoutDeserialize = false)
        {
            try
            {
                MongoLogging.LogMessage(Constants.DownloadDataCalledCalledInAPIInput<PERSON><PERSON><PERSON>, Constants.DownloadData, TraceLevel.Info);
                if (HelperUtility.IsNotNull(inputModel))
                {
                    httpConfigurationModel = CheckParams(erpId, logId,httpConfigurationModel, inputModel);
                }
                string endpoint = httpConfigurationModel.Endpoint.Trim();

                try
                {
                    ApiClient apiClient = new ApiClient();
                    RequestModel requestModel = apiClient.SetRequestModel(endpoint);
                    if (HelperUtility.IsNotNull(headerCollection))
                    {
                        var headerParameter = HelperMethods.GetHeaderParameter(httpConfigurationModel.APIConfigurationId, Constants.Source);
                        foreach (var item in headerParameter)
                        {
                            if (!headerCollection.AllKeys.Contains(item.Key))
                            {
                                headerCollection.Add(item.Key, item.Value);
                            }
                        }
                        requestModel.HeaderCollection = headerCollection;
                    }
                    if (requestBody is not NullableRequestBody)
                    {
                        requestModel.RequestBody = JsonConvert.SerializeObject(requestBody,Formatting.Indented);
                    }
                    dynamic apiResponse = "";
                    if (httpConfigurationModel.APIAction == Constants.GET)
                    {
                        apiResponse = apiClient.GetRequest<dynamic>(requestModel, out string statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.PUT)
                    {
                        apiResponse = apiClient.PutRequest<dynamic>(requestModel, out string statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.POST)
                    {
                        apiResponse = apiClient.PostRequest<dynamic>(requestModel, out string statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.DELETE)
                    {
                        apiResponse = apiClient.DeleteRequest<dynamic>(requestModel, out string statusCode);
                    }
                    else if (httpConfigurationModel.APIAction == Constants.PATCH)
                    {
                        apiResponse = apiClient.PatchRequest<dynamic>(requestModel, out string statusCode);
                    }
                    dynamic result = JsonConvert.SerializeObject(apiResponse);
                    return result;
                }
                catch (Exception ex)
                {
                    MongoLogging.LogMessage(Constants.DownloadDataMethodFailedInAPIInputHandler + ex.Message, Constants.DownloadData, TraceLevel.Error);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.APIConnectionFailed + ex.Message);
                    if(!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.DownloadData, TraceLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.DownloadDataMethodFailedInAPIInputHandler + ex.Message, Constants.DownloadData, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.DownloadDataMethodFailedInAPIInputHandler + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.DownloadData, TraceLevel.Error);
                return false;
            }
        }

        public dynamic CheckParams(int erpId, int logId, HTTPConfigurationModel httpConfigurationModel, dynamic inputModel)
        {
            try
            {
                MongoLogging.LogMessage(Constants.CheckParamsMethodCalled, Constants.CheckParamsMethod, TraceLevel.Error);
                string url = httpConfigurationModel.Endpoint;
                JObject firstItem = null;
                JToken parsedInput = JToken.Parse(inputModel.ToString());

                if (parsedInput.Type == JTokenType.Array)
                {
                    firstItem = (JObject)parsedInput.First;
                }
                else if (parsedInput.Type == JTokenType.Object)
                {
                    firstItem = (JObject)parsedInput;
                }

                var matches = Regex.Matches(url, @"\{(.*?)\}");
                foreach (Match match in matches)
                {
                    string key = match.Groups[1].Value;
                    if (firstItem.ContainsKey(key))
                    {
                        string value = Uri.EscapeDataString(firstItem[key].ToString());
                        url = url.Replace(match.Value, value);
                    }
                }
                httpConfigurationModel.Endpoint = url;
                return httpConfigurationModel;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.CheckParamsMethodFailed + ex.Message, Constants.CheckParamsMethod, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.CheckParamsMethodFailed + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.CheckParamsMethod, TraceLevel.Error);
                return httpConfigurationModel;
            }
        }


        public static Dictionary<string, object> ExtractKeyValuePairs(JToken token, string parentKey = "")
        {
            var result = new Dictionary<string, object>();

            if (token.Type == JTokenType.Object)
            {
                foreach (var property in token.Children<JProperty>())
                {
                    string key = string.IsNullOrEmpty(parentKey) ? property.Name : $"{parentKey}.{property.Name}";
                    var childPairs = ExtractKeyValuePairs(property.Value, key);
                    foreach (var childPair in childPairs)
                    {
                        result[childPair.Key] = childPair.Value;
                    }
                }
            }
            else if (token.Type == JTokenType.Array)
            {
                int index = 0;
                foreach (var arrayItem in token.Children())
                {
                    string key = $"{parentKey}[{index}]";
                    result[key] = ExtractKeyValuePairs(arrayItem);
                    index++;
                }
            }
            else
            {
                result[parentKey] = token.ToString();
            }

            return result;
        }

    }
}

