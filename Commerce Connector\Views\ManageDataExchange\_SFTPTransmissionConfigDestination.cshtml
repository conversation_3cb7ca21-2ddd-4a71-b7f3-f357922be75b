﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="mt-3">
    <!-- Hidden ERP ID -->
    @Html.HiddenFor(model => model.ERPBaseDefinitionId)
    @Html.HiddenFor(Model => Model.TransmissionConfigurations.TransmissionConfigurationId)
    <!-- Server Address -->
    <div class="row">
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.ServerAddress" class="form-label" data-test-selector="lblServerAddress" aria-label="Server Address"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfigDestination.ServerAddress" id="DestServerAddress" class="form-control" placeholder="SFTP Server Address" data-test-selector="txtServerAddress" aria-label="Destination Server Address" />
            <span id="DestServerAddressError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Folder Path -->
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.FolderPath" class="form-label" data-test-selector="lblFolderPath" aria-label="Folder Path"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfigDestination.FolderPath" class="form-control" id="DestFolderPath" placeholder="Directory path on the server" data-test-selector="txtFolderPath" aria-label="Destination Folder Path" />
            <span id="DestFolderPathError" class="text-danger field-validation-error"> </span>

        </div>

        <!-- Filename -->
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.Filename" class="form-label" data-test-selector="lblFilename" aria-label="File Name"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfigDestination.Filename" class="form-control" id="DestFilename" placeholder="File Name to retrieve" data-test-selector="txtFilename" aria-label="Destination File Name" />
            <span id="DestFilenameError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Port Number -->
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.PortNumber" class="form-label" data-test-selector="lblPortNumber" aria-label="Port Number"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.SftpConfigDestination.PortNumber" type="text" inputmode="numeric" class="form-control" id="DestPortNumber" data-test-selector="txtPortNumber" aria-label="Destination Port" maxlength="4" />
            <span id="DestPortNumberError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Username -->
        <div class="col-md-12 col-lg-6 mb-3 position-relative">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.Username" class="form-label" data-test-selector="lblUsername" aria-label="Username"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <div class="input-group">
                <input asp-for="TransmissionConfigurations.SftpConfigDestination.Username" type="text" id="DestUsername" class="form-control" data-test-selector="txtUsername" aria-label="Destination Username" />
            </div>
            <span id="DestUsernameError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Password -->
        <div class="col-md-12 col-lg-6 mb-3 position-relative">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.Password" class="form-label" data-test-selector="lblPassword" aria-label="Password"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <div class="input-group">
                <input class="form-control"
                       type="password"
                       name="TransmissionConfigurations.SftpConfigDestination.Password"
                       id="DestPassword"
                       value="@Model.TransmissionConfigurations?.SftpConfigDestination?.Password" data-test-selector="txtPassword" aria-label="Destination Password" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnSFTPConfigDestination" aria-label="Toggle Password Visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
            <span id="DestPasswordError" class="text-danger field-validation-error"> </span>
        </div>

        <!-- Action After Retrieval -->
        <!-- Added d-none for future use and set a default to keep validation working for the required field. -->
        <div class="col-md-12 col-lg-6 mb-3 d-none">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.ActionAfterRetrieval" class="form-label" data-test-selector="lblActionAfterRetrieval" aria-label="Action After Retrieval"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.SftpConfigDestination.ActionAfterRetrieval" class="form-select" id="DestActionAfterRetrieval" data-test-selector="drpActionAfterRetrieval" aria-label="Destination Action After File Retrieval">
                @* <option value="">@CommerceConnector_Resources.SelectAction</option> *@
                <option value="Delete">@CommerceConnector_Resources.DeleteAction</option>
                <option value="Archive">@CommerceConnector_Resources.Archive</option>
            </select>
            <span id="DestActionAfterRetrievalError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.SftpConfigDestination.Format" class="form-label" data-test-selector="lblDestFileFormat" aria-label="Destination Format"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.SftpConfigDestination.Format" class="form-select" id="SFTPDestDropdownFileFormat" data-test-selector="drpFileFormat" aria-label="Destination File Format">
                <option value="">@CommerceConnector_Resources.SelectSourceFileFormat</option>
                <option value="CSV">@CommerceConnector_Resources.CSV</option>
                <option value="JSON">@CommerceConnector_Resources.JSON</option>
                <option value="XML">@CommerceConnector_Resources.XML</option>
            </select>
            <span id="SFTPDestFileFormatError" class="text-danger field-validation-error"> </span>
        </div>
    </div>
</div>