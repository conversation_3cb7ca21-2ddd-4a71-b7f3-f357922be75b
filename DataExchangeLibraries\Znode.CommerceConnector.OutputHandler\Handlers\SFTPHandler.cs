﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler.IHandlers;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Models;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.OutputHandler
{
    public class SFTPHandler : ISFTPHandler
    {
        ISFTPClient _sFTPClient;

        public SFTPHandler(ISFTPClient sFTPClient)
        {
            _sFTPClient = sFTPClient;
        }

        public dynamic SFTPOutputDataHandler(string data, int erpId, ProcessorDetails processorDetails)
        {
            SFTPConfigurationModel sFTPConfigurationModel = new SFTPConfigurationModel();
            string schedulerType = GetSchedulerType(erpId);
            if (schedulerType == "RealTime")
            {
                sFTPConfigurationModel = GetInputHandlerSFTPCredentials(erpId);
            }
            else
            {
                sFTPConfigurationModel = GetSFTPCredentials(erpId);
            }
            bool result = _sFTPClient.UploadData<bool>(sFTPConfigurationModel, data, false);
            bool recordsUpdated = HelperMethods.InsertUpdateProcessorLogs(Convert.ToInt32(processorDetails.ErpId), processorDetails.LogId, data, result);
            if (!recordsUpdated)
                MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.SFTPOutputDataHandler, TraceLevel.Error);
            return result;
        }

        public SFTPConfigurationModel GetSFTPCredentials(int erpId)
        {
            MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerCalled, Constants.SFTPOutputDataHandler, TraceLevel.Info);
            List<SFTPConfigurationModel> sftpCredentials = new List<SFTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Destination));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
            List<SFTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<SFTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }


        public SFTPConfigurationModel GetInputHandlerSFTPCredentials(int erpId)
        {
            try
            {
                MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerCalled, Constants.SFTPOutputDataHandler, TraceLevel.Info);
                List<SFTPConfigurationModel> sftpCredentials = new List<SFTPConfigurationModel>();
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
                List<SFTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<SFTPConfigurationModel>(response);
                MongoLogging.LogMessage("GetSFTPCredentials : " + dataModelList?.FirstOrDefault(), Constants.SFTPOutputDataHandler, TraceLevel.Info);
                return dataModelList?.FirstOrDefault();
            }
            catch
            {
                MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerCalled, Constants.SFTPOutputDataHandler, TraceLevel.Info);
                return new SFTPConfigurationModel();
            }
        }

        public string GetSchedulerType(int erpId)
        {
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            DataTable schedulerTypeTable = new DataTable();
            schedulerTypeTable = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeErpbaseDefinitionScheduler> schedulerType = znodeViewRepository.ConvertDataTableToList<ZnodeErpbaseDefinitionScheduler>(schedulerTypeTable);
            return schedulerType?.FirstOrDefault()?.SchedulerType;
        }
    }
}
