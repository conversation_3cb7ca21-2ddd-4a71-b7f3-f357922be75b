﻿using Microsoft.Data.SqlClient;

using System.Data;

using Znode.Libraries.Data.Helpers;

namespace Znode.Libraries.Data
{
    public class ZnodeViewRepository
    {
        /// <summary>
        /// Execute provided stored procedure and SqlParameters
        /// </summary>
        /// <param name="procedureName"></param>
        /// <param name="sqlParameters"></param>
        /// <returns></returns>
        public DataTable ExecuteStoredProcedure(string procedureName, List<SqlParameter> sqlParameters)
        {
            SqlConnection conn = GetSqlConnection();
            try
            {
                SqlCommand cmd = new SqlCommand(procedureName, conn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 1800
                };


                foreach (SqlParameter sqlParameter in sqlParameters)
                {
                    cmd.Parameters.AddWithValue(sqlParameter.ParameterName, sqlParameter.Value);
                }
                if (conn.State.Equals(ConnectionState.Closed))
                {
                    conn.Open();
                }

                SqlDataReader sqlData = cmd.ExecuteReader();
                DataTable outputTable = new DataTable();
                outputTable.Load(sqlData);

                //ZnodeLogging.LogMessage("SP execution done", Convert.ToString(ZnodeLogging.Components.ERP), TraceLevel.Info, null, "EpicorDataHelper.ExecuteStoredProcedure", ZnodeEpicorConstant.EpicorErp);
                return outputTable;
            }
            catch (Exception ex)
            {
                //ZnodeLogging.LogMessage(ex.Message, Convert.ToString(ZnodeLogging.Components.ERP), TraceLevel.Error, null, "EpicorDataHelper.ExecuteStoredProcedure", ZnodeEpicorConstant.EpicorErp);
                return null;
            }
            finally
            {
                if (conn.State.Equals(ConnectionState.Open))
                {
                    conn.Close();
                }
            }
        }

        // Execute stored procedure with out parameter.
        public DataTable ExecuteStoredProcedure(string procedureName, List<SqlParameter> sqlParameters, out Dictionary<string, object> outputValues)
        {
            SqlConnection conn = GetSqlConnection();
            try
            {
                SqlCommand cmd = new SqlCommand(procedureName, conn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 1800
                };

                foreach (SqlParameter sqlParameter in sqlParameters)
                {
                    SqlParameter newParam = new SqlParameter
                    {
                        ParameterName = sqlParameter.ParameterName,
                        SqlDbType = sqlParameter.SqlDbType,
                        Direction = sqlParameter.Direction,
                    };

                    if (sqlParameter.Direction == ParameterDirection.Input || sqlParameter.Direction == ParameterDirection.InputOutput)
                    {
                        newParam.Value = sqlParameter.Value ?? DBNull.Value;
                    }

                    cmd.Parameters.Add(newParam);
                }
                if (conn.State.Equals(ConnectionState.Closed))
                {
                    conn.Open();
                }

                DataTable outputTable = new DataTable();
                using (SqlDataReader reader = cmd.ExecuteReader())
                {
                    outputTable.Load(reader);
                }

                // Extract output parameter values
                outputValues = new Dictionary<string, object>();
                foreach (SqlParameter param in cmd.Parameters)
                {
                    if (param.Direction == ParameterDirection.Output || param.Direction == ParameterDirection.InputOutput)
                    {
                        outputValues[param.ParameterName] = param.Value;
                    }
                }

                //ZnodeLogging.LogMessage("SP execution done", Convert.ToString(ZnodeLogging.Components.ERP), TraceLevel.Info, null, "EpicorDataHelper.ExecuteStoredProcedure", ZnodeEpicorConstant.EpicorErp);
                return outputTable;
            }
            catch (Exception ex)
            {
                //ZnodeLogging.LogMessage(ex.Message, Convert.ToString(ZnodeLogging.Components.ERP), TraceLevel.Error, null, "EpicorDataHelper.ExecuteStoredProcedure", ZnodeEpicorConstant.EpicorErp);
                outputValues = null;
                return null;
            }
            finally
            {
                if (conn.State.Equals(ConnectionState.Open))
                {
                    conn.Close();
                }
            }
        }

        public DataTable ExecuteStoredProcedureList(string procedureName, List<SqlParameter> sqlParameters, int? indexOutParameter, out int totalRowCount)
        {
            totalRowCount = 0; 

            using (SqlConnection conn = GetSqlConnection())
            {
                using (SqlCommand cmd = new SqlCommand(procedureName, conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 1800;

                    if (sqlParameters != null)
                    {
                        foreach (var param in sqlParameters)
                        {
                            cmd.Parameters.Add(param);
                        }
                    }

                    if (conn.State == ConnectionState.Closed)
                        conn.Open();

                    using (SqlDataReader reader = cmd.ExecuteReader())
                    {
                        DataTable result = new DataTable();
                        result.Load(reader);

                        if (indexOutParameter.HasValue && indexOutParameter.Value < cmd.Parameters.Count)
                        {
                            var outParam = cmd.Parameters[indexOutParameter.Value];
                            if (outParam.Direction == ParameterDirection.Output || outParam.Direction == ParameterDirection.InputOutput)
                            {
                                totalRowCount = Convert.ToInt32(outParam.Value);
                            }
                        }
                        return result;
                    }
                }
            }
        }


        public DataSet ExecuteStoredProcedureWithMultipleResults(string procedureName, List<SqlParameter> sqlParameters)
        {
            using (SqlConnection conn = GetSqlConnection())
            using (SqlCommand cmd = new SqlCommand(procedureName, conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 1800;

                foreach (SqlParameter sqlParameter in sqlParameters)
                {
                    cmd.Parameters.AddWithValue(sqlParameter.ParameterName, sqlParameter.Value ?? DBNull.Value);
                }

                using (SqlDataAdapter adapter = new SqlDataAdapter(cmd))
                {
                    DataSet dataSet = new DataSet();
                    adapter.Fill(dataSet); 
                    return dataSet;
                }
            }
        }


        public List<T> ConvertDataTableToList<T>(DataTable dataTable) where T : new()
        {

            var list = new List<T>();

            if (dataTable != null)
            {
                foreach (DataRow row in dataTable.Rows)
                {
                    var obj = new T();
                    foreach (var prop in typeof(T).GetProperties())
                    {
                        if (dataTable.Columns.Contains(prop.Name))
                        {
                            var value = row[prop.Name];
                            if (value != DBNull.Value)
                            {
                                prop.SetValue(obj, value);
                            }
                        }
                    }
                    list.Add(obj);
                }
                return list;
            }
            else
            {
                return list;
            }
        }

        /// <summary>
        /// This method will provide SQL connection string from Znode_Entities key
        /// </summary>
        /// <returns></returns>
        private SqlConnection GetSqlConnection()
        {
            return new SqlConnection(HelperMethods.ConnectionString);
        }
    }
}
