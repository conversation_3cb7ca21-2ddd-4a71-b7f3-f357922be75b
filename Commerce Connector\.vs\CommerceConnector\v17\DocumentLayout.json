{"Version": 1, "WorkspaceRootPath": "D:\\7. Commerce connector doc\\TempSolution\\Commerce Connector\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{27179954-098E-43E5-92EF-6647BD12BDAA}|..\\Libraries\\Znode.CommerceConnector.API\\Znode.CommerceConnector.API.csproj|d:\\7. commerce connector doc\\tempsolution\\libraries\\znode.commerceconnector.api\\controllers\\dataexchangelibrarycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}|..\\Libraries\\Znode.CommerceConnector.Client\\Znode.CommerceConnector.Client.csproj|d:\\7. commerce connector doc\\tempsolution\\libraries\\znode.commerceconnector.client\\endpoints\\exchangelibraryendpoint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27179954-098E-43E5-92EF-6647BD12BDAA}|..\\Libraries\\Znode.CommerceConnector.API\\Znode.CommerceConnector.API.csproj|d:\\7. commerce connector doc\\tempsolution\\libraries\\znode.commerceconnector.api\\services\\iservice\\idataexchangelibraryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{27179954-098E-43E5-92EF-6647BD12BDAA}|..\\Libraries\\Znode.CommerceConnector.API\\Znode.CommerceConnector.API.csproj|d:\\7. commerce connector doc\\tempsolution\\libraries\\znode.commerceconnector.api\\services\\service\\dataexchangelibraryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EE199DAA-CA4A-4654-8AC6-D8F3697C642F}|..\\Libraries\\Znode.CommerceConnector.Client\\Znode.CommerceConnector.Client.csproj|d:\\7. commerce connector doc\\tempsolution\\libraries\\znode.commerceconnector.client\\clients\\exchangelibraryclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 13, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{13b12e3e-c1b4-4539-9371-4fe9a0d523fc}"}, {"$type": "Bookmark", "Name": "ST:129:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:131:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:132:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:133:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:134:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:135:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:136:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:137:0:{1fc202d4-d401-403c-9834-5b218574bb67}"}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "ExchangeLibraryEndpoint.cs", "DocumentMoniker": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.Client\\Endpoints\\ExchangeLibraryEndpoint.cs", "RelativeDocumentMoniker": "..\\Libraries\\Znode.CommerceConnector.Client\\Endpoints\\ExchangeLibraryEndpoint.cs", "ToolTip": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.Client\\Endpoints\\ExchangeLibraryEndpoint.cs", "RelativeToolTip": "..\\Libraries\\Znode.CommerceConnector.Client\\Endpoints\\ExchangeLibraryEndpoint.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAcAAAABAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T09:53:34.017Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "IDataExchangeLibraryService.cs", "DocumentMoniker": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.API\\Services\\IService\\IDataExchangeLibraryService.cs", "RelativeDocumentMoniker": "..\\Libraries\\Znode.CommerceConnector.API\\Services\\IService\\IDataExchangeLibraryService.cs", "ToolTip": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.API\\Services\\IService\\IDataExchangeLibraryService.cs", "RelativeToolTip": "..\\Libraries\\Znode.CommerceConnector.API\\Services\\IService\\IDataExchangeLibraryService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAoAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T09:51:57.269Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "DataExchangeLibraryService.cs", "DocumentMoniker": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.API\\Services\\Service\\DataExchangeLibraryService.cs", "RelativeDocumentMoniker": "..\\Libraries\\Znode.CommerceConnector.API\\Services\\Service\\DataExchangeLibraryService.cs", "ToolTip": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.API\\Services\\Service\\DataExchangeLibraryService.cs", "RelativeToolTip": "..\\Libraries\\Znode.CommerceConnector.API\\Services\\Service\\DataExchangeLibraryService.cs", "ViewState": "AQIAAAMAAAAAAAAAAAAuwAsAAAAFAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T09:51:13.352Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DataExchangeLibraryController.cs", "DocumentMoniker": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.API\\Controllers\\DataExchangeLibraryController.cs", "RelativeDocumentMoniker": "..\\Libraries\\Znode.CommerceConnector.API\\Controllers\\DataExchangeLibraryController.cs", "ToolTip": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.API\\Controllers\\DataExchangeLibraryController.cs", "RelativeToolTip": "..\\Libraries\\Znode.CommerceConnector.API\\Controllers\\DataExchangeLibraryController.cs", "ViewState": "AQIAAAYAAAAAAAAAAADgvxIAAABRAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-05T09:50:02.238Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ExchangeLibraryClient.cs", "DocumentMoniker": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.Client\\Clients\\ExchangeLibraryClient.cs", "RelativeDocumentMoniker": "..\\Libraries\\Znode.CommerceConnector.Client\\Clients\\ExchangeLibraryClient.cs", "ToolTip": "D:\\7. Commerce connector doc\\TempSolution\\Libraries\\Znode.CommerceConnector.Client\\Clients\\ExchangeLibraryClient.cs", "RelativeToolTip": "..\\Libraries\\Znode.CommerceConnector.Client\\Clients\\ExchangeLibraryClient.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAcAAAA3AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-05-03T09:48:42.892Z"}]}]}]}