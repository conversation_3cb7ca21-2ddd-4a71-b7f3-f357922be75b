﻿using System.Net;

using Znode.CommerceConnector.Model;

namespace Znode.CommerceConnector.OutputHandler
{
    public interface IOutputHandlerInitializer
    {
        dynamic OutputHandler(string data, int erpId, dynamic requestBody, WebHeaderCollection headers, ProcessorDetails processorDetails, dynamic inputModel);

        HTTPConfigurationModel GetOutputHandlerAPICredentials(int erpId);
    }
}
