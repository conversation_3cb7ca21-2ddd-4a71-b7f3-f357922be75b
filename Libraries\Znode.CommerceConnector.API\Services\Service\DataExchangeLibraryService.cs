﻿using Azure.Storage.Blobs;
using Microsoft.Data.SqlClient;
using System.Collections.Specialized;
using System.Data;
using System.Diagnostics;
using System.Text;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.API.IHelpers;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;


namespace Znode.CommerceConnector.API.Services
{
    public class DataExchangeLibraryService : IDataExchangeLibraryService
    {
        private readonly IAPIDataHelper _apiDataHelper;
        private readonly IHangfireHelper _hangfireHelper;
        static string path = Directory.GetCurrentDirectory();
        private string _uploadPath = Path.Combine(path, HelperMethods.YAMlFilepath);
        private readonly string _blobFolderPath;

        public DataExchangeLibraryService(IAPIDataHelper apiDataHelper, IHangfireHelper hangfireHelper)
        {
            _apiDataHelper = apiDataHelper;
            _hangfireHelper = hangfireHelper;
            _blobFolderPath = HelperMethods.YAMlFilepath ?? "Data/CCYAMLFiles";
        }

        /// <summary>
        /// Inserts data into ZnodeBaseDefinitionTable based on echange Id.
        /// </summary>
        /// <param name="selectedIds"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool AddSelectedExchange(AddSelectedExchangeModel addSelectedExchangeModel)
        {
            if (addSelectedExchangeModel.SelectedIds?.Count == 0)
            {
                throw new Exception(CommerceConnectorConstants.ErrorIdLessThanZero);

            }
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@DataExchangeLibraryIds", String.Join(",", addSelectedExchangeModel.SelectedIds)));
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionName", addSelectedExchangeModel.SelectedExchangeName));
            sqlParameters.Add(new SqlParameter("@UserId", 0));
            DataTable response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_InsertSelectedExchangeData", sqlParameters);
            if(response != null && response.Rows.Count > 0 && Convert.ToBoolean(response.Rows[0]["Status"]))
            {
                MediaConfigurationModel mediaConfiguration = addSelectedExchangeModel?.MediaConfiguration;
                string baseDefinitionYamlFileName =Convert.ToString( response?.Rows[0]["BaseDefinitionYAMLFileName"])?? string.Empty;
                string defaultExchangeFileName = Convert.ToString(response?.Rows[0]["ExchangeLibraryYAMLFileName"]) ?? string.Empty;

                string baseDefinitionBlobPath = $"{_blobFolderPath}/{baseDefinitionYamlFileName}";
                string dataExchangeBlobPath = $"{_blobFolderPath}/{defaultExchangeFileName}";

                // Get container and blob clients
                BlobContainerClient containerClient = HelperUtility.GetBlobContainerClient(mediaConfiguration);
                BlobClient baseDefinitionBlobClient = containerClient.GetBlobClient(baseDefinitionBlobPath);
                BlobClient dataExchangeBlobClient = containerClient.GetBlobClient(dataExchangeBlobPath);

                try
                {
                    if (baseDefinitionBlobClient.Exists().Value)
                    {
                        baseDefinitionBlobClient.Delete();
                    }

                    // Copy content from data exchange to base definition
                    if (!string.IsNullOrEmpty(defaultExchangeFileName) && dataExchangeBlobClient.Exists().Value)
                    {
                        var downloadResult = dataExchangeBlobClient.DownloadContent();
                        string fileContent = downloadResult.Value.Content.ToString();

                        using (var stream = new MemoryStream(Encoding.UTF8.GetBytes(fileContent)))
                        {
                            baseDefinitionBlobClient.Upload(stream, overwrite: true);
                        }
                    }
                    return true;
                }
                catch (Exception ex)
                {
                    MongoLogging.LogMessage($"Error in AddSelectedExchange blob handling: {ex.Message}", CommerceConnectorConstants.YAMLService, TraceLevel.Error);
                    return false;
                }
            }
            return false;
        }

        public bool ValidateExchangeName(string exchangeName)
        {
            int Status = 0;
            if (string.IsNullOrWhiteSpace(exchangeName))
            {
                throw new Exception(CommerceConnectorConstants.ErrorIdLessThanZero);

            }
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@DataExchangeLibraryName ", Value = exchangeName },
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_ValidateDataERPBaseDefinitionName", sqlParameters, 1, out Status);

            return Convert.ToBoolean(Status);
        }

        /// <summary>
        /// To get the data exchange library list.
        /// </summary>
        /// <param name="filters">filters for global search</param>
        /// <param name="sorts">sorts for sorting</param>
        /// <param name="page">page for pagination</param>
        /// <returns>DataExchangeListModel</returns>
        public DataExchangeListModel GetDataExchangeLibraryList(FilterCollection filters, NameValueCollection sorts, NameValueCollection page)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            var whereClause = DynamicClauseHelper.GenerateDynamicWhereClauseForSP(filters.ToFilterDataCollection());
            var orderBy = DynamicClauseHelper.GenerateDynamicOrderByClause(sorts);
            DynamicClauseHelper.SetPaging(page, out int pagingStart, out int pagingLength);
            sqlParameters.Add(new SqlParameter("@WhereClause", whereClause));
            sqlParameters.Add(new SqlParameter("@Rows", pagingLength));
            sqlParameters.Add(new SqlParameter("@PageNo", pagingStart));
            sqlParameters.Add(new SqlParameter("@Order_BY", orderBy));
            sqlParameters.Add(new SqlParameter("@RowsCount", SqlDbType.Int) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetDataExchangeLibraryData", sqlParameters, out outValue);
            int totalRows = outValue?.Count() > 0 ? (int)outValue["@RowsCount"] : 0;
            List<DataExchangeModel> dataModelList = znodeViewRepository.ConvertDataTableToList<DataExchangeModel>(response);
            DataExchangeListModel dataExchangeListModel = new DataExchangeListModel()
            {
                DataModelList = dataModelList,
                TotalRows = totalRows
            };
            return dataExchangeListModel;
        }

        /// <summary>
        /// To get the standard data exchange list.
        /// </summary>
        /// <param name="filters">filters for global search</param>
        /// <param name="sorts">sorts for sorting</param>
        /// <param name="page">page for pagination</param>
        /// <returns>StandardDataExchangeListModel</returns>
        public StandardDataExchangeListModel GetDataExchangeList(FilterCollection filters, NameValueCollection sorts, NameValueCollection page)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            var whereClause = DynamicClauseHelper.GenerateDynamicWhereClauseForSP(filters.ToFilterDataCollection());
            var orderBy = DynamicClauseHelper.GenerateDynamicOrderByClause(sorts);
            DynamicClauseHelper.SetPaging(page, out int pagingStart, out int pagingLength);
            sqlParameters.Add(new SqlParameter("@WhereClause", whereClause));
            sqlParameters.Add(new SqlParameter("@Rows", pagingLength));
            sqlParameters.Add(new SqlParameter("@PageNo", pagingStart));
            sqlParameters.Add(new SqlParameter("@Order_BY", orderBy));
            sqlParameters.Add(new SqlParameter("@RowsCount", SqlDbType.Int) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetZnodeBaseDefinitionData", sqlParameters, out outValue);
            int totalRows = outValue?.Count() > 0 ? (int)outValue["@RowsCount"] : 0;
            List<StandardDataExchangeModel> dataModelList = znodeViewRepository.ConvertDataTableToList<StandardDataExchangeModel>(response);
            StandardDataExchangeListModel listModel = new StandardDataExchangeListModel()
            {
                DataModelList = dataModelList,
                TotalRows = totalRows
            };
            return listModel;
        }

        public bool DeleteDataExchangeSchedular(int erpId)
        {
            int Status = 0;
            // Remove job from hangfire if any.
            bool jobRemoved = _hangfireHelper.RemoveHangfireJob(HelperMethods.GetSchedulerConfiguration(erpId));
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();

            List<SqlParameter> sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId", Value=erpId},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_DeleteERPDataExchangeDetails", sqlParameters, 1, out Status);

            return Convert.ToBoolean(Status);
        }

        public ProcessorLogListModel GetGenericLogList(FilterCollection filters, NameValueCollection sorts, NameValueCollection page, int logId = 0)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            var whereClause = DynamicClauseHelper.GenerateDynamicWhereClauseForSP(filters.ToFilterDataCollection());
            var orderBy = DynamicClauseHelper.GenerateDynamicOrderByClause(sorts);
            DynamicClauseHelper.SetPaging(page, out int pagingStart, out int pagingLength);
            sqlParameters.Add(new SqlParameter("@WhereClause", whereClause));
            sqlParameters.Add(new SqlParameter("@CustomProcessLogId", logId));
            sqlParameters.Add(new SqlParameter("@Rows", pagingLength));
            sqlParameters.Add(new SqlParameter("@PageNo", pagingStart));
            sqlParameters.Add(new SqlParameter("@Order_BY", orderBy));
            sqlParameters.Add(new SqlParameter("@RowsCount", SqlDbType.Int) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetImportDataExchangeProcessLog", sqlParameters, out outValue);
            int totalRows = outValue?.Count() > 0 ? (int)outValue["@RowsCount"] : 0;
            List<ProcessorLogModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ProcessorLogModel>(response);
            ProcessorLogListModel listModel = new ProcessorLogListModel()
            {
                ProcessorLogs = dataModelList,
                TotalRecords = totalRows
            };
            return listModel;
        }

        public ProcessingLogDetailListModel GetGenericLogDetails(FilterCollection filters, NameValueCollection sorts, NameValueCollection page, int logId)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            var whereClause = DynamicClauseHelper.GenerateDynamicWhereClauseForSP(filters.ToFilterDataCollection());
            var orderBy = DynamicClauseHelper.GenerateDynamicOrderByClause(sorts);
            DynamicClauseHelper.SetPaging(page, out int pagingStart, out int pagingLength);
            sqlParameters.Add(new SqlParameter("@WhereClause", whereClause));
            sqlParameters.Add(new SqlParameter("@CustomProcessLogId", logId));
            sqlParameters.Add(new SqlParameter("@Rows", pagingLength));
            sqlParameters.Add(new SqlParameter("@PageNo", pagingStart));
            sqlParameters.Add(new SqlParameter("@Order_BY", orderBy));
            sqlParameters.Add(new SqlParameter("@RowsCount", SqlDbType.Int) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetDataExchangeImportLog", sqlParameters, out outValue);
            int totalRows = outValue?.Count() > 0 ? (int)outValue["@RowsCount"] : 0;
            List<ProcessingLogDetailsModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ProcessingLogDetailsModel>(response);
            ProcessingLogDetailListModel listModel = new ProcessingLogDetailListModel()
            {
                ProcessingLogDetails = dataModelList,
                TotalRecords = totalRows
            };
            return listModel;
        }

    }
}
