﻿@using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="d-flex my-2">
    <div class="data-library-label d-flex text-nowrap">
        <label class="pb-1 fw-bold" data-test-selector="lblExchangeName">@CommerceConnector_Resources.ExchangeName</label>
        <span class="text-danger required" data-test-selector="spnRequired"></span>
    </div>
    <div class="data-library-input ps-3 w-100">
        <input type="text" class="pb-1" id="dataExchangeName" data-test-selector="txtExchangeName" aria-label="Data Exchange Name" />
        <span id="dataExchangeNameError" data-test-selector="spnDataExchangeNameError" class="field-validation-error pt-1"> </span>
    </div>
</div>
@Html.Hidden("selectedExchangeName")