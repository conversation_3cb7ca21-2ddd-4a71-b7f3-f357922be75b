﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeImportDataExchangeLog
{
    public int ImportDataExchangeLogId { get; set; }

    public int ImportDataExchangeProcessLogId { get; set; }

    public int ErpbaseDefinitionId { get; set; }

    public string ImportLogMessage { get; set; } = null!;

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeErpbaseDefinition ErpbaseDefinition { get; set; } = null!;

    public virtual ZnodeImportDataExchangeProcessLog ImportDataExchangeProcessLog { get; set; } = null!;
}
