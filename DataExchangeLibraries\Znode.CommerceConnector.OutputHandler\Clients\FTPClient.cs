﻿using System.Net;
using System.Text;
using Znode.CommerceConnector.Model;

namespace Znode.CommerceConnector.OutputHandler
{
    public class FTPClient : IFTPClient
    {
        public bool UploadData<T>(FTPConfigurationModel fTPConfigurationModel, string csv, bool returnWithoutDeserialize = false)
        {
            string host = fTPConfigurationModel.ServerAddress.Trim();
            string username = fTPConfigurationModel.UserName.Trim();
            string password = fTPConfigurationModel.Password.Trim();
            string remoteFolder = fTPConfigurationModel.FolderPath.Trim();
            string fileName = fTPConfigurationModel.FileName.Trim();
            string csvContent = csv;

            try
            {
                string ftpUrl = $"ftp://{host}/{remoteFolder}/{fileName}";

                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
                request.Method = WebRequestMethods.Ftp.UploadFile;
                request.Credentials = new NetworkCredential(username, password);
                request.UseBinary = true;
                request.UsePassive = true;
                request.EnableSsl = true;
                request.KeepAlive = false;
                byte[] fileContents = Encoding.UTF8.GetBytes(csvContent);
                request.ContentLength = fileContents.Length;
                ServicePointManager.ServerCertificateValidationCallback = (sender, cert, chain, sslPolicyErrors) => true;
                bool checkFile = false;
                bool fileExists = false;

                try
                {
                    if (fileContents.Length > 0)
                    {
                        fileExists = FtpFileExists(ftpUrl, username, password);
                        if (!fileExists)
                        {
                            using (Stream requestStream = request.GetRequestStream())
                            {
                                do
                                {
                                    requestStream.Write(fileContents, 0, fileContents.Length);
                                    checkFile = IsFileReady(ftpUrl, fileContents.Length, username, password);
                                }
                                while (!checkFile);
                                checkFile = true;
                                return true;
                            }
                        }
                    }
                    return true;
                }
                catch (Exception ex)
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        bool IsFileReady(string fileUrl, int expectedSize, string username, string password)
        {
            for (int i = 0; i < 5; i++)
            {
                try
                {
                    FtpWebRequest sizeRequest = (FtpWebRequest)WebRequest.Create(fileUrl);
                    sizeRequest.Method = WebRequestMethods.Ftp.GetFileSize;
                    sizeRequest.Credentials = new NetworkCredential(username, password);
                    sizeRequest.UseBinary = true;
                    sizeRequest.UsePassive = true;
                    sizeRequest.EnableSsl = true;
                    sizeRequest.KeepAlive = false;
                    using (FtpWebResponse sizeResponse = (FtpWebResponse)sizeRequest.GetResponse())
                    {
                        if ((int)sizeResponse.ContentLength == expectedSize)
                            return true;
                    }
                }
                catch
                {
                    return false;
                }

                Thread.Sleep(1000);
            }
            return false;
        }

        public bool FtpFileExists(string ftpUrl, string username, string password)
        {
            try
            {
                FtpWebRequest request = (FtpWebRequest)WebRequest.Create(ftpUrl);
                request.Method = WebRequestMethods.Ftp.GetFileSize;
                request.Credentials = new NetworkCredential(username, password);
                request.UseBinary = true;
                request.UsePassive = true;
                request.EnableSsl = true;

                using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
                {
                    return true;
                }
            }
            catch (WebException ex)
            {
                FtpWebResponse response = (FtpWebResponse)ex.Response;
                if (response != null && response.StatusCode == FtpStatusCode.ActionNotTakenFileUnavailable)
                {
                    return false;
                }

                throw;
            }
        }
    }
}
