﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
    <ProjectReference Include="..\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj" />
    <ProjectReference Include="..\Znode.Libraries.Hangfire\Znode.Libraries.Hangfire.csproj" />
  </ItemGroup>

</Project>
