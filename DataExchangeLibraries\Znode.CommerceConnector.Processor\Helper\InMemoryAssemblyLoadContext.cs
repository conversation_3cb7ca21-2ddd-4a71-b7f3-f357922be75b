﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.Loader;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Processor.Helper
{
    public class InMemoryAssemblyLoadContext : AssemblyLoadContext
    {
        public InMemoryAssemblyLoadContext() : base(isCollectible: true) { }

        public Assembly LoadAssembly(byte[] assemblyData)
        {
            using var stream = new MemoryStream(assemblyData);
            return LoadFromStream(stream);
        }
    }
}
