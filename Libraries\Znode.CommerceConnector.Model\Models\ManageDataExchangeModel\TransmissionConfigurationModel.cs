﻿using Newtonsoft.Json;
using System.Data;
namespace Znode.CommerceConnector.Model
{
    public class TransmissionConfigurationModel
    {
        public int? TransmissionConfigurationId { get; set; }
        public int? ERPBaseDefinitionId { get; set; }
        public string? TransmissionMode { get; set; }
        public string? DestinationTransmissionMode { get; set; }
        public HttpsTransmissionConfigurationModel? HttpsConfig { get; set; }

        public SftpTransmissionConfigurationModel? SFTPConfig { get; set; }

        public SftpTransmissionConfigurationModel? SftpConfigDestination { get; set; }
        public HttpsTransmissionConfigurationModel? HttpsConfigDestination { get; set; }
    }
}
