﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Model.Models
{
    public class InforAPIRequestModel
    {
        public int companyNumber { get; set; }
        public string operatorInit { get; set; }
        public string operatorPassword { get; set; }
        public int customerNumber { get; set; }
        public string ediPartnerCode { get; set; }
        public string shipTo { get; set; }
        public bool getPriceBreaks { get; set; }
        public bool useDefaultWhse { get; set; }
        public bool sendFullQtyOnOrder { get; set; }
        public bool checkOtherWhseInventory { get; set; }
        public string pricingMethod { get; set; }
        public tOemultprcinV2 tOemultprcinV2 { get; set; }
    }

    public class tOemultprcinV2
    {
        [JsonProperty("t-oemultprcinV2")]
        public List<tOemultprcinV2Item> tOemultprcinV2List { get; set; }
    }

    public class tOemultprcinV2Item
    {
        public int seqNo { get; set; }
        public string whse { get; set; }
        public string prod { get; set; }
        public int qtyOrd { get; set; }
        public string unit { get; set; }
    }
}
