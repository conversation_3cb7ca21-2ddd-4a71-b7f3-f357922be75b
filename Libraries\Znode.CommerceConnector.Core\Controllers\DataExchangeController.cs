﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using System.Diagnostics;
using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.Models.WebGridModels;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.CommerceConnector.Model.Models;
using Znode.Libraries.Common.Logger;
using Znode.Libraries.ECommerce.Utilities;
using Znode.Libraries.Resources.CommerceConnector_Resources;

namespace Znode.CommerceConnector.Core
{
    public class DataExchangeController : Controller
    {
        private readonly ICCDataExchangeAgent _exchangeAgent;
        public DataExchangeController(ICCDataExchangeAgent exchangeData)
        {
            _exchangeAgent = exchangeData;
        }

        /// <summary>
        /// Render view of GetDataExchangeLibraryList
        /// </summary>
        /// <returns>View</returns>
        [HttpGet]
        public virtual ActionResult GetDataExchangeLibraryList()
        {
            string token = Request.Headers[CCAdminConstant.CCAdminToken].ToString();
            token = Request.Query[CCAdminConstant.CCAdminToken].ToString();
            if(string.IsNullOrEmpty(token))
            token=Convert.ToString(TempData[CCAdminConstant.CCAdminToken]);

            if (!string.IsNullOrEmpty(token) && !Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
            {
                Request.Headers.Add(CCAdminConstant.CCAdminToken,token);
            }
            ViewBag.token=token;
            return View("DataExchange");
        }
          

        /// <summary>
        /// To render the dynamic grid.
        /// </summary>
        /// <param name="request">grid request model.</param>
        /// <returns>Json data</returns>
        [HttpPost]
        public IActionResult RenderDataExchangeGrid([FromBody] GridModel request)
        {
            if (HelperUtility.IsNotNull(request))
            {
                MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.LogRenderDataExchangeGridMethodStart, TraceLevel.Info);
                StandardDataExchangesListViewModel dataExchangeModel = _exchangeAgent.GetStandardDataExchangeList(request);
                if (HelperUtility.IsNotNull(dataExchangeModel.DataExchangeList))
                {
                    GridResponseModel result = GridHelper.GenerateDynamicGrid(dataExchangeModel.DataExchangeList, request);
                    MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.LogRenderDataExchangeGridMethodEnd, TraceLevel.Info);
                    return Json(new
                    {
                        rows = result.Data,
                        columns = result.Columns,
                        totalCount = dataExchangeModel.TotalRows,
                        page = request.Page,
                        pageSize = request.PageSize,
                        filteredCount = result.FilteredCount,
                        pageCount = (int)Math.Ceiling((double)dataExchangeModel.TotalRows / (double)request.PageSize)
                    });
                }
            }
            return Json(new { rows = new GridResponseModel().Data });
        }

        public virtual JsonResult TriggerDataExchangeSchedular(int erpId)
        {
            string token = Request.Headers[CCAdminConstant.CCAdminToken].ToString();
            bool result = _exchangeAgent.TriggerDataExchangeSchedular(erpId);
            return Json(new
            {
                status = result,
                message = result ? CCAdminConstant.TaskSchedulerTriggeredSuccessfully : CCAdminConstant.TaskSchedulerTriggeredFailed
            });
        }

        [HttpPost]
        public virtual ActionResult RealTimeDataScheduler([FromQuery] int erpId, [FromBody] List<RequestFilterModel> filterList, dynamic requestData)
        {
            dynamic result = _exchangeAgent.RealTimeDataScheduler(requestData, erpId);
            if (result)
            {
                TempData[CCAdminConstant.SuccessfulMessage] = CCAdminConstant.TaskSchedulerTriggeredSuccessfully;
            }
            else
            {
                TempData[CCAdminConstant.FailureMessage] = CCAdminConstant.TaskSchedulerTriggeredFailed;
            }
            return RedirectToAction("GetDataExchangeLibraryList", "DataExchange");
        }

        public virtual JsonResult DeleteDataExchangeSchedularById(int erpId)
        {
            bool result = _exchangeAgent.DeleteDataExchangeSchedular(erpId);
            return Json(new
            {
                status = result,
                message = result ? CommerceConnector_Resources.DeleteTaskSchedularSuccessMessage : CommerceConnector_Resources.DeleteTaskSchedularErrorMessage
            });
        }

        public virtual ActionResult EnableDisableDataExchangeScheduler(int erpId, bool IsEnabled)
        {
            var token = Request.Query[CCAdminConstant.CCAdminToken].ToString();
           if(string.IsNullOrEmpty(token) && Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
            {
                token = Request.Headers[CCAdminConstant.CCAdminToken];
            }
            else if (!Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
            {
                
                Request.Headers.Add(CCAdminConstant.CCAdminToken, token);
            }

            bool result = _exchangeAgent.EnableDisableDataExchangeScheduler(erpId, IsEnabled);
            if (result)
            {
                TempData["SuccessMessage"] = CCAdminConstant.DataExchangeEnabledSuccessMessage;
            }
            else
            {
                TempData["ErrorMessage"] = CCAdminConstant.DataExchangeDisabledMessage;
            }
            return RedirectToAction("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token });

        }

        /// <summary>
        /// Render view of Processing Logs
        /// </summary>
        /// <returns>View</returns>
        [HttpGet]
        public virtual ActionResult GetProcessingLogs(int erpBaseDefinitionId = 0, string exchangeName = "")
        {

            TempData["ExchangeName"] = exchangeName;
            TempData["ERPBaseDefinitionId"] = erpBaseDefinitionId;
            return View("ProcessingLogsList");
        }

        /// <summary>
        /// To render the dynamic grid.
        /// </summary>
        /// <param name="request">grid request model.</param>
        /// <returns>Json data</returns>
        [HttpPost]
        public IActionResult RenderLogsGrid([FromBody] LogListRequestModel requestModel)
        {
            if (HelperUtility.IsNotNull(requestModel.GridModel))
            {
                MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.RenderProcessingLogsGridMethodStart, TraceLevel.Info);
                ProcessingLogListViewModel processingLogModel = new ProcessingLogListViewModel();
                if (requestModel.IsFromImportLogs)
                    processingLogModel = _exchangeAgent.GetZnodeImportLogList(requestModel.GridModel, requestModel.ExchangeName);
                else
                    processingLogModel = _exchangeAgent.GetProcessingLogList(requestModel);
                if (HelperUtility.IsNotNull(processingLogModel.ProcessingLogList))
                {
                    GridResponseModel result = GridHelper.GenerateDynamicGrid(processingLogModel.ProcessingLogList, requestModel.GridModel);
                    MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.RenderProcessingLogsGridMethodEnd, TraceLevel.Info);
                    return Json(new
                    {
                        rows = result.Data,
                        columns = result.Columns,
                        totalCount = processingLogModel.TotalRows,
                        page = requestModel.GridModel.Page,
                        pageSize = requestModel.GridModel.PageSize,
                        filteredCount = result.FilteredCount,
                        pageCount = (int)Math.Ceiling((double)processingLogModel.TotalRows / (double)requestModel.GridModel.PageSize)
                    });
                }
            }
            return Json(new { rows = new GridResponseModel().Data });
        }

        /// <summary>
        /// Render view of Processing Logs
        /// </summary>
        /// <returns>View</returns>
        [HttpGet]
        public virtual ActionResult GetZnodeImportLogs(string exchangeName = "")
        {
            //string token = Request.Headers["token"].ToString();
            //ViewBag.token = token;
            TempData["ExchangeName"] = exchangeName;
            return View("ZnodeImportLogsList");
        }

        [HttpPost]
        public IActionResult ShowLogDetails([FromBody] ShowLogDetailsRequest model)
        {
            if (HelperUtility.IsNotNull(model.Request) && model.ProcessLogId > 0)
            {
                MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.ShowLogDetailsMethodStart, TraceLevel.Info);
                LogDetailsListViewModel processingLogModel = new LogDetailsListViewModel();
                if (model.IsFromImportLogs)
                    processingLogModel = _exchangeAgent.GetProcessingLogDetails(model.Request, model.ProcessLogId);
                if (HelperUtility.IsNotNull(processingLogModel?.LogDetailsList))
                {
                    GridResponseModel result = GridHelper.GenerateDynamicGrid(processingLogModel.LogDetailsList, model.Request);
                    MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.ShowLogDetailsMethodEnd, TraceLevel.Info);
                    return Json(new
                    {
                        successRecordCount = processingLogModel.ProcessingLogs?.SuccessRecordCount,
                        totalProcessedRecords = processingLogModel.ProcessingLogs?.TotalProcessedRecords,
                        failedRecordcount = processingLogModel.ProcessingLogs?.FailedRecordcount,
                        rows = result.Data,
                        columns = result.Columns,
                        totalCount = processingLogModel.TotalRows,
                        page = model.Request.Page,
                        pageSize = model.Request.PageSize,
                        filteredCount = result.FilteredCount,
                        pageCount = (int)Math.Ceiling((double)processingLogModel.TotalRows / (double)model.Request.PageSize)
                    });
                }
            }
            return Json(new { rows = new GridResponseModel().Data });
        }

        [HttpPost]
        public IActionResult ShowProcessingLogDetails([FromBody] ShowLogDetailsRequest model)
        {
            if (HelperUtility.IsNotNull(model.Request) && model.ProcessLogId > 0)
            {
                GridResponseModel result = new GridResponseModel();
                MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.ShowLogDetailsMethodStart, TraceLevel.Info);
                ProcessingLogDetailListViewModel processingLogModel = _exchangeAgent.GetGenericLogDetails(model.Request, model.ProcessLogId);
                if (HelperUtility.IsNotNull(processingLogModel?.ProcessingLogDetails))
                {
                    if (processingLogModel.ProcessingLogDetails.FirstOrDefault()?.ImportDataExchangeLogId > 0)
                        result = GridHelper.GenerateDynamicGrid(processingLogModel.ProcessingLogDetails, model.Request);
                    MongoLogging.LogMessage(ZnodeLoggingEnum.Components.ERP.ToString(), MongoLoggingConstants.ShowLogDetailsMethodEnd, TraceLevel.Info);
                    return Json(new
                    {
                        successRecordCount = processingLogModel.ProcessingLogDetails?.FirstOrDefault()?.SuccessRecordCount ?? 0,
                        totalProcessedRecords = processingLogModel.ProcessingLogDetails?.FirstOrDefault()?.TotalProcessedRecords ?? 0,
                        failedRecordcount = processingLogModel.ProcessingLogDetails?.FirstOrDefault()?.FailedRecordcount ?? 0,
                        rows = result.Data,
                        columns = result.Columns,
                        totalCount = processingLogModel.TotalRecords,
                        page = model.Request.Page,
                        pageSize = model.Request.PageSize,
                        filteredCount = result.FilteredCount,
                        pageCount = (int)Math.Ceiling((double)processingLogModel.TotalRecords / (double)model.Request.PageSize)
                    });
                }
            }
            return Json(new { rows = new GridResponseModel().Data });
        }

        public virtual IActionResult GetLogHistory(int erpId, string exchangeName = "")
        {
            string token = Convert.ToString(Request.Query[CCAdminConstant.CCAdminToken].ToString());
            TempData[CCAdminConstant.CCAdminToken] = token;
            if (!string.IsNullOrEmpty(token) && !Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
            {
                Request.Headers.Add(CCAdminConstant.CCAdminToken, token);
            }
            if (erpId > 0)
            {
                return RedirectToAction("GetProcessingLogs", "DataExchange", new {erpBaseDefinitionId = erpId, exchangeName, CCAdminToken= token });
            }
            return RedirectToAction("GetDataExchangeLibraryList", "DataExchange");
        }
    }
}
