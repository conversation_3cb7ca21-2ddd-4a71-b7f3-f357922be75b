﻿using System.Collections.ObjectModel;
using System.Web;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client.Clients
{
    public abstract class BaseClient
    {
        private readonly string znodeApiUriItemSeparator = ",";
        private readonly string znodeApiUriKeyValueSeparator = "~";
        private readonly string znodeCommaReplacer = "^";

        public string BuildEndpointQueryString(Collection<string> expands, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex, int? pageSize) =>
  string.Concat(BuildExpandQueryString(expands), BuildFilterQueryString(filters), BuildSortQueryString(sorts), BuildPageQueryString(pageIndex, pageSize));

        // This is to call Znode API having filter and need to pass with single quote.
        public string BuildEndpointQueryStringWithoutEncode(Collection<string> expands, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex, int? pageSize) =>
string.Concat(BuildExpandQueryString(expands), BuildFilterQueryStringWithoutEncode(filters), BuildSortQueryString(sorts), BuildPageQueryString(pageIndex, pageSize));

        private string BuildExpandQueryString(Collection<string> expands)
        {
            string queryString = "?expand=";

            if (expands != null)
            {
                foreach (string e in expands)
                    queryString += e + znodeApiUriItemSeparator;

                queryString = queryString.TrimEnd(znodeApiUriItemSeparator.ToCharArray());
            }

            return queryString;
        }

        private string BuildFilterQueryString(FilterCollection filters)
        {
            string queryString = "&filter=";

            if (filters != null)
            {
                foreach (FilterTuple f in filters)
                    queryString += $"{f.FilterName}{znodeApiUriKeyValueSeparator}{f.FilterOperator}{znodeApiUriKeyValueSeparator}{HttpUtility.UrlEncode(f.FilterValue?.Replace(",", znodeCommaReplacer))}{znodeApiUriItemSeparator}";

                queryString = queryString.TrimEnd(znodeApiUriItemSeparator.ToCharArray());
            }

            return queryString;
        }

        private string BuildFilterQueryStringWithoutEncode(FilterCollection filters)
        {
            string queryString = "&filter=";

            if (filters != null)
            {
                foreach (FilterTuple f in filters)
                    queryString += $"{f.FilterName}{znodeApiUriKeyValueSeparator}{f.FilterOperator}{znodeApiUriKeyValueSeparator}'{f.FilterValue?.Replace(",", znodeCommaReplacer)}'{znodeApiUriItemSeparator}";

                queryString = queryString.TrimEnd(znodeApiUriItemSeparator.ToCharArray());
            }

            return queryString;
        }

        private string BuildSortQueryString(Dictionary<string, string> sorts)
        {
            string queryString = "&sort=";

            if (sorts != null)
            {
                foreach (KeyValuePair<string, string> s in sorts)
                    queryString += $"{s.Key}{znodeApiUriKeyValueSeparator}{s.Value}{znodeApiUriItemSeparator}";

                queryString = queryString.TrimEnd(znodeApiUriItemSeparator.ToCharArray());
            }

            return queryString;
        }

        private string BuildPageQueryString(int? pageIndex, int? pageSize)
        {
            string queryString = "&page=";

            if (pageIndex.HasValue && pageSize.HasValue)
            {
                queryString += $"index{znodeApiUriKeyValueSeparator}{pageIndex.Value}";
                queryString += znodeApiUriItemSeparator;
                queryString += $"size{znodeApiUriKeyValueSeparator} {pageSize.Value}";
            }

            return queryString;
        }

    }
}