﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.Data.Models;

namespace Znode.Libraries.Data.Data;

public partial class AppDbContext : DbContext
{
    public AppDbContext()
    {
    }

    public AppDbContext(DbContextOptions<AppDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<ZnodeApiconfiguration> ZnodeApiconfigurations { get; set; }

    public virtual DbSet<ZnodeDataExchangeLibrary> ZnodeDataExchangeLibraries { get; set; }

    public virtual DbSet<ZnodeErpbaseDefinition> ZnodeErpbaseDefinitions { get; set; }

    public virtual DbSet<ZnodeErpbaseDefinitionScheduler> ZnodeErpbaseDefinitionSchedulers { get; set; }

    public virtual DbSet<ZnodeErpdataExchangeDataLog> ZnodeErpdataExchangeDataLogs { get; set; }

    public virtual DbSet<ZnodeErpdataExchangeHeader> ZnodeErpdataExchangeHeaders { get; set; }

    public virtual DbSet<ZnodeErpdataExchangeParameter> ZnodeErpdataExchangeParameters { get; set; }

    public virtual DbSet<ZnodeErpdataExchangeStatus> ZnodeErpdataExchangeStatuses { get; set; }

    public virtual DbSet<ZnodeGlobalSetting> ZnodeGlobalSettings { get; set; }

    public virtual DbSet<ZnodeImportDataExchangeLog> ZnodeImportDataExchangeLogs { get; set; }

    public virtual DbSet<ZnodeImportDataExchangeProcessLog> ZnodeImportDataExchangeProcessLogs { get; set; }

    public virtual DbSet<ZnodeProceduresErrorLog> ZnodeProceduresErrorLogs { get; set; }

    public virtual DbSet<ZnodeSftpconfiguration> ZnodeSftpconfigurations { get; set; }

    public virtual DbSet<ZnodeTimeZone> ZnodeTimeZones { get; set; }

    public virtual DbSet<ZnodeTransmissionConfiguration> ZnodeTransmissionConfigurations { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. You can avoid scaffolding the connection string by using the Name= syntax to read it from configuration - see https://go.microsoft.com/fwlink/?linkid=2131148. For more guidance on storing connection strings, see https://go.microsoft.com/fwlink/?LinkId=723263.
        => optionsBuilder.UseSqlServer(HelperMethods.ConnectionString);

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<ZnodeApiconfiguration>(entity =>
        {
            entity.HasKey(e => e.ApiconfigurationId)
                .HasName("PK_APIConfigurationId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeAPIConfiguration", "cco");

            entity.HasIndex(e => new { e.Type, e.TransmissionConfigurationId }, "IDX_ZnodeTransmissionConfiguration_ConfigurationId_Type_INC_Columns");

            entity.Property(e => e.ApiconfigurationId).HasColumnName("APIConfigurationId");
            entity.Property(e => e.AccessTokenUrl)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("AccessTokenURL");
            entity.Property(e => e.Apiaction)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("APIAction");
            entity.Property(e => e.AuthType)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ClientId)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ClientSecret)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Endpoint)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Format).HasMaxLength(256);
            entity.Property(e => e.GrantType)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.LocationOfCredentials)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Scope)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .IsUnicode(false);

            entity.HasOne(d => d.TransmissionConfiguration).WithMany(p => p.ZnodeApiconfigurations)
                .HasForeignKey(d => d.TransmissionConfigurationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeAPIConfiguration_ZnodeTransmissionConfiguration");
        });

        modelBuilder.Entity<ZnodeDataExchangeLibrary>(entity =>
        {
            entity.HasKey(e => e.DataExchangeLibraryId)
                .HasName("PK_DataExchangeLibraryId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeDataExchangeLibrary", "cco");

            entity.HasIndex(e => e.DataExchangeLibraryId, "IDX_ZnodeDataExchangeLibrary_DataExchangeLibraryId_INC_Columns");

            entity.HasIndex(e => e.ProcessorFileName, "IDX_ZnodeDataExchangeLibrary_ProcessorFileName");

            entity.Property(e => e.Access)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Collection)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.DataDestination)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.DataSource)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Description)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Format)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Library)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ProcessorFileName)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.Tags)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.TriggerOrigin)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Version)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ZnodeErpbaseDefinition>(entity =>
        {
            entity.HasKey(e => e.ErpbaseDefinitionId)
                .HasName("PK_ERPBaseDefinitionId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeERPBaseDefinition", "cco");

            entity.HasIndex(e => e.ProcessorFileName, "IDX_ZnodeBaseDefinitionTable_ProcessorFileName");

            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.Access)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Collection)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.DataDestination)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.DataSource)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Description)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Format)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Library)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.MethodName)
                .HasMaxLength(128)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Name)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ProcessorFileName)
                .HasMaxLength(128)
                .IsUnicode(false);
            entity.Property(e => e.Tags)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.TriggerOrigin)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Version)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.YamlfileName)
                .HasMaxLength(256)
                .HasColumnName("YAMLFileName");

            entity.HasOne(d => d.DataExchangeLibrary).WithMany(p => p.ZnodeErpbaseDefinitions)
                .HasForeignKey(d => d.DataExchangeLibraryId)
                .HasConstraintName("FK_ZnodeERPBaseDefinition_ZnodeDataExchangeLibrary");
        });

        modelBuilder.Entity<ZnodeErpbaseDefinitionScheduler>(entity =>
        {
            entity.HasKey(e => e.ErpbaseDefinitionSchedulerId)
                .HasName("PK_ERPBaseDefinitionSchedulerId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeERPBaseDefinitionScheduler", "cco");

            entity.Property(e => e.ErpbaseDefinitionSchedulerId).HasColumnName("ERPBaseDefinitionSchedulerId");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.CronExpression)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.HangfireJobId)
                .HasMaxLength(16)
                .IsUnicode(false);
            entity.Property(e => e.LastRunTime).HasColumnType("datetime");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.ProcessorFileName)
                .HasMaxLength(255)
                .IsUnicode(false);
            entity.Property(e => e.SchedulerFrequency)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.SchedulerName)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.SchedulerType)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.StartDate).HasColumnType("datetime");

            entity.HasOne(d => d.ErpbaseDefinition).WithMany(p => p.ZnodeErpbaseDefinitionSchedulers)
                .HasForeignKey(d => d.ErpbaseDefinitionId)
                .HasConstraintName("FK_ZnodeERPBaseDefinitionScheduler_ZnodeERPBaseDefinition");
        });

        modelBuilder.Entity<ZnodeErpdataExchangeDataLog>(entity =>
        {
            entity.HasKey(e => e.ErpdataExchangeDataLogId).HasName("PK__ZnodeERP__22A7A8678435D357");

            entity.ToTable("ZnodeERPDataExchangeDataLog", "cco");

            entity.HasIndex(e => e.ErpbaseDefinitionId, "IDX_ZnodeERPDataExchangeDataLog_ERPBaseDefinitionId_INC_UserName_FieldName_OldValue_NewValue");

            entity.Property(e => e.ErpdataExchangeDataLogId).HasColumnName("ERPDataExchangeDataLogId");
            entity.Property(e => e.ChangeDateTime).HasColumnType("datetime");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.FieldName).HasMaxLength(128);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.NewValue).HasMaxLength(256);
            entity.Property(e => e.OldValue).HasMaxLength(256);
            entity.Property(e => e.UserName).HasMaxLength(256);

            entity.HasOne(d => d.ErpbaseDefinition).WithMany(p => p.ZnodeErpdataExchangeDataLogs)
                .HasForeignKey(d => d.ErpbaseDefinitionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeERPDataExchangeDataLog_ERPBaseDefinitionId");
        });

        modelBuilder.Entity<ZnodeErpdataExchangeHeader>(entity =>
        {
            entity.HasKey(e => e.ErpdataExchangeHeaderId)
                .HasName("PK_ERPDataExchangeHeaderId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeERPDataExchangeHeader", "cco");

            entity.Property(e => e.ErpdataExchangeHeaderId).HasColumnName("ERPDataExchangeHeaderId");
            entity.Property(e => e.ApiconfigurationId).HasColumnName("APIConfigurationId");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Key)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Value)
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.Apiconfiguration).WithMany(p => p.ZnodeErpdataExchangeHeaders)
                .HasForeignKey(d => d.ApiconfigurationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeERPDataExchangeHeader_ZnodeAPIConfiguration");
        });

        modelBuilder.Entity<ZnodeErpdataExchangeParameter>(entity =>
        {
            entity.HasKey(e => e.ErpdataExchangeParameterId)
                .HasName("PK_ERPDataExchangeParameterId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeERPDataExchangeParameter", "cco");

            entity.Property(e => e.ErpdataExchangeParameterId).HasColumnName("ERPDataExchangeParameterId");
            entity.Property(e => e.ApiconfigurationId).HasColumnName("APIConfigurationId");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.Key)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.Value)
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.Apiconfiguration).WithMany(p => p.ZnodeErpdataExchangeParameters)
                .HasForeignKey(d => d.ApiconfigurationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeERPDataExchangeParameter_ZnodeAPIConfiguration");
        });

        modelBuilder.Entity<ZnodeErpdataExchangeStatus>(entity =>
        {
            entity.HasKey(e => e.ErpdataExchangeStatusId)
                .HasName("PK_ERPDataExchangeStatusId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeERPDataExchangeStatus", "cco");

            entity.HasIndex(e => e.ErpbaseDefinitionId, "IDX_ZnodeERPDataExchangeStatus_ERPBaseDefinitionId").IsUnique();

            entity.Property(e => e.ErpdataExchangeStatusId).HasColumnName("ERPDataExchangeStatusId");
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.ErrorNotifications).HasMaxLength(600);
            entity.Property(e => e.InformationNotifications).HasMaxLength(600);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.WarningNotifications).HasMaxLength(600);

            entity.HasOne(d => d.ErpbaseDefinition).WithOne(p => p.ZnodeErpdataExchangeStatus)
                .HasForeignKey<ZnodeErpdataExchangeStatus>(d => d.ErpbaseDefinitionId)
                .HasConstraintName("FK_ZnodeERPDataExchangeStatus_ZnodeERPBaseDefinition");
        });

        modelBuilder.Entity<ZnodeGlobalSetting>(entity =>
        {
            entity.HasKey(e => e.ZnodeGlobalSettingId).HasFillFactor(90);

            entity.ToTable("ZnodeGlobalSetting");

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.FeatureName).HasMaxLength(100);
            entity.Property(e => e.FeatureSubValues).HasMaxLength(600);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
        });

        modelBuilder.Entity<ZnodeImportDataExchangeLog>(entity =>
        {
            entity.HasKey(e => e.ImportDataExchangeLogId).HasName("PK__ZnodeImp__47F1512314688239");

            entity.ToTable("ZnodeImportDataExchangeLog", "cco");

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.ImportLogMessage).HasMaxLength(512);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");

            entity.HasOne(d => d.ErpbaseDefinition).WithMany(p => p.ZnodeImportDataExchangeLogs)
                .HasForeignKey(d => d.ErpbaseDefinitionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ERPBaseDefinitionId");

            entity.HasOne(d => d.ImportDataExchangeProcessLog).WithMany(p => p.ZnodeImportDataExchangeLogs)
                .HasForeignKey(d => d.ImportDataExchangeProcessLogId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ImportDataExchangeProcessLogId");
        });

        modelBuilder.Entity<ZnodeImportDataExchangeProcessLog>(entity =>
        {
            entity.HasKey(e => e.ImportDataExchangeProcessLogId);

            entity.ToTable("ZnodeImportDataExchangeProcessLog", "cco");

            entity.HasIndex(e => new { e.ImportDataExchangeProcessLogId, e.ErpbaseDefinitionId }, "IDX_CustomImportProcessLog");

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.ProcessCompletedDate).HasColumnType("datetime");
            entity.Property(e => e.ProcessStartedDate).HasColumnType("datetime");
            entity.Property(e => e.StatusName)
                .HasMaxLength(32)
                .IsUnicode(false);
            entity.Property(e => e.TemplateName)
                .HasMaxLength(64)
                .IsUnicode(false);

            entity.HasOne(d => d.ErpbaseDefinition).WithMany(p => p.ZnodeImportDataExchangeProcessLogs)
                .HasForeignKey(d => d.ErpbaseDefinitionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeCustomImportProcessLog_ERPBaseDefinitionId");
        });

        modelBuilder.Entity<ZnodeProceduresErrorLog>(entity =>
        {
            entity.HasKey(e => e.ProcedureErrorLogId)
                .HasName("PK_Znode_ProcdureErrorLog")
                .HasFillFactor(90);

            entity.ToTable("ZnodeProceduresErrorLog");

            entity.Property(e => e.CreatedBy)
                .HasMaxLength(500)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.ErrorInProcedure)
                .HasMaxLength(1000)
                .IsUnicode(false);
            entity.Property(e => e.ErrorLine)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.ProcedureName)
                .HasMaxLength(1000)
                .IsUnicode(false);
        });

        modelBuilder.Entity<ZnodeSftpconfiguration>(entity =>
        {
            entity.HasKey(e => e.SftpconfigurationId)
                .HasName("PK_TransmissionId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeSFTPConfiguration", "cco");

            entity.HasIndex(e => e.TransmissionConfigurationId, "IDX_ZnodeSFTPConfiguration_ConfigurationId_INC_Columns");

            entity.Property(e => e.SftpconfigurationId).HasColumnName("SFTPConfigurationId");
            entity.Property(e => e.ActionAfterRetrieval)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.FileName)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.FolderPath)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Format).HasMaxLength(256);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.Password)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ServerAddress)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.Type)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.UserName)
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.TransmissionConfiguration).WithMany(p => p.ZnodeSftpconfigurations)
                .HasForeignKey(d => d.TransmissionConfigurationId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeSFTPConfiguration_ZnodeTransmissionConfiguration");
        });

        modelBuilder.Entity<ZnodeTimeZone>(entity =>
        {
            entity.HasKey(e => e.TimeZoneId)
                .HasName("PK_TimeZoneDetails")
                .HasFillFactor(90);

            entity.ToTable("ZnodeTimeZone");

            entity.HasIndex(e => e.TimeZoneDetailsCode, "UK_TimeZoneDetails")
                .IsUnique()
                .HasFillFactor(90);

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.DaylightBeginsAt)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.DaylightEndsAt)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.DstinSeconds).HasColumnName("DSTInSeconds");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.TimeZoneDetailsCode).HasMaxLength(50);
            entity.Property(e => e.TimeZoneDetailsDesc).HasMaxLength(200);
        });

        modelBuilder.Entity<ZnodeTransmissionConfiguration>(entity =>
        {
            entity.HasKey(e => e.TransmissionConfigurationId)
                .HasName("PK_ConfigurationId")
                .HasFillFactor(90);

            entity.ToTable("ZnodeTransmissionConfiguration", "cco");

            entity.HasIndex(e => e.ErpbaseDefinitionId, "IDX_ZnodeTransmissionConfiguration_ERPBaseDefinitionId").IsUnique();

            entity.HasIndex(e => e.ErpbaseDefinitionId, "IDX_ZnodeTransmissionConfiguration_ERPBaseDefinitionId_INC_Columns");

            entity.HasIndex(e => e.ErpbaseDefinitionId, "IDX_ZnodeTransmissionConfiguration_ERPBaseDefinitionId_INC_ConfigurationId").IsUnique();

            entity.Property(e => e.CreatedDate).HasColumnType("datetime");
            entity.Property(e => e.DestinationTransmissionMode)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.ErpbaseDefinitionId).HasColumnName("ERPBaseDefinitionId");
            entity.Property(e => e.ModifiedDate).HasColumnType("datetime");
            entity.Property(e => e.SourceTransmissionMode)
                .HasMaxLength(200)
                .IsUnicode(false);

            entity.HasOne(d => d.ErpbaseDefinition).WithOne(p => p.ZnodeTransmissionConfiguration)
                .HasForeignKey<ZnodeTransmissionConfiguration>(d => d.ErpbaseDefinitionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_ZnodeTransmissionConfiguration_ZnodeERPBaseDefinition");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
