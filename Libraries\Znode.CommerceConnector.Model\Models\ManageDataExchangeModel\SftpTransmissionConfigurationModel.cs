﻿namespace Znode.CommerceConnector.Model
{
    public class SftpTransmissionConfigurationModel
    {
        public int? SFTPConfigurationId { get; set; }

        public int? TransmissionConfigurationId { get; set; }
        public string? ServerAddress { get; set; }
        public string? FolderPath { get; set; }
        public string? Filename { get; set; }
        public string? Username { get; set; }
        public string? Password { get; set; }
        public string? ActionAfterRetrieval { get; set; }
        public int? PortNumber { get; set; }
        public string? Format { get; set; }
    }
}
