﻿using Newtonsoft.Json;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.API
{
    public class APIHelpers
    {
        public static string ToJson(object data)
        {
            bool minifiedJsonResponse = true;
            return JsonConvert.SerializeObject(data, Formatting.None, new JsonSerializerSettings
            {
                DefaultValueHandling = (minifiedJsonResponse ? DefaultValueHandling.Ignore : DefaultValueHandling.Include)
            });
        }
    }
}
