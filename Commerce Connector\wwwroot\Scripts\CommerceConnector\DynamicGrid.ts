﻿class DynamicGrid {
    currentPage: number = 1;
    currentPageSize: number = 10;
    totalRows: number = 0;
    currentSortField: string | null = null;
    currentSortDirection: string | null = null;
    static DynamicGrid: any;

    public RenderPagination(currentPage: number, totalPages: number, element: string, callback: (page: number) => void): void {
        const isFirstPage = currentPage === 1;
        const isLastPage = currentPage === totalPages;
        const isSinglePage = totalPages <= 1;

        const paginationHtml = `
        <div class="page-limit d-flex align-items-center">
            <a id="first" href="javascript:void(0)" class="first ${isFirstPage || isSinglePage ? 'disabled' : ''}">
                <em class="z-backward-arrow pe-1" title="First"></em>
            </a>
            <a id="previousPage" href="javascript:void(0)" class="Previous ${isFirstPage || isSinglePage ? 'disabled' : ''}">
                <em class="z-first pe-1" title="Previous Page"></em>
            </a>
            <span>Page</span>
            <input type="text"
                   autocomplete="off"
                   class="pagerTxt"
                   id="pagerTxt"
                   value="${currentPage}"
                   onkeypress="return /^[0-9]+$/.test(event.key)">
            <span class="LowerCase pe-1">/ ${totalPages}</span>
            <a id="nextPage" href="javascript:void(0)" class="Next ${isLastPage || isSinglePage ? 'disabled' : ''}">
                <em class="z-last pe-1" title="Next Page"></em>
            </a>
            <a id="last" href="javascript:void(0)" class="last ${isLastPage || isSinglePage ? 'disabled' : ''}">
                <em class="z-forward-arrow" title="Last"></em>
            </a>
        </div>
    `;
        if (element != '') {
            $("#" + element).html(paginationHtml);
        }
        else {
            $("#paginationControls").html(paginationHtml);
        }

        $(document).off('click', '#first').on('click', '#first', () => {
            if (!isFirstPage && !isSinglePage) callback(1);
        });

        $(document).off('click', '#previousPage').on('click', '#previousPage', () => {
            if (!isFirstPage && !isSinglePage) callback(currentPage - 1);
        });

        $(document).off('click', '#nextPage').on('click', '#nextPage', () => {
            if (!isLastPage && !isSinglePage) callback(currentPage + 1);
        });

        $(document).off('click', '#last').on('click', '#last', () => {
            if (!isLastPage && !isSinglePage) callback(totalPages);
        });

        $(document).off('keypress', '#pagerTxt').on('keypress', '#pagerTxt', function (e) {
            if (e.key === "Enter") {
                const inputVal = parseInt($(this).val() as string);
                if (!isNaN(inputVal) && inputVal >= 1 && inputVal <= totalPages) {
                    callback(inputVal);
                } else {
                    $(this).val(currentPage); 
                }
            }
        });
    }

    public UpdateRowInfo(currentPage: number, pageSize: number, filteredCount: number, totalCount: number, element: string = ''): void {
        const startRow = ((currentPage - 1) * pageSize) + 1;
        const endRow = Math.min(currentPage * pageSize, totalCount);
        const text = filteredCount > 0
            ? `Showing ${startRow} - ${endRow} of ${totalCount}`
            : `No records found`;
        if (element != '') {
            document.getElementById(element)!.innerText = text;
        }
        else {
            document.getElementById("customRowInfo")!.innerText = text;
        }
    }

    public setTotalRows(count: number): void {
        this.totalRows = count;
    }

    public setCurrentPage(page: number): void {
        this.currentPage = page;
    }

    public setSorting(field: string | null, direction: string | null): void {
        this.currentSortField = field;
        this.currentSortDirection = direction;
    }
}