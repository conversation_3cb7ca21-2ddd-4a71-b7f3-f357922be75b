﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class HttpsTransmissionConfigurationViewModel
    {
        [Key]
        public int? APIConfigurationId { get; set; }

        [Display(Name = "API Action")]
        public string? HttpAction { get; set; }
        public string? Endpoint { get; set; }

        [Display(Name = "Authentication Type")]
        public string? AuthenticationType { get; set; }

        [Display(Name = "Grant Type")]
        public string? GrantType { get; set; }

        [DisplayName("Access Token URL")]
        public string? AccessTokenUrl { get; set; }

        [Display(Name = "Client ID")]
        public string? ClientId { get; set; }

        [Display(Name = "Client Secret")]
        public string? ClientSecret { get; set; }
        public string? Scope { get; set; }

        [Display(Name = "Location Of Credentials")]
        public string? LocationOfCredentials { get; set; }
        public int? TransmissionConfigurationId { get; set; }

        public string? Username { get; set; }
        public string? Password { get; set; }

        [Display(Name = "API Key")]
        public string? APIKey { get; set; }
        [Display(Name = "Query Parameters")]
        public List<KeyValueViewModel>? QueryParams { get; set; } = new();

        [Display(Name = "Header Parameters")]
        public List<KeyValueViewModel>? HeaderParams { get; set; } = new();

        public string? Format { get; set; }
    }
}
