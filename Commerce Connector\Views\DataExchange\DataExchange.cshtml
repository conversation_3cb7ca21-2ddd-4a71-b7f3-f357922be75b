﻿@model Znode.CommerceConnector.Core.ViewModels.StandardDataExchangesListViewModel
@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources
@using Znode.CommerceConnector.Core.ViewModels
@{
    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();

}
<div class="col-md-12 nopadding dashboard-title">
    <div class="title-container d-flex justify-content-between align-items-center">
        <h1 data-test-selector="hdgDataExchangeList">@CommerceConnector_Resources.DataExchangeList</h1>
        <input type="hidden" value="@token" id="hdnAdminToken" />
        <div class="alert-message">
            <div class="messageBoxContainer text-center" id="exchangeLibraryNotification" data-test-selector="divExchangeLibraryNotification"></div>
            @{
                @if (TempData["SuccessfulMessage"] != null)
                {
                    <div class="alert alert-success text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divSuccessMessage">
                        @TempData["SuccessfulMessage"]
                    </div>
                }
                else if (TempData["FailureMessage"] != null)
                {
                    <div class="alert alert-danger text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divFailureMessage">
                        @TempData["FailureMessage"]
                    </div>
                }
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divSuccessMessage">@TempData["SuccessMessage"]</div>
                }

                @if (TempData["ErrorMessage"] != null)
                {
                    <div class="alert alert-danger text-center p-2 mt-1 mb-0" id="triggerMessage" data-test-selector="divErrorMessage">@TempData["ErrorMessage"]</div>
                }
            }
        </div>
        <div class="mt-2" id="addDataExchangeList">
            <a href="/commerce-connector/ManageDataExchange/AddDataExchange?@CCAdminConstant.CCAdminToken=@token" class="btn-text btn-text-secondary me-2" data-test-selector="lnkAddNew" aria-label="Add New data Exchange">@CommerceConnector_Resources.AddNew</a>
            <a href="/commerce-connector/GetExchangeLibrary/GetAvailableLibraries?@CCAdminConstant.CCAdminToken=@token" class="btn-text btn-text-secondary" data-test-selector="lnkAddFromLibrary" aria-label="Add Data Exchange From Library">@CommerceConnector_Resources.AddFromLibrary</a>
        </div>
    </div>
</div>

<div class="col-md-12 page-container">
    <!-- Left Panel -->
    <div class="row">
        <div class="col-md-3 col-lg-2">
            <div class="left-panel-side list-group" id="configTabs" aria-label="Configuration Navigation">
                <ul class="left-panel-side-ul nav nav-tabs border-0 d-block" data-test-selector="listLeftPanelSideUl">
                    <li class="nav-item" data-test-selector="listDataExchange" data-section="DataExchange"><a class="nav-link active" href="/commerce-connector/DataExchange/GetDataExchangeLibraryList?@CCAdminConstant.CCAdminToken=@token"  data-test-selector="lnkDataExchange">@CommerceConnector_Resources.DataExchange</a></li>
                    <li class="nav-item" data-test-selector="listProcessingLog" data-section="ProcessingLog"><a class="nav-link" href="/commerce-connector/DataExchange/GetProcessingLogs?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkProcessingLog">@CommerceConnector_Resources.DataExchangeLog</a></li>
                    <li class="nav-item" data-test-selector="listImportLog" data-section="ImportLog"><a class="nav-link" href="/commerce-connector/DataExchange/GetZnodeImportLogs?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkImportLog">@CommerceConnector_Resources.LabelZnodeImportLog</a></li>
                </ul>
            </div>
        </div>

        <!-- Right Panel -->
        <div class="col-md-9 col-lg-10 px-3" id="configContent">
            <div class="tab-content">
                <div id="dataExchange" class="tab-pane active">
                    @Html.Partial("GetDataExchangeLibraryList", Model)
                </div>
            </div>
        </div>
    </div>
</div>