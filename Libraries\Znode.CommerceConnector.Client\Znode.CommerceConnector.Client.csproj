﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
    <ProjectReference Include="..\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
    <ProjectReference Include="..\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj" />
  </ItemGroup>

</Project>
