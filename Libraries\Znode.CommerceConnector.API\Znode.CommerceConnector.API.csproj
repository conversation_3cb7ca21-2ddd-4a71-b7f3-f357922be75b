﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Core" Version="5.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.3.0" />
    <PackageReference Include="SSH.NET" Version="2025.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="App_Start\" />
  </ItemGroup>

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\DataExchangeLibraries\Znode.CommerceConnector.Processor\Znode.CommerceConnector.Processor.csproj" />
    <ProjectReference Include="..\Znode.CommerceConnector.Hangfire\Znode.CommerceConnector.Hangfire.csproj" />
    <ProjectReference Include="..\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
    <ProjectReference Include="..\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
    <ProjectReference Include="..\Znode.Libraries.ECommerce.Utilities\Znode.Libraries.ECommerce.Utilities.csproj" />
  </ItemGroup>

</Project>
