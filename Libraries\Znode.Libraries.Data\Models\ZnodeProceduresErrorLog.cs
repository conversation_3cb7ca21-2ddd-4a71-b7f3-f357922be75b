﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeProceduresErrorLog
{
    public int ProcedureErrorLogId { get; set; }

    public string? ProcedureName { get; set; }

    public string? ErrorInProcedure { get; set; }

    public string? ErrorMessage { get; set; }

    public string? ErrorLine { get; set; }

    public string? ErrorCall { get; set; }

    public string CreatedBy { get; set; } = null!;

    public DateTime CreatedDate { get; set; }
}
