<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <location path="." inheritInChildApplications="false">
    <system.webServer>
	 <modules>
            <remove name="WebDAVModule" />
      </modules>
      <handlers>
	   <remove name="WebDAV" />
        <add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
      </handlers>
      <aspNetCore processPath="dotnet" arguments=".\Znode.Engine.Api.dll" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="inprocess" />
    </system.webServer>
  </location>
    <system.webServer>
        <security>
            <requestFiltering>
                <verbs>
                    <add verb="PUT" allowed="true" />
                    <add verb="Delete" allowed="true" />
                </verbs>
            </requestFiltering>
        </security>
    </system.webServer>
</configuration>
<!--ProjectGuid: 0626BE95-9B16-4CAB-A901-464F4D28A2E3-->