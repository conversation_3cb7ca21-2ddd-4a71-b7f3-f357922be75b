﻿using Microsoft.Extensions.Configuration;
using MongoDB.Bson;
using MongoDB.Driver;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace Znode.Libraries.ECommerce.Utilities
{
    public static class MongoLogging
    {
        private static readonly IMongoCollection<BsonDocument> _collection;
        private static readonly IConfigurationRoot _configuration;

        static MongoLogging()
        {
            try
            {
                _configuration = new ConfigurationBuilder().SetBasePath(AppContext.BaseDirectory).AddJsonFile("appsettings.json", optional: false, reloadOnChange: true).Build();
                var connectionString = _configuration.GetConnectionString(MongoLoggingConstants.ZnodeMongoDBForLog);
                var mongoUrl = new MongoUrl(connectionString);
                var databaseName = mongoUrl.DatabaseName;
                var collectionName = MongoLoggingConstants.LogMessageEntity;
                var client = new MongoClient(mongoUrl);
                var database = client.GetDatabase(databaseName);
                _collection = database.GetCollection<BsonDocument>(collectionName);
            }
            catch (Exception ex)
            {
                var errorPath = Path.Combine(AppContext.BaseDirectory, "App_Data", "mongo_error.txt");
                Directory.CreateDirectory(Path.GetDirectoryName(errorPath));
                File.WriteAllText(errorPath, ex.ToString());
            }
        }

        public static void LogMessage(Exception ex, string componentName = "", TraceLevel traceLevel = TraceLevel.Info, object obj = null)
        {
            string message = ex.Message;
            InsertLog(message, componentName, traceLevel, ex);
        }

        public static void LogMessage(string message, string componentName = "", TraceLevel traceLevel = TraceLevel.Info, object obj = null, [CallerMemberName] string methodName = "", [CallerFilePath] string fileName = "", [CallerLineNumber] int lineNumber = 0)
        {
            InsertLog(message, componentName, traceLevel);
        }

        private static void InsertLog(string message, string component, TraceLevel level, Exception ex = null)
        {
            try
            {
                var logEntry = new BsonDocument
                {
                    [MongoLoggingConstants.LogMessageId] = Guid.NewGuid().ToString(),
                    [MongoLoggingConstants.Component] = component,
                    [MongoLoggingConstants.TraceLevel] = level.ToString(),
                    [MongoLoggingConstants.LogMessage] = message,
                    [MongoLoggingConstants.CreatedDate] = DateTime.UtcNow,
                    [MongoLoggingConstants.StackTraceMessage] = ex?.ToString() ?? string.Empty

                };
                _collection.InsertOne(logEntry);
            }
            catch (Exception execption)
            {
                var fallbackPath = Path.Combine(AppContext.BaseDirectory, "App_Data", "mongo_log_error.txt");
                File.WriteAllText(fallbackPath, execption.ToString());
            }
        }
    }
}
