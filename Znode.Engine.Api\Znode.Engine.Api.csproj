﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>5fc7a95a-93ff-4fd9-b8fb-f298eeded749</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BuildBundlerMinifier" Version="3.2.449" />
    <PackageReference Include="Hangfire" Version="1.8.20" />
    <PackageReference Include="Microsoft.TypeScript.MSBuild" Version="5.8.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.3.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataExchangeLibraries\Znode.CommerceConnector.InputHandler\Znode.CommerceConnector.InputHandler.csproj" />
    <ProjectReference Include="..\DataExchangeLibraries\Znode.CommerceConnector.OutputHandler\Znode.CommerceConnector.OutputHandler.csproj" />
    <ProjectReference Include="..\Libraries\Znode.CommerceConnector.API\Znode.CommerceConnector.API.csproj" />
    <ProjectReference Include="..\Libraries\Znode.Libraries.Data\Znode.Libraries.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="App_Data\" />
  </ItemGroup>

</Project>
