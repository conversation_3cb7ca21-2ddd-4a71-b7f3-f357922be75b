﻿using Microsoft.Data.SqlClient;
using System.Data;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Data;
using Znode.Libraries.Hangfire;

namespace Znode.CommerceConnector.API.Services
{
    public class TaskSchedularService : ITaskSchedularService
    {
        private readonly ITaskSchedulerConfiguration _taskSchedulerConfiguration;
        private readonly AppDbContext _context;

        public TaskSchedularService(ITaskSchedulerConfiguration taskSchedulerConfiguration, AppDbContext context)
        {
            _taskSchedulerConfiguration = taskSchedulerConfiguration;
            _context = context;
        }

        public bool CallTaskSchedular(int erpId, dynamic inputModel)
        => _taskSchedulerConfiguration.TriggerTaskSchedular(erpId, inputModel);

        public bool EnableDisableTaskScheduler(int erpId, bool isActive)
        {
            StatusActivationModel model = new StatusActivationModel();
            bool isStatusUpdated = false;

            var entity = _context.ZnodeErpdataExchangeStatuses.FirstOrDefault(e => e.ErpbaseDefinitionId == erpId);
            model.ERPBaseDefinitionId = erpId;
            model.Status = isActive == true ? "1" : "0";
            model.ErrorNotification = entity?.ErrorNotifications;
            model.WarningNotification = entity?.WarningNotifications;
            model.InformationNotification = entity?.InformationNotifications;
            isStatusUpdated = SaveActivationDetails(model);
            return string.Equals(model.Status, "1") ? true : false;
        }

        private bool SaveActivationDetails(StatusActivationModel model)
        {
            int status = 0;
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters1 = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@IsActive", Value=model.Status},
                new SqlParameter(){ParameterName="@InformationNotifications", Value=model.InformationNotification ?? string.Empty},
                new SqlParameter(){ParameterName="@WarningNotifications", Value=model.WarningNotification ?? string.Empty},
                new SqlParameter(){ParameterName="@ErrorNotifications", Value=model.ErrorNotification ?? string.Empty},
                new SqlParameter(){ParameterName="@UserId", Value=0},
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId", Value=model.ERPBaseDefinitionId},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            DataTable response = znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_InsertUpdateERPDataExchangeStatus", sqlParameters1, 6, out status);
            return Convert.ToBoolean(status);
        }
    }
}
