﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Znode.CommerceConnector.Client.Clients;
using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.InputHandler.Clients;
using Znode.CommerceConnector.InputHandler.Handlers;
using Znode.CommerceConnector.InputHandler.IHandlers;
namespace Znode.CommerceConnector.Core
{
    public static class CustomDependencyRegistration
    {
        public static void RegisterInputHandlerDI(this WebApplicationBuilder builder)
        {
            builder.Services.AddTransient<IAPIHandler, APIHandler>();
            builder.Services.AddTransient<ISFTPHandler, SFTPHandler>();
            builder.Services.AddTransient<IFTPHandler, FTPHandler>();
            builder.Services.AddTransient<ISFTPClient, SFTPClient>();
            builder.Services.AddTransient<IFTPClient, FTPClient>();
            builder.Services.AddTransient<IAPIClient, APIClient>();
            builder.Services.AddTransient<IFTPClient, FTPClient>();
            builder.Services.AddTransient<IYAMLDataClient, YAMLDataClient>();
            builder.Services.AddTransient<IYAMLDataHandler, YAMLDataHandler>();
            builder.Services.AddTransient<IYAMLClient, YAMLClient>();
        }
    }
}
