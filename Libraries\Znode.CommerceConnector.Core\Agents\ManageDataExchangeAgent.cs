﻿using AutoMapper;

using Microsoft.AspNetCore.Mvc;

using NCrontab;

using Newtonsoft.Json;

using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.Core.Helper;
using Znode.CommerceConnector.Core.IAgents;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.ViewModels;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;
using Znode.Libraries.Resources.CommerceConnector_Resources;

using static NCrontab.CrontabSchedule;

namespace Znode.CommerceConnector.Core.Agents
{
    public class ManageDataExchangeAgent : IManageDataExchangeAgent
    {
        private readonly IManageDataExchangClient _client;
        private readonly IMapper _mapper;

        public ManageDataExchangeAgent(IManageDataExchangClient client, IMapper mapper)
        {
            _client = client;
            _mapper = mapper;
        }

        public BaseDefinitionViewModel GetDataExchangeDetailsByID(int id)
        {
            BaseDefinitionModel baseDefinition = _client.GetDataExchangeDetailsByID(id);
            if (HelperUtility.IsNull(baseDefinition)) return new BaseDefinitionViewModel();

            BaseDefinitionViewModel viewModel = new BaseDefinitionViewModel();
            viewModel = _mapper.Map<BaseDefinitionViewModel>(baseDefinition);
            viewModel.StatusActivationViewModel = _mapper.Map<StatusActivationViewModel>(baseDefinition.StatusActivationModel);
            viewModel.SchedulerConfigurationViewModel = _mapper.Map<SchedulerConfigurationViewModel>(baseDefinition.SchedulerSettingModel);           
            if (HelperUtility.IsNotNull(baseDefinition.SchedulerSettingModel.StartDate))
                viewModel.SchedulerConfigurationViewModel.StartTime = GetSeparatedTime(Convert.ToDateTime(baseDefinition.SchedulerSettingModel.StartDate));
            return viewModel;
        }


        public bool SaveDataExchangeDetails(BaseDefinitionViewModel viewModel)
        {
            BaseDefinitionModel model = new BaseDefinitionModel();
            int configurationId = viewModel.TransmissionConfigurations.TransmissionConfigurationId ?? 0;

            model = _mapper.Map<BaseDefinitionModel>(viewModel);
            model.SchedulerSettingModel = _mapper.Map<SchedulerConfigurationModel>(viewModel.SchedulerConfigurationViewModel);
            model.StatusActivationModel = _mapper.Map<StatusActivationModel>(viewModel.StatusActivationViewModel);

            if (HelperUtility.IsNotNull(viewModel.SchedulerConfigurationViewModel?.StartDate) && HelperUtility.IsNotNull(viewModel.SchedulerConfigurationViewModel?.StartTime))
                model.SchedulerSettingModel.StartDate = viewModel.SchedulerConfigurationViewModel.SchedulerFrequency == CCAdminConstant.Recurring ? null : (DateTime?)GetDateTime(Convert.ToDateTime(viewModel.SchedulerConfigurationViewModel.StartDate), Convert.ToDateTime(viewModel.SchedulerConfigurationViewModel.StartTime));
            model.SchedulerSettingModel.ERPBaseDefinitionId = viewModel.ERPBaseDefinitionId;
            model.TransmissionConfigurations.ERPBaseDefinitionId = viewModel.ERPBaseDefinitionId;
            model.StatusActivationModel.ERPBaseDefinitionId = viewModel.ERPBaseDefinitionId;
            model.TransmissionConfigurations.HttpsConfig.TransmissionConfigurationId = configurationId;
            model.TransmissionConfigurations.HttpsConfigDestination.TransmissionConfigurationId = configurationId;
            model.TransmissionConfigurations.HttpsConfig.APIConfigurationId = viewModel.TransmissionConfigurations.HttpsConfig.APIConfigurationId;
            model.TransmissionConfigurations.HttpsConfigDestination.APIConfigurationId = viewModel.TransmissionConfigurations.HttpsConfigDestination.APIConfigurationId;
            model.TransmissionConfigurations.SFTPConfig.TransmissionConfigurationId = configurationId;
            model.TransmissionConfigurations.SftpConfigDestination.TransmissionConfigurationId = configurationId;

            model.Library = viewModel.DataExchangeLibraryId == 0 ? CCAdminConstant.LibraryCustom : CCAdminConstant.LibraryNative;
            if (model.SchedulerSettingModel.SchedulerType.Equals("RealTime"))
            {
                model.TransmissionConfigurations.DestinationTransmissionMode = string.Empty;
            }
            return _client.SaveDataExchangeDetails(model);
        }

        /// <summary>
        /// Check validation for Cron Expression and StartDate and StartTime.
        /// </summary>
        /// <param name="schedulerModel">schedulerModel</param>
        /// <param name="status">status to return</param>
        /// <returns>SchedulerConfigurationViewModel</returns>
        public virtual SchedulerConfigurationViewModel CheckValidation(SchedulerConfigurationViewModel schedulerModel, out bool status)
        {
            if (schedulerModel.SchedulerFrequency == CCAdminConstant.OneTime && schedulerModel.SchedulerType != CCAdminConstant.OnDemand)
            {
                return ValidateOneTimeSchedulerModel(schedulerModel, out status);
            }
            else if (schedulerModel.SchedulerFrequency == CCAdminConstant.Recurring && schedulerModel.SchedulerType != CCAdminConstant.OnDemand)
            {
                return ValidateRecurringSchedulerModel(schedulerModel, out status);
            }
            else
            {
                status = true;
                return schedulerModel;
            }
        }

        public virtual SchedulerConfigurationViewModel CheckSchedulerNameValidation(SchedulerConfigurationViewModel schedulerModel, out bool status)
        {
            if (schedulerModel.ERPBaseDefinitionSchedulerId <= 0 && SchedulerNameExists(schedulerModel.SchedulerName))
            {
                status = false;
                return (SchedulerConfigurationViewModel)GetViewModelWithErrorMessage(schedulerModel, CommerceConnector_Resources.SchedulerNameExists);
            }
            else
            {
                status = true;
                return schedulerModel;
            }
        }

        // Validate Cron Expression and return error message.
        protected virtual SchedulerConfigurationViewModel ValidateRecurringSchedulerModel(SchedulerConfigurationViewModel model, out bool status)
        {
            status = false;

            if (ValidateCronExpression(model.CronExpression) == false)
                return (SchedulerConfigurationViewModel)GetViewModelWithErrorMessage(model, CommerceConnector_Resources.InvalidCronExpressionError);
            else
            {
                status = true;
                return model;
            }
        }

        // Validate Cron Expression.
        protected virtual bool ValidateCronExpression(string cronExpression)
        {
            CrontabSchedule cronSchedule = TryParse(cronExpression, new ParseOptions
            {
                IncludingSeconds = false
            });

            return HelperUtility.IsNotNull(cronSchedule);
        }

        // Validate StartDate and StartTime with current local time.
        protected virtual SchedulerConfigurationViewModel ValidateOneTimeSchedulerModel(SchedulerConfigurationViewModel model, out bool status)
        {
            status = false;
            if (model.StartDate?.Date < DateTime.UtcNow.Date)
                return (SchedulerConfigurationViewModel)GetViewModelWithErrorMessage(model, CommerceConnector_Resources.ErrorStartDateGreaterThan);
            else if (model.StartDate?.Date == DateTime.UtcNow.Date && Convert.ToDateTime(model.StartTime).TimeOfDay < DateTime.UtcNow.TimeOfDay)
                return (SchedulerConfigurationViewModel)GetViewModelWithErrorMessage(model, CommerceConnector_Resources.ErrorStartTimeEarlierThanNow);
            else
            {
                status = true;
                return model;
            }
        }

        // Returns true if scheduler name exists.
        protected virtual bool SchedulerNameExists(string schedulerName)
        {
            if (!string.IsNullOrEmpty(schedulerName))
            {
                return _client.ValidateSchedulerName(schedulerName);             
            }
            return false;
        }

        /// <summary>
        /// Get BaseViewModel with HasError and ErrorMessage set.
        /// </summary>
        /// <param name="viewModel">View model to set.</param>
        /// <param name="errorMessage">Error message to set.</param>
        /// <returns>Returns BaseViewModel with HasError and ErrorMessage set.</returns>
        protected BaseViewModel GetViewModelWithErrorMessage(BaseViewModel viewModel, string errorMessage)
        {
            viewModel.HasError = true;
            viewModel.ErrorMessage = errorMessage;
            return viewModel;
        }

        public BaseDefinitionViewModel GetBaseDefinitionDetailsByID(int erpId)
        {
            BaseDefinitionViewModel model = new BaseDefinitionViewModel();
            BaseDefinitionModel baseDefinition = _client.GetBaseDefinitionDetailsByID(erpId);
            model = _mapper.Map<BaseDefinitionViewModel>(baseDefinition);
            model.StatusActivationViewModel.Status = Convert.ToBoolean(baseDefinition.StatusActivationModel.Status);
            return model;
        }

        public bool TestConnection(string mode, string request) => _client.TestConnection(mode, request);

        //Return Concatenated Date and time
        private DateTime GetDateTime(DateTime date, DateTime time) => date.Date + time.TimeOfDay;

        //Return separate Time
        private string GetSeparatedTime(DateTime dateTime) => (Convert.ToString(dateTime.TimeOfDay));

        public ConfigurationChangeLogListViewModel GetConfigurationChangeList(GridModel gridModel, int id)
        {
            FilterCollection filter = new FilterCollection();
            Dictionary<string, string> sorts = new();
            if (!string.IsNullOrWhiteSpace(gridModel?.GlobalSearch))
            {
                string searchableFields = string.Join("|", new[]
                {
                    CommerceConnectorConstants.FieldName,
                    CommerceConnectorConstants.OldValue,
                    CommerceConnectorConstants.NewValue,
                    CommerceConnectorConstants.UserName
                });

                filter.Add(new FilterTuple(searchableFields, ProcedureFilterOperators.Contains, gridModel.GlobalSearch));
            }

            ConfigurationChangeLogListModel list = _client.GetConfigurationChangeList(id,filter, sorts, gridModel?.Page, gridModel?.PageSize);
            ConfigurationChangeLogListViewModel listViewModel = new ConfigurationChangeLogListViewModel
            {
                ConfigurationChangeLogList = _mapper.Map<List<ConfigurationChangeLogViewModel>>(list?.ConfigurationChangeLogList),
                TotalRows = list.TotalRows
            };
            return listViewModel;
        }
    }
}
