﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class SftpTransmissionConfigurationViewMOdel
    {
        public int? SFTPConfigurationId { get; set; }

        [Display(Name = "Server Address")]
        public string? ServerAddress { get; set; }

        [Display(Name = "Folder Path")]
        public string? FolderPath { get; set; }

        [Display(Name = "File Name")]
        public string? Filename { get; set; }

        public string? Username { get; set; }
        public string? Password { get; set; }

        [Display(Name = "Action After Retrieval")]
        public string? ActionAfterRetrieval { get; set; }
        public int? ConfigurationId { get; set; }

        [Display(Name = "Port Number")]
        public int? PortNumber { get; set; }
        public string? Format { get; set; }
    }
}
