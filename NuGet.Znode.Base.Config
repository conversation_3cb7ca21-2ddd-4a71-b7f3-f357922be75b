﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="nuget.znode.com" value="https://nuget.znode.com/nuget" />
		<add key="dev.source" value="https://nuget.pkg.github.com/mrrsoft/index.json" />

  </packageSources>
  <packageSourceCredentials>
    <nuget.znode.com>
      <add key="Username" value="<EMAIL>" />
      <add key="ClearTextPassword" value="Mrr@123" />
    </nuget.znode.com>
		<dev.source>
			<add key="Username" value="sachin-manore-amla" />
			<add key="ClearTextPassword" value="****************************************" />
		</dev.source>
  </packageSourceCredentials>
</configuration>
