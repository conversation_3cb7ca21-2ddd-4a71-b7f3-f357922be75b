﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeErpbaseDefinitionScheduler
{
    public int ErpbaseDefinitionSchedulerId { get; set; }

    public int? ErpbaseDefinitionId { get; set; }

    public string? SchedulerType { get; set; }

    public string? SchedulerName { get; set; }

    public string? SchedulerFrequency { get; set; }

    public DateTime? StartDate { get; set; }

    public string? CronExpression { get; set; }

    public string? ProcessorFileName { get; set; }

    public DateTime? LastRunTime { get; set; }

    public string? HangfireJobId { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeErpbaseDefinition? ErpbaseDefinition { get; set; }
}
