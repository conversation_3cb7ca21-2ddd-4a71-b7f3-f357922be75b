﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.CommerceConnector.Core.ViewModels
@using Znode.Libraries.Resources.CommerceConnector_Resources

<div class="manage-data-exchange">

    @Html.HiddenFor(model => model.ERPBaseDefinitionId)
    @Html.HiddenFor(model => model.TransmissionConfigurations.TransmissionConfigurationId)

    @Html.Partial("_BaseDefinitionDetails", Model)

    <div class="accordion mt-4" id="schedulerConfiguration">
        <div class="accordion-item">
            <div class="accordion-header m-0" id="headingConfiguration">
                <button class="accordion-button" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseConfiguration"
                        aria-expanded="false"
                        aria-controls="collapseConfiguration"
                        data-test-selector="btnScheduleConfiguration">
                    @CommerceConnector_Resources.LabelScheduleConfiguration
                </button>
            </div>
            <div id="collapseConfiguration"
                 class="accordion-collapse collapse show"
                 aria-labelledby="headingConfiguration"
                 data-bs-parent="#schedulerConfiguration">
                <div class="accordion-body">
                    @Html.Partial("_SchedulerConfiguration", Model)
                </div>
            </div>
        </div>
    </div>

    <div class="accordion mt-4" id="accordionTransmission">
        <div class="accordion-item">
            <div class="accordion-header m-0" id="headingTransmission">
                <button class="accordion-button collapsed" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseTransmission"
                        aria-expanded="false"
                        aria-controls="collapseTransmission" data-test-selector="btnTransmissionSource" aria-label="Base Definition Transmission Source">
                    @CommerceConnector_Resources.TransmissionSource
                </button>
            </div>
            <div id="collapseTransmission"
                 class="accordion-collapse collapse mt-4"
                 aria-labelledby="headingTransmission"
                 data-bs-parent="#accordionTransmission" aria-label="Transmission Source Details">
                <div class="accordion-body">
                    <label asp-for="TransmissionConfigurations.TransmissionMode" class="ps-2 pb-1" data-test-selector="lblTransmissionMode" aria-label="Transmission Configurations Mode"></label><span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span>
                    <div class="row">
                        <div class="col-md-12 col-lg-6">
                            <select asp-for="TransmissionConfigurations.TransmissionMode" asp-items="Model.TransmissionConfigurations.TransmissionModeOptions" class="form-select" id="transmissionSourceDropdown" aria-label="Source Transmission Mode" onchange="ManageDataExchange.prototype.handleTransmissionSourceChange(this)" data-test-selector="drpTransmissionModeOptions"></select>
                            <span asp-validation-for="TransmissionConfigurations.TransmissionMode" id="TransmissionSourceError" class="text-danger" data-test-selector="spnTransmissionMode" aria-label="Transmission Configurations Mode"></span>
                        </div>
                        <div id="DivSourceTestConnection" class="col-md-12 col-lg-6">
                            <button type="button" id="sourceTestConnectionBtn" class="btn-text btn-text-secondary me-2" data-test-selector="btnTestConnection" aria-label="Test Connection Manage Data Exchange" onclick="ManageDataExchange.prototype.ValidateAndTestConnection('Source', this)">@CommerceConnector_Resources.LabelTestConnectivity</button>
                        </div>
                    </div>
                    <div class="py-3 d-none" id="APIHandler_Source" aria-label="HTTP Transmission Configuration Source" data-test-selector="divHTTPTransmissionConfig">
                        @Html.Partial("_HTTPTransmissionConfig", Model)
                    </div>
                    <div class="py-3 d-none" id="SFTPHandler_Source" aria-label="SFTP Transmission Configuration Source" data-test-selector="divSFTPTransmissionConfig">
                        @Html.Partial("_SFTPTransmissionConfig", Model)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion mt-4" id="accordionTransmissionDestination">
        <div class="accordion-item">
            <div class="accordion-header m-0" id="headingTransmissionDestination">
                <button class="accordion-button collapsed" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseTransmissionDestination"
                        aria-expanded="false"
                        aria-controls="collapseTransmissionDestination" data-test-selector="btnTransmissionDestination" aria-label="Base Definition Transmission Destination">
                    @CommerceConnector_Resources.TransmissionDestination
                </button>
            </div>
            <div id="collapseTransmissionDestination"
                 class="accordion-collapse collapse mt-4"
                 aria-labelledby="headingTransmissionDestination"
                 data-bs-parent="#accordionTransmissionDestination" aria-label="Transmission Destination Details">
                <div class="accordion-body">
                    <label asp-for="TransmissionConfigurations.TransmissionModeDestination" class="ps-2 pb-1" data-test-selector="lblTransmissionModeDestination" aria-label="Transmission Destination Mode"></label><span class="text-danger required" data-test-selector="spnRequired" aria-label="Required"></span>
                    <div class="row">
                        <div class="col-md-12 col-lg-6">
                            <select asp-for="TransmissionConfigurations.TransmissionModeDestination" asp-items="Model.TransmissionConfigurations.TransmissionModeOptions" aria-label="Destination Transmission Mode" class="form-select" id="transmissionDestinationDropdown" onchange="ManageDataExchange.prototype.handleTransmissionSourceChange(this)" data-test-selector="drpTransmissionModeOptions"></select>
                            <span asp-validation-for="TransmissionConfigurations.TransmissionModeDestination" class="text-danger" id="TransmissionDestinationError" data-test-selector="spnTransmissionModeDestination" aria-label="Destination Transmission Mode"></span>
                        </div>
                        <div id="DivDestinationTestConnection" class="col-md-12 col-lg-6">
                            <button type="button" id="destTestConnectionBtn" class="btn-text btn-text-secondary me-2" data-test-selector="btnTestConnection" aria-label="Test Connection Manage Data Exchange" onclick="ManageDataExchange.prototype.ValidateAndTestConnection('Dest', this)">@CommerceConnector_Resources.LabelTestConnectivity</button>
                        </div>
                    </div>
                    <div class="py-3 d-none" id="APIHandler_Destination" aria-label="HTTP Transmission Configuration Destination Source" data-test-selector="divHTTPTransmissionConfigDestination">
                        @Html.Partial("_HTTPTransmissionConfigDestination", Model)
                    </div>
                    <div class="py-3 d-none" id="SFTPHandler_Destination" aria-label="SFTP Transmission Configuration Destination Source" data-test-selector="divSFTPTransmissionConfigDestination">
                        @Html.Partial("_SFTPTransmissionConfigDestination", Model)
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="accordion mt-4 d-none" id="accordionStatusActivation">
        <div class="accordion-item">
            <div class="accordion-header m-0" id="headingStatusActivation" data-test-selector="hdgStatusActivation">
                <button class="accordion-button collapsed" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseStatusActivation"
                        aria-expanded="false"
                        aria-controls="collapseStatusActivation" data-test-selector="btnStatusActivation" aria-label="Manage Status Activation">
                    @CommerceConnector_Resources.StatusActivation
                </button>
            </div>
            <div id="collapseStatusActivation"
                 class="accordion-collapse collapse"
                 aria-labelledby="headingStatusActivation"
                 data-bs-parent="#accordionStatusActivation">
                <div class="accordion-body">
                    <div class="py-3" id="StatusActivation" data-test-selector="divManageActivation">
                        @Html.Partial("ManageActivation", Model)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accordion mt-4" id="accordionMapping">
        <div class="accordion-item">
            <div class="accordion-header m-0" id="headingMapping">
                <button class="accordion-button" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseMapping"
                        aria-expanded="true"
                        aria-controls="collapseMapping" data-test-selector="btnMapping" aria-label="Mapping Editor">
                    Mapping
                </button>
            </div>
            <div id="collapseMapping"
                 class="accordion-collapse collapse show"
                 aria-labelledby="headingMapping"
                 data-bs-parent="#accordionMapping" aria-label="Mapping Editor">
                <div class="accordion-body">
                    <div class="py-3 px-2 mapping" id="Mapping" data-test-selector="divMapping">
                        @Html.Partial("YAMLEditor")
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<div id="noDataMessageForErpExchangeForm" class="text-center d-none">
    <h2>@CommerceConnector_Resources.NoRecordsFound</h2>
</div>
<input type="hidden" id="ConfigurationChangeJsonData" name="ConfigurationChangeJsonData" />