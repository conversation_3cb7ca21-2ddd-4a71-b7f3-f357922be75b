﻿declare var agGrid: any;

class ProcessingLog {
    private logsGridInitialized = false;
    private detailsGridInitialized = false;
    private defaultZoom = window.devicePixelRatio;

    private logsGridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0 // disables AG Grid's internal sorting
        },
        rowData: [],
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,

        onGridReady: (params: any) => {
            this.logsGridOptions.api = params.api;
            this.logsGridOptions.columnApi = params.columnApi;
            const handleResize = () => {
                if (this.logsGridOptions?.api) {
                    this.logsGridOptions.api.sizeColumnsToFit();
                }
            };

            $(window).on("resize", handleResize);
            setTimeout(() => {
                if (this.logsGridOptions?.api) {
                    this.logsGridOptions.api.sizeColumnsToFit();
                }
            }, 100);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const activeSort = sortState.find((c: any) => c.sort);
                DynamicGrid.prototype.setSorting(activeSort?.colId || null, activeSort?.sort || null);
                this.LoadProcessingLogsGrid(1);
            });
        }
    };

    private detailsGridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0
        },
        rowData: [],
        context: {},
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,
        rowStyle: {
            borderBottom: '1px solid #e0e0e0'
        },

        onGridReady: (params: any) => {
            this.detailsGridOptions.api = params.api;
            this.detailsGridOptions.columnApi = params.columnApi;
            // Window resize or zoom handler
            const handleResizeOrZoom = () => {
                const currentZoom = window.devicePixelRatio;

                // If zoom level changed, re-auto-size
                if (currentZoom >= this.defaultZoom) {
                    const allColumnIds = params.columnApi.getAllColumns().map(col => col.getColId());
                    params.columnApi.autoSizeColumns(allColumnIds, false);
                } else if (currentZoom < this.defaultZoom) {
                    // Only resized, so fit to width
                    params.api.sizeColumnsToFit();
                }
            };

            $(window).on("resize", handleResizeOrZoom);
            setTimeout(() => {
                const allColumnIds = params.columnApi.getAllColumns().map(col => col.getColId());
                params.columnApi.autoSizeColumns(allColumnIds);
            }, 100);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const activeSort = sortState.find((c: any) => c.sort);
                const logId = params.context.logId;
                DynamicGrid.prototype.setSorting(activeSort?.colId || null, activeSort?.sort || null);
                this.ShowLogDetails(1, logId);
            });
        }
    };

    LoadProcessingLogsGrid(page: number) {
        ZnodeGlobal.prototype.ShowLoader();
        var exchangeName = $('#hdnExchangeName').val();
        var erpBaseDefinitionId = $('#hdnERPBaseDefinitionId').val();
        const request = {
            page: page,
            pageSize: Number($('#pageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#globalSearch").val()
        };

        $.ajax({
            url: '/commerce-connector/DataExchange/RenderLogsGrid',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                gridModel: request,
                exchangeName: exchangeName,
                eRPBaseDefinitionId: erpBaseDefinitionId,
                isFromImportLogs: false
            }),
            success: (res) => {
                const rowCount = res?.rows?.length || 0;

                if (rowCount === 0) {
                    if (this.logsGridInitialized) {
                        this.logsGridOptions.api.setRowData([]);
                    }

                    DataExchange.prototype.showNoRecordsMsg("processingLogsGrid");
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    DataExchange.prototype.showRecords("processingLogsGrid");
                }
                DynamicGrid.prototype.setTotalRows(res.totalCount);

                if (!this.logsGridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#processingLogsGrid');
                    new agGrid.Grid(gridDiv!, this.logsGridOptions);
                    this.logsGridInitialized = true;
                }
                if (!this.logsGridOptions.columnDefs?.length && res.columns) {
                    const columnsToSkipTooltips = ['importProcessLogId', 'importTemplateId'];
                    const dateFields = ['processStartedDate', 'processCompletedDate'];
                    const dynamicCols = res.columns.map(c => {
                        const field = c.field as string;
                        const isTemplateName = c.field === "templateName";
                        const column: any = {
                            field: c.field,
                            headerName: isTemplateName ? "Exchange Name" : c.title,
                            sortable: c.sortable === true,
                            filter: false,
                            hide: ["successRecordCount", "failedRecordcount", "totalProcessedRecords", "createdBy", "createdDate", "modifiedBy", "modifiedDate", "errorMessage", "hasError", "importName", "importTemplateId"].indexOf(field) !== -1,
                            headerTooltip: c.title,
                            resizable: true
                        };
                        if (['importProcessLogId'].indexOf(field) > -1) {
                            column.width = 110;
                            column.maxWidth = 130;
                        }
                        else if (['importTemplateId'].indexOf(field) > -1) {
                            column.width = 130;
                            column.maxWidth = 160;
                        }
                        if (dateFields.indexOf(field) > -1) {
                            column.valueFormatter = (params) => ZnodeGlobal.prototype.ConvertDateTimeToLocal(params.value);
                        }
                        if (columnsToSkipTooltips.indexOf(field) === -1) {
                            if (!(dateFields.indexOf(field) === -1)) {
                                column.tooltipValueGetter = (params) => ZnodeGlobal.prototype.ConvertDateTimeToLocal(params.value);
                            }
                            else {
                                column.tooltipField = field;
                            }
                        }
                        return column;
                    });
                    const actionCol = {
                        headerName: "Actions",
                        width: 120,
                        sortable: false,

                        cellRenderer: function (params) {
                            const id = params.data?.importProcessLogId;
                            return `
                                <a href="#" id="viewLogDetails" data-action="view" data-id="${id}" title="View" data-bs-toggle="modal" data-bs-target="#processingLogDetailsModal"><i class="z-view"></i></a>
                            `;
                        }
                    };

                    this.logsGridOptions.api.setColumnDefs([...dynamicCols, actionCol]);
                }

                this.logsGridOptions.api.setRowData(res.rows);
                setTimeout(() => {
                    this.logsGridOptions.api.sizeColumnsToFit();
                }, 100);

                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, '', (page) => {
                    this.LoadProcessingLogsGrid(page);
                });
                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount);
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    ShowLogDetails(page: number, logId: number = 0) {
        ZnodeGlobal.prototype.ShowLoader();
        $('#processLogId').val(logId);

        const request = {
            page: page,
            pageSize: Number($('#logPageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#logsGlobalSearch").val()
        };

        $.ajax({
            url: '/commerce-connector/DataExchange/ShowProcessingLogDetails',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                request: request,
                processLogId: logId,
                isFromImportLogs: false
            }),
            success: (res) => {
                const rowCount = res?.rows?.length || 0;
                const target = "#target-log-details-to-display";

                $('#totalRecordsField').html(res.totalProcessedRecords);
                $('#succeededRecordsField').html(res.successRecordCount);
                $('#failedRecordsField').html(res.failedRecordcount);

                this.detailsGridOptions.context = { logId: logId };

                if (rowCount === 0) {
                    if (this.detailsGridInitialized) {
                        this.detailsGridOptions.api.setRowData([]);
                    }

                    $("#processingLogDetailsGrid").hide();
                    $("#logsPaginationControls").hide();
                    $("#logsShowPerPage").hide();
                    $("#logsShowPerCount").hide();
                    $("#logsCustomRowInfo").hide();
                    $("#logsNoRecordsMessage").show();
                    $("#processingLogDetailsContent").show();
                    $("body").css("overflow", "hidden");
                    $(target).append(res);
                    $(target).fadeIn("fast");
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    $("#logsNoRecordsMessage").hide();
                    $("#logsShowPerPage").show();
                    $("#logsShowPerCount").show();
                    $("#logsCustomRowInfo").show();
                    $("#processingLogDetailsGrid").show();
                    $("#logsPaginationControls").show();
                }

                DynamicGrid.prototype.setTotalRows(res.totalCount);

                $('#processingLogDetailsGrid').addClass('ag-grid-hidden');

                if (!this.detailsGridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#processingLogDetailsGrid');
                    new agGrid.Grid(gridDiv!, this.detailsGridOptions);
                    this.detailsGridInitialized = true;
                }

                let dynamicCols = [];
                if (!this.detailsGridOptions.columnDefs?.length && res.columns) {
                    dynamicCols = res.columns.map(c => ({
                        field: c.field,
                        headerName: c.title,
                        sortable: c.sortable === true,
                        filter: false,
                        hide: ["successRecordCount", "failedRecordcount", "totalProcessedRecords"].indexOf(c.field) !== -1,
                    }));
                }

                $("#processingLogDetailsContent").show();
                $("body").css("overflow", "hidden");
                $(target).append(res);
                $(target).fadeIn("fast");

                setTimeout(() => {
                    const gridDiv = document.querySelector<HTMLElement>('#processingLogDetailsGrid');

                    if (this.detailsGridInitialized && this.detailsGridOptions.api) {
                        this.detailsGridOptions.api.destroy();
                        this.detailsGridInitialized = false;
                    }

                    gridDiv.innerHTML = '';

                    new agGrid.Grid(gridDiv!, this.detailsGridOptions);

                    if (dynamicCols.length) {
                        this.detailsGridOptions.api.setColumnDefs(dynamicCols);
                    }

                    this.detailsGridOptions.api.setRowData(res.rows);
                    this.detailsGridOptions.api.setDomLayout('normal');
                    this.detailsGridOptions.api.sizeColumnsToFit();

                    $('#processingLogDetailsGrid').removeClass('ag-grid-hidden');
                    this.detailsGridInitialized = true;
                }, 350);

                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, "logsPaginationControls", (page) => {
                    this.ShowLogDetails(page, logId);
                });

                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount, "logsCustomRowInfo");

                $("#processingLogDetailsContent").show();
                $("body").css("overflow", "hidden");
                $(target).append(res);
                $(target).fadeIn("fast");
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    ShowLogDetailsPopup(logId) {
        const target = "#target-log-details-to-display";
        $(target).hide();
        $('#processingLogDetailsContent').hide();
        this.ShowLogDetails(1, logId);
    }
}

$(document).ready(function () {
    const instance = new ProcessingLog();
    $("#processingLogsGrid").hide();
    $("#noRecordsMessage").hide();
    if ($("#processingLogsGrid").length > 0) {
        instance.LoadProcessingLogsGrid(1);
        $('#pageSizeSelect').on('change', () => {
            this.currentPageSize = + $('#pageSizeSelect').val();
            instance.LoadProcessingLogsGrid(1);
        });

        $('#globalSearchbtn').on('click', () => {
            instance.LoadProcessingLogsGrid(1)
        });
        $('#globalSearch').keydown(function (e) {
            if (e.keyCode === 13) {
                instance.LoadProcessingLogsGrid(1);
            }
        });
    }

    $('#logPageSizeSelect').on('change', () => {
        var logId = parseInt($('#processLogId').val().toString());
        this.currentPageSize = + $('#logPageSizeSelect').val();
        instance.ShowLogDetails(1, logId);
    });

    $('#logsGlobalSearchbtn').on('click', () => {
        var logId = parseInt($('#processLogId').val().toString());
        instance.ShowLogDetails(1, logId)
    });
    $('#logsGlobalSearch').keydown(function (e) {
        var logId = parseInt($('#processLogId').val().toString());
        if (e.keyCode === 13) {
            instance.ShowLogDetails(1, logId);
        }
    });

    $(document).on('click', '#viewLogDetails', function (e) {
        debugger;
        e.preventDefault();
        const id = $(this).data('id');
        instance.ShowLogDetailsPopup(id);
    });

    $('#processingLogDetailsModal').on('hidden.bs.modal', function () {
        var page = parseInt($('#paginationControls').find('input').val());
        var pageCount = parseInt($('#paginationControls').find('.LowerCase').html().split('/')[1]);
        DynamicGrid.prototype.RenderPagination(page, pageCount, '', (page) => {
            instance.LoadProcessingLogsGrid(page);
        });
    });
});
