﻿using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Diagnostics;
using System.Net;
using System.Text;
using System.Xml;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class APIHandler : IAPIHandler
    {
        IHandlerDataHelper _handlerDataHelper;
        IAPIClient _apiClient;

        public APIHandler(IHandlerDataHelper handlerDataHelper, IAPIClient apiClient)
        {
            _handlerDataHelper = handlerDataHelper;
            _apiClient = apiClient;
        }

        public dynamic APIDataHandler(int erpId, int logId, dynamic requestBody, WebHeaderCollection headerCollection, dynamic inputModel)
        {
            try
            {
                MongoLogging.LogMessage(Constants.APIDataHandlerMethodCalledInInputHand<PERSON>, Constants.<PERSON><PERSON><PERSON>H<PERSON><PERSON>, TraceLevel.Info);
                dynamic inputHandlerData = "";
                dynamic file = new byte[0];
                string format = _handlerDataHelper.GetAPIFormat(erpId, Constants.Source);

                HTTPConfigurationModel apiConfigurationModel = GetInputHandlerHTTPCredentials(erpId);

                file = _apiClient.GetData<dynamic>(erpId, logId, apiConfigurationModel, requestBody, headerCollection, inputModel, false);

                if (HelperUtility.IsNotNull(format) && HelperUtility.IsNotNull(file))
                {
                    if (format == "JSON")
                    {
                        MongoLogging.LogMessage(Constants.JSONFormat, Constants.APIDataHandler, TraceLevel.Info);
                        //returning the json file as it is without creating temptable
                        inputHandlerData = file;
                    }
                    else if (format == "CSV")
                    {
                        MongoLogging.LogMessage(Constants.CSVFormat, Constants.APIDataHandler, TraceLevel.Info);
                        inputHandlerData = ParseCSVData(file, erpId);
                    }
                    else if (format == "XML")
                    {
                        MongoLogging.LogMessage(Constants.XMLFormat, Constants.APIDataHandler, TraceLevel.Info);
                        XmlDocument doc = new XmlDocument();
                        string xml = Encoding.UTF8.GetString(file);
                        doc.LoadXml(xml);
                        DataSet dataSet = new DataSet();
                        using (StringReader stringReader = new StringReader(xml))
                        {
                            dataSet.ReadXml(stringReader);
                        }
                        inputHandlerData = _handlerDataHelper.CreateTempTableWithData(dataSet).FirstOrDefault();
                    }
                    MongoLogging.LogMessage(Constants.TemptableGeneratedInInputHandler + inputHandlerData, Constants.APIDataHandler, TraceLevel.Info);
                    return inputHandlerData;
                }
                MongoLogging.LogMessage(Constants.FileNotFound + inputHandlerData, Constants.APIDataHandler, TraceLevel.Info);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.DownloadedFileNotFound + inputHandlerData);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.APIDataHandler, TraceLevel.Error);
                return inputHandlerData;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.APIDataHandlerMethodFailedInInputHandler + ex.Message, Constants.APIDataHandler, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.APIDataHandlerMethodFailedInInputHandler + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.APIDataHandler, TraceLevel.Error);
                return string.Empty;
            }
        }

        public HTTPConfigurationModel GetInputHandlerHTTPCredentials(int erpId)
        {
            List<HTTPConfigurationModel> dataModelList;
            try
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsCalledInAPIInputHandler, Constants.APIDataHandler, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPITransmissionDetails", sqlParameters);
                dataModelList = znodeViewRepository.ConvertDataTableToList<HTTPConfigurationModel>(response);
                return dataModelList?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsFailedInAPIInputHandler + ex.Message, Constants.APIDataHandler, TraceLevel.Error);
                return new HTTPConfigurationModel();
            }
        }

        public string ConvertJsonToDataTable(string json)
        {

            var jObject = JObject.Parse(json);
            Dictionary<string, object> dict = jObject.ToObject<Dictionary<string, object>>();

            JArray array;

            if (jObject["value"] is JArray valueArray)
            {
                array = valueArray;
            }
            else
            {
                var valueProps = jObject.Properties()
                    .Where(p => p.Name.StartsWith("value["))
                    .OrderBy(p => p.Name)
                    .Select(p => p.Value)
                    .ToArray();

                array = new JArray(valueProps);
            }

            DataTable dataTable = JsonConvert.DeserializeObject<DataTable>(array.ToString());
            DataSet csvDataSet = new DataSet();
            csvDataSet.Tables.Add(dataTable);
            List<string> tempTableName = _handlerDataHelper.CreateTempTableWithData(csvDataSet);
            return tempTableName.FirstOrDefault();
        }

        public string ParseCSVData(byte[] file, int erpId)
        {
            using (var memoryStream = new MemoryStream(file))
            using (var reader = new StreamReader(memoryStream))
            {
                DataTable dataTable = LoadCsvFromStream(reader);
                DataSet csvDataSet = new DataSet();
                csvDataSet.Tables.Add(dataTable);

                List<string> tempTableName = _handlerDataHelper.CreateTempTableWithData(csvDataSet);
                return tempTableName?.FirstOrDefault();
            }
        }

        private DataTable LoadCsvFromStream(TextReader reader)
        {
            DataTable dt = new DataTable();
            bool headersRead = false;

            string? line;
            while ((line = reader.ReadLine()) != null)
            {
                string[] values = line.Split(',');

                if (!headersRead)
                {
                    foreach (var header in values)
                        dt.Columns.Add(header.Trim());

                    headersRead = true;
                }
                else
                {
                    dt.Rows.Add(values);
                }
            }

            return dt;
        }
    }
}
