﻿using Renci.SshNet;
using Renci.SshNet.Sftp;
using System.Diagnostics;
using System.Net;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class SFTPClient : ISFTPClient
    {
        public byte[] DownloadData<T>(int erpId, int logId, SFTPConfigurationModel sFTPConfigurationModel, bool returnWithoutDeserialize = false)
        {
            string host = sFTPConfigurationModel?.ServerAddress?.Trim();
            string username = sFTPConfigurationModel?.UserName?.Trim();
            string password = sFTPConfigurationModel?.Password?.Trim();
            string remoteFolder = sFTPConfigurationModel?.FolderPath?.Trim();
            string fileName =  sFTPConfigurationModel?.FileName?.Trim();
            ServicePointManager.ServerCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            try
            {
                using (var sftp = new SftpClient(host, sFTPConfigurationModel.PortNumber, username, password))
                {
                    sftp.Connect();
                    MongoLogging.LogMessage(Constants.ConnectedToSFTPServer, Constants.DownloadData, TraceLevel.Info);

                    IEnumerable<ISftpFile> sftpFiles = sftp.ListDirectory(remoteFolder)?.Where(x => x.Name.Equals(fileName, StringComparison.OrdinalIgnoreCase)).ToList();
                    if (HelperUtility.IsNotNull(sftpFiles))
                    {
                        foreach (SftpFile file in sftpFiles)
                        {
                            using (MemoryStream ms = new MemoryStream())
                            {
                                sftp.DownloadFile(file.FullName, ms);
                                sftp.Disconnect();
                                return ms.ToArray();
                            }
                        }
                    }
                    MongoLogging.LogMessage(Constants.SpecifiedFileNotFoundOnServer + fileName, Constants.DownloadData, TraceLevel.Info);
                    bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.SpecifiedFileNotFoundOnServer + fileName);
                    if (!logResponse)
                        MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.DownloadData, TraceLevel.Error);
                    return new byte[0];
                }
                MongoLogging.LogMessage(Constants.FailedToConnectToSFTPServer, Constants.DownloadData, TraceLevel.Info);
                bool failedLogResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.FailedToConnectToSFTPServer);
                if (!failedLogResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.DownloadData, TraceLevel.Error);
                return new byte[0];
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.DownloadFailed + ex.Message, Constants.DownloadData, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.DownloadFailed + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.DownloadData, TraceLevel.Error); 
                return new byte[0];
            }
        }
    }
}
