﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Model
{
    public class ApiStatus
    {
        public int? ErrorCode { get; set; }

        public string ErrorMessage { get; set; }

        public bool HasError { get; set; }

        public HttpStatusCode StatusCode { get; set; }

        public int RequestTimeOut { get; set; } = 100000;

    }
}
