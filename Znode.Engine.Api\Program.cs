using Hangfire;
using Microsoft.EntityFrameworkCore;
using Znode.CommerceConnector.API;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Core;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.Engine.Api;
using Znode.Libraries.Data.Data;
using Znode.Libraries.Data.Helpers;
var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

builder.Services.AddControllers();
builder.Services.AddAutoMapper(typeof(AutoMapperConfig).Assembly);
builder.Configuration.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
builder.Configuration.AddEnvironmentVariables();
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
HelperMethods._staticServiceProvider = builder.Services.BuildServiceProvider();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();
builder.Services.AddHangfire((sp, config) =>
{
    var connectionString = sp.GetRequiredService<IConfiguration>().GetConnectionString("HangfireConnection");
    config.UseSqlServerStorage(connectionString);
});
builder.Services.AddHangfireServer();
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(builder =>
    {
        builder.AllowAnyOrigin()
               .AllowAnyHeader()
               .AllowAnyMethod();
    });
});

builder.Services.AddDbContext<AppDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("ZnodeCommerceConnectorDB"))
);
builder.Services.AddHttpClient();
builder.Services.AddHttpContextAccessor();
builder.BuildSwagger();
builder.RegisterDI();
builder.RegisterInputHandlerDI();
builder.RegisterOutputHandlerDI();
builder.RegisterParserDI();

var app = builder.Build();

HelperMethods.Configure(app.Services);
BaseEndpoint.Configure(app.Services);
app.UseSwagger();
app.UseSwaggerUI();

app.UseHttpsRedirection();

app.UseAuthorization();

app.UseCors();

app.MapControllers();

app.Run();
