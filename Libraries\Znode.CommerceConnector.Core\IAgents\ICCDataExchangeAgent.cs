﻿using Microsoft.AspNetCore.Mvc.Filters;
using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.ViewModels;

namespace Znode.CommerceConnector.Core.IAgents
{
    public interface ICCDataExchangeAgent
    {
        StandardDataExchangesListViewModel GetStandardDataExchangeList(GridModel gridModel);

        bool TriggerDataExchangeSchedular(int erpId);
        dynamic RealTimeDataScheduler(dynamic requestBody, int erpId);

        bool DeleteDataExchangeSchedular(int erpId);

        bool EnableDisableDataExchangeScheduler(int erpId, bool isActive);

        int CreateEditBaseDataExchange(BaseDefinitionViewModel viewModel);
        ProcessingLogListViewModel GetProcessingLogList(LogListRequestModel logListRequest);
        LogDetailsListViewModel GetProcessingLogDetails(GridModel gridModel, int logId);
        ProcessingLogListViewModel GetZnodeImportLogList(GridModel gridModel, string exchangeName);
        ProcessingLogDetailListViewModel GetGenericLogDetails(GridModel gridModel, int logId);

    }
}
