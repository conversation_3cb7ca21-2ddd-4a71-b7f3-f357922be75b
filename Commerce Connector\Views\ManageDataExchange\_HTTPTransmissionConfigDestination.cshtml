﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources
@using Znode.Libraries.ECommerce.Utilities;

<div class="mt-3">
    @Html.HiddenFor(model => model.ERPBaseDefinitionId)
    @Html.HiddenFor(Model => Model.TransmissionConfigurations.TransmissionConfigurationId)
    @Html.HiddenFor(Model => Model.TransmissionConfigurations.HttpsConfigDestination.APIConfigurationId)
    <input type="hidden" asp-for="ERPBaseDefinitionId" />
    <div class="row">
        <div class="col-md-12 col-lg-6 mb-3">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.HttpAction" class="form-label" data-test-selector="lblHttpAction" aria-label="Http Action"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.HttpsConfigDestination.HttpAction" id="DestHTTPAction" class="form-select" data-test-selector="drpHttpAction" aria-label="Destination API Action">
                <option value="">@CommerceConnector_Resources.SelectAPIMethod</option>
                <option value="GET">@CommerceConnector_Resources.Get</option>
                <option value="POST">@CommerceConnector_Resources.Post</option>
                <option value="PUT">@CommerceConnector_Resources.Put</option>
                <option value="DELETE">@CommerceConnector_Resources.Delete</option>
                <option value="PATCH">@CommerceConnector_Resources.Patch</option>
            </select>
            <span id="DestHTTPActionError" class="text-danger field-validation-error"> </span>
        </div>


        <div class="col-md-12 col-lg-6 mb-3" id="DivDestAuthType">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.AuthenticationType" class="form-label" data-test-selector="lblAuthenticationType" aria-label="Authentication Type"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.HttpsConfigDestination.AuthenticationType" id="DestDropdownAuthType" onchange="ManageDataExchange.prototype.HandleAuthType('Dest', $(this))" class="form-select" data-test-selector="drpAuthenticationType" aria-label="Destination Authentication Type">
                <option value="">@CommerceConnector_Resources.SelectAuthType</option>
                <option value="Basic">@CommerceConnector_Resources.Basic</option>
                <option value="OAuth2">@CommerceConnector_Resources.OAuth</option>
                <option value="Bearer">@CommerceConnector_Resources.Bearer</option>
            </select>
            <span id="DestDropdownAuthTypeError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestEndpoint">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.Endpoint" class="form-label" data-test-selector="lblEndpoint" aria-label="Endpoint"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.Endpoint" class="form-control" id="DestEndpoint" placeholder="API Endpoint" data-test-selector="txtEndpoint" aria-label="Destination Endpoint" />
            <span id="DestEndpointError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 position-relative d-none" id="DivDestAPIKey">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.APIKey" class="form-label" data-test-selector="lblPassword" aria-label="API Configuration Password"></label>
            <div class="input-group">
                <input class="form-control"
                       type="password"
                       name="TransmissionConfigurations.HttpsConfigDestination.APIKey"
                       id="DestAPIKey"
                       value="@Model.TransmissionConfigurations.HttpsConfigDestination?.APIKey" data-test-selector="txtAPIKey" aria-label="Destination API Key" placeholder="Your API key" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle password visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestGrantType">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.GrantType" class="form-label" data-test-selector="lblGrantType" aria-label="Grant Type"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.GrantType" id="DestGrantType" class="form-control" placeholder="Authentication grant type" data-test-selector="txtGrantType" aria-label="Destination Grant Type" />
            <span id="DestGrantTypeError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestAccessTokenUrl">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.AccessTokenUrl" class="form-label" data-test-selector="lblAccessTokenUrl" aria-label="Access Token Url"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.AccessTokenUrl" id="DestAccessTokenUrl" class="form-control" placeholder="Access token URL" data-test-selector="lblAccessTokenUrl" aria-label="Destination Access Token URL" />
            <span id="DestAccessTokenUrlError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestClientId">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.ClientId" class="form-label" data-test-selector="lblClientId" aria-label="Client Id"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.ClientId" id="DestClientId" class="form-control" placeholder="Client ID" data-test-selector="txtClientId" aria-label="Destination Client ID" />
            <span id="DestClientIdError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 position-relative d-none" id="DivDestClientSecret">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.ClientSecret" class="form-label" data-test-selector="lblClientSecret" aria-label="Client Secret"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <div class="input-group">
                <input class="form-control" type="password" name="TransmissionConfigurations.HttpsConfigDestination.ClientSecret" id="DestClientSecret" placeholder="Client Secret"
                       value="@Model.TransmissionConfigurations?.HttpsConfigDestination?.ClientSecret" data-test-selector="txtTransmissionConfigurationsPassword" aria-label="Destination Client Secret" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle Client Secret visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
            <span id="DestClientSecretError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestScope">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.Scope" class="form-label" data-test-selector="lblScope" aria-label="Https Configuration Destination Scope"></label>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.Scope" id="DestScope" class="form-control" data-test-selector="txtScope" aria-label="Destination Scope" />
            <span id="DestScopeError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestLocationOfCredentials">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.LocationOfCredentials" class="form-label" data-test-selector="lblLocationOfCredentials" aria-label="Location Of Credentials"></label>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.LocationOfCredentials" id="DestLocationOfCredentials" class="form-control" placeholder="Location Of Credentials"
                   data-test-selector="txtLocationOfCredentials" aria-label="Destination Location Of Credentials" />
            <span id="DestLocationOfCredentialsError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivDestAPIUsername">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.Username" class="form-label" data-test-selector="lblUsername"></label>
            <input asp-for="TransmissionConfigurations.HttpsConfigDestination.Username" id="DestAPIUsername" class="form-control" data-test-selector="txtAPIUsername" aria-label="Destination Username" />
        </div>

        <div class="col-md-12 col-lg-6 mb-3 position-relative d-none" id="DivDestAPIPassword">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.Password" class="form-label" data-test-selector="lblPassword" aria-label="API Configuration Password"></label>
            <div class="input-group">
                <input class="form-control"
                       type="password"
                       name="TransmissionConfigurations.HttpsConfigDestination.Password"
                       id="DestAPIPassword"
                       value="@Model.TransmissionConfigurations?.HttpsConfigDestination?.Password" data-test-selector="txtAPIPassword" aria-label="Destination Password" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle password visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
        </div>

        <div class="col-md-12 col-lg-6 mb-3" id="DivDestinationFileFormat">
            <label asp-for="TransmissionConfigurations.HttpsConfigDestination.Format" class="form-label" data-test-selector="lblDestinationFileFormat" aria-label="Destination Format"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.HttpsConfigDestination.Format" class="form-select" id="HTTPDestDropdownFileFormat" data-test-selector="drpFileFormat" aria-label="Destination File Format">
                <option value="">@CommerceConnector_Resources.SelectSourceFileFormat</option>
                <option value="CSV">@CommerceConnector_Resources.CSV</option>
                <option value="JSON">@CommerceConnector_Resources.JSON</option>
                <option value="XML">@CommerceConnector_Resources.XML</option>
            </select>
            <span id="HTTPDestFileFormatError" class="text-danger field-validation-error"> </span>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 col-lg-6">
            <div class="d-flex align-items-center mt-4">
                <h6 data-test-selector="hdgQueryParams" aria-label="Query Params">@CommerceConnector_Resources.QueryParams</h6>
                <button type="button" class="btn-text btn-text-secondary query-field-btn" onclick="ManageDataExchange.prototype.addParameters('destination','QueryParams')" data-test-selector="btnAddField" aria-label="Add Query Params Field">@CommerceConnector_Resources.AddField</button>
            </div>
            <div class="destination-query-params" data-test-selector="divQueryParams">

                <div id="destinationQueryParamHeader" class="row">
                    <div class="col-md-5 my-2" data-test-selector="divKey">@CommerceConnector_Resources.Key</div>
                    <div class="col-md-5 my-2" data-test-selector="divValue">@CommerceConnector_Resources.Value</div>
                </div>

                <div id="queryParamBodyDestination">
                    @if (HelperUtility.IsNotNull(Model.TransmissionConfigurations.HttpsConfigDestination?.QueryParams) && Model.TransmissionConfigurations.HttpsConfigDestination.QueryParams.Any())
                    {
                        for (int i = 0; i < Model.TransmissionConfigurations.HttpsConfigDestination.QueryParams.Count; i++)
                        {
                            <div class="row align-items-center" id="destination-query-row-@i">
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.QueryParams[@i].Key" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfigDestination.QueryParams[i].Key" data-test-selector="txtQueryParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="destinationQueryParameterError-@i" class="text-danger"></span></div>
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.QueryParams[@i].Value" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfigDestination.QueryParams[i].Value" data-test-selector="txtQueryParamsValue" /></div>
                                <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('destination-query-row-@i')" data-test-selector="btnDelete" aria-label="Delete Query Params Field"><em class="z-close-circle"></em></button></div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="row align-items-center" id="destination-query-row-0">
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.QueryParams[0].Key" class="form-control" data-test-selector="txtQueryParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="destinationQueryParameterError-0" class="text-danger"></span></div>
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.QueryParams[0].Value" class="form-control" data-test-selector="txtQueryParamsValue" /></div>
                            <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('destination-query-row-0')" data-test-selector="btnDelete" aria-label="Delete Header Params Field"><em class="z-close-circle"></em></button></div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 col-lg-6">
            <div class="d-flex align-items-center mt-4">
                <h6 data-test-selector="hdgHeaderParams" aria-label="Header Params">@CommerceConnector_Resources.HeaderParams</h6>
                <button type="button" class="btn-text btn-text-secondary ms-3" onclick="ManageDataExchange.prototype.addParameters('destination','HeaderParams')" data-test-selector="btnAddField" aria-label="Add Header Params Field">@CommerceConnector_Resources.AddField</button>
            </div>
            <div class="destination-header-params" data-test-selector="divHeaderParams">

                <div id="destinationHeaderParamHeader" class="row">
                    <div class="col-md-5 my-2" data-test-selector="divKey">@CommerceConnector_Resources.Key</div>
                    <div class="col-md-5 my-2" data-test-selector="divValue">@CommerceConnector_Resources.Value</div>
                </div>

                <div id="headerParamBodyDestination">
                    @if (HelperUtility.IsNotNull(Model.TransmissionConfigurations.HttpsConfigDestination?.HeaderParams) && Model.TransmissionConfigurations.HttpsConfigDestination.HeaderParams.Any())
                    {
                        for (int i = 0; i < Model.TransmissionConfigurations.HttpsConfigDestination.HeaderParams.Count; i++)
                        {
                            <div class="row align-items-center" id="destination-header-row-@i">
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.HeaderParams[@i].Key" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfigDestination.HeaderParams[i].Key" data-test-selector="txtHeaderParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="destinationHeaderParameterError-@i" class="text-danger"></span></div>
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.HeaderParams[@i].Value" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfigDestination.HeaderParams[i].Value" data-test-selector="txtHeaderParamsValue" /></div>
                                <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('destination-header-row-@i')" data-test-selector="btnDelete" aria-label="Delete Header Params Field"><em class="z-close-circle"></em></button></div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="row align-items-center" id="destination-header-row-0">
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.HeaderParams[0].Key" class="form-control" data-test-selector="txtHeaderParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="destinationHeaderParameterError-0" class="text-danger"></span></div>
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfigDestination.HeaderParams[0].Value" class="form-control" data-test-selector="txtHeaderParamsValue" /></div>
                            <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('destination-header-row-0')" data-test-selector="btnDelete" aria-label="Delete Header Params Field"><em class="z-close-circle"></em></button></div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="destinationQueryIndex" value="@Model.TransmissionConfigurations.HttpsConfigDestination?.QueryParams?.Count() ?? 0" data-test-selector="txtDestinationQueryIndex" aria-label="Destination Query Index Text" />
<input type="hidden" id="destinationHeaderIndex" value="@Model.TransmissionConfigurations.HttpsConfigDestination?.HeaderParams?.Count() ?? 0" data-test-selector="txtDestinationHeaderIndex" aria-label="Destination Header Index Text" />



