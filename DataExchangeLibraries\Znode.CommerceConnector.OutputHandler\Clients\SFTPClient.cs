﻿using Renci.SshNet;
using System.Diagnostics;
using System.Text;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.OutputHandler
{
    public class SFTPClient : ISFTPClient
    {
        public bool UploadData<T>(SFTPConfigurationModel sFTPConfigurationModel, string data, bool returnWithoutDeserialize = false)
        {
            MongoLogging.LogMessage(Constants.UploadDataMethodInSFTPClientCalled, Constants.UploadData, TraceLevel.Info);
            string host = sFTPConfigurationModel?.ServerAddress?.Trim();
            string username = sFTPConfigurationModel?.UserName?.Trim();
            string password = sFTPConfigurationModel?.Password?.Trim();
            string remoteFolder = sFTPConfigurationModel?.FolderPath?.Trim();
            string fileName = sFTPConfigurationModel?.FileName?.Trim();
            byte[] csvBytes = Encoding.UTF8.GetBytes(data);
            using var ms = new MemoryStream(csvBytes);
            string remoteFilePath = remoteFolder + "/" + fileName;

            try
            {
                if (!string.IsNullOrEmpty(data) && !string.IsNullOrWhiteSpace(data))
                {
                    MongoLogging.LogMessage(Constants.SFTPFolderInSFTPClientCalled + remoteFolder, Constants.UploadData, TraceLevel.Info);

                    var connectionInfo = new Renci.SshNet.ConnectionInfo(host, sFTPConfigurationModel.PortNumber, username, new PasswordAuthenticationMethod(username, password));

                    using (var sftp = new SftpClient(connectionInfo))
                    {
                        try
                        {
                            sftp.Connect();
                            sftp.UploadFile(ms, remoteFilePath);
                            sftp.Disconnect();
                            return true;
                        }
                        catch (Exception ex)
                        {
                            MongoLogging.LogMessage(Constants.FolderPathNotFound + ex.Message, Constants.UploadData, TraceLevel.Error);
                            return false;
                        }
                    }
                }
                else
                {
                    MongoLogging.LogMessage(Constants.FileToUploadIsEmpty, Constants.UploadData, TraceLevel.Error);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.ErrorUploadingFileUsingSFTP + ex.Message, Constants.UploadData, TraceLevel.Error);
                return false;
            }
        }
    }
}
