﻿namespace Znode.CommerceConnector.API.Helpers
{
    public class CommerceConnectorConstants
    {
        public const string ErrorIdLessThanZero = "Id can't be less than one.";
        public const string CallProcessorMethodCalled = "Call Processor method called";
        public const string TriggerTaskSchedularServicecalled = "TriggerTaskSchedular Service called";
        public const string TriggerTaskSchedularService = "TriggerTaskSchedular Service";
        public static string DownloadSampleAPIControllerCalled = "DownloadSample API controller called";
        public static string SaveYAMLAPIControllerCalled = "SAve YAML method in controller called";
        public static string GetYamlDataAPIControllerCalled = "Get YAML Data method in controller called";
        public static string GetYamlDataAPIControllerFailed = "Get YAML Data method in controller failed";
        public static string SaveYAMLAPIControllerFailed = "Save YAML method in controller failed";
        public static string DownloadSampleAPIControllerFailed = "DownloadSample API controller failed";
        public static string DownloadSampleServiceCalled = "DownloadSample Service called";
        public static string DownloadSampleServiceFailed = "DownloadSample Service failed";
        public static string BlobPathNotFound = "Blob not found at path:";
        public static string SaveYamlServiceCalled = "Save Yaml Service called";
        public static string SaveYamlServiceFailed = "Save Yaml Service failed";
        public static string APIController = "API Controller";
        public static string YAMLService = "YAML Service";
        public static string TriggerTaskSchedulerControllerCalled = "TriggerTaskScheduler controller called";
        public static string TriggerTaskSchedulerControllerFailed = "TriggerTaskScheduler controller failed";
        public static string RealTimeDataExchange = "RealTimeDataExchange";
        public static string RealTimeDataExchangeServiceCalled = "RealTimeDataExchange service called";
        public static string RealTimeDataExchangeServiceCFailed = "RealTimeDataExchange service failed";
        public static string CallRealTimeDataExchangeControllerCalled = "CallRealTimeDataExchange controller called";
        public static string CallRealTimeDataExchangeControllerFailed = "CallRealTimeDataExchange failed";
        public static string ProcessorFileNotFound = "Processor File is not available.";
    }
}
