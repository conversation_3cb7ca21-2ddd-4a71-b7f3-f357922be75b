﻿namespace Znode.CommerceConnector.Model
{
    public class ZnodeImportTemplateMappingModel
    {
        public int ImportTemplateMappingId { get; set; }
        public int ImportTemplateId { get; set; }
        public string SourceColumnName { get; set; }
        public string TargetColumnName { get; set; }
        public int DisplayOrder { get; set; }
        public bool IsActive { get; set; }
        public bool IsAllowNull { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int ModifiedBy { get; set; }
        public DateTime ModifiedDate { get; set; }
    }

    public class Mapping
    {
        public List<ZnodeImportTemplateMappingModel> mappings;
    }
}
