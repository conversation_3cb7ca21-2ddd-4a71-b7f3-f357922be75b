﻿namespace Znode.CommerceConnector.Core.Helper
{
    public struct CCAdminConstant
    {
        public const string HTTP = "HTTPS";
        public const string SFTP = "SFTP";
        public const string UnSupportedMode = "Unsupported transmission mode.";
        public const string TransmissionModeRequired = "Transmission mode is required.";
        public const string TransmissionMode = "Transmission Mode";
        public const string SuccessDataExchange = "Data exchange details saved successfully.";
        public const string FailedDataExchange = "Failed to save data exchange details.";
        public const string OnDemand = "OnDemand";
        public const string Scheduled = "Scheduled";
        public const string RealTime = "RealTime";
        public const string OneTime = "OneTime";
        public const string Recurring = "Recurring";
        public const string ReqSchedulerNameErrorMessage = "ReqSchedulerNameError";
        public const string ReqSchedulerTypeErrorMessage = "ReqSchedulerTypeError";
        public const string SchedulerNameMaxLengthErrorMessage = "SchedulerNameMaxLengthError";
        public const string ReqRunDateErrorMessage = "ReqRunDateError";
        public const string LabelScheduleType = "LabelScheduleType";
        public const string LabelScheduleName = "LabelScheduleName";
        public const string LabelFrequency = "LabelFrequency";
        public const string LabelRunDate = "LabelRunDate";
        public const string LabelRunTime = "LabelRunTime";
        public const string ReqRunTimeErrorMessage = "ReqRunTimeError";
        public const string LabelCronExpression = "LabelCronExpression";
        public const string CronMaxLengthErrorMessage = "CronMaxLengthError";
        public const string InvalidCronExpressionErrorMessage = "InvalidCronExpressionError";
        public const string DataExchangeEnabledSuccessMessage = "Data exchange enabled successfully.";
        public const string DataExchangeDisabledMessage = "Data exchange disabled successfully.";
        public const string TaskSchedulerTriggeredSuccessfully = "Task scheduler triggered successfully!";
        public const string TaskSchedulerTriggeredFailed = "Task Scheduler failed to trigger. Please check logs.";
        public const string SuccessfulMessage = "SuccessfulMessage";
        public const string FailureMessage = "FailureMessage";
        public const string LibraryCustom = "Custom";
        public const string LibraryNative = "Native";
        public const string FieldName = "Field";
        public const string FieldOldValue = "Old Value";
        public const string FieldNewValue = "New Value";
        public const string FieldDateChange = "Changed Date";
        public const string UserName = "UserName";
        public const string ProcessorFileAlreadyExist = "This processor filename already exists. Please enter a different filename.";
        public const string LabelCSVColumnData = "LabelCSVColumnData";
        public const string LabelCSVColumnName = "LabelCSVColumnName";
        public const string LabelEndDate = "LabelEndDate";
        public const string LabelErrorDescription = "LabelErrorDescription";
        public const string LabelImportName = "LabelImportName";
        public const string LabelImportProcessLogId = "LabelImportProcessLogId";
        public const string LabelLogId = "LabelLogId";
        public const string LabelTemplateId = "LabelTemplateId";
        public const string LabelTemplateName = "LabelTemplateName";
        public const string LabelRowNumber = "LabelRowNumber";
        public const string LabelStartDate = "LabelStartDate";
        public const string CCAdminToken = "CCAdminToken";
    }
}
