{"name": "@types/sizzle", "version": "2.3.9", "description": "TypeScript definitions for sizzle", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sizzle", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "le<PERSON><PERSON>-thieu", "url": "https://github.com/leonard-thieu"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sizzle"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "0695816c2a1aaa2ea7eb8dc8c445f1aa526abd767d88970c1c4da9db0cf34bbe", "typeScriptVersion": "4.8"}