﻿using Azure.Storage;
using Azure.Storage.Blobs;
using Znode.CommerceConnector.Model;
namespace Znode.Libraries.ECommerce.Utilities
{
    public static class HelperUtility
    {
        //Returns true if the passed value is not null, else return false.
        public static bool IsNotNull(object value)
            => !Equals(value, null);

        //Returns true if the passed value is null else false.
        public static bool IsNull(object value)
            => Equals(value, null);

        //Returns camel casing of string.
        public static string ToCamelCase(string name)
            => char.ToLowerInvariant(name[0]) + name.Substring(1);

        //Returns sorter type.
        public static string GetSorterType(Type type)
        {
            if (type == typeof(string)) return "string";
            if (type == typeof(decimal) || type == typeof(int)) return "number";
            return "string";
        }

        //Returns data type.
        public static string GetDataType(Type type)
        {
            if (type == typeof(string)) return "string";
            if (type == typeof(decimal) || type == typeof(int) || type == typeof(double)) return "number";
            if (type == typeof(DateTime)) return "date";
            return "string";
        }

        // ExchangeName, Tags and Collection columns are sortable only. If you want more column to be sortable, just add that column name in or condition.
        public static bool IsColumnSortable(string name)
        {
            if (name.Equals("exchangename", StringComparison.CurrentCultureIgnoreCase) || name.Equals("tags", StringComparison.CurrentCultureIgnoreCase) || name.Equals("collection", StringComparison.CurrentCultureIgnoreCase))
                return true;
            return false;
        }

        public static BlobContainerClient GetBlobContainerClient(MediaConfigurationModel mediaConfiguration)
        {
            string blobServiceUrl = new Uri(mediaConfiguration.URL.TrimEnd('/')).GetLeftPart(UriPartial.Authority);
            var credential = new StorageSharedKeyCredential(mediaConfiguration.AccessKey, mediaConfiguration.SecretKey);
            var blobServiceClient = new BlobServiceClient(new Uri(blobServiceUrl), credential);
            var containerClient = blobServiceClient.GetBlobContainerClient(mediaConfiguration.BucketName);

            return containerClient;
        }
    }
}
