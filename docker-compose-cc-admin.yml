version: '3.4'
name: "znode10x"

#Creating Networks
networks:
        Znode10xDBNetwork:
            driver: bridge
            name: Znode10xDBNetwork
            internal: false
        Znode10xAPINetwork:
            driver: bridge
            name: Znode10xAPINetwork
            internal: false
        Znode10xMongoNetwork:
            driver: bridge
            name: Znode10xMongoNetwork
            internal: false
        Znode10xAPINetworkExternal:
            driver: bridge
            name: Znode10xAPINetworkExternal
            internal: false
            external: false 

services:

  znode.engine.commerceconnector:
    restart: always
    # image: ${DOCKER_REGISTRY-}znode10xcommerceconnectoradmin
    image: ${AmlaRegistry}/znode10xcommerceconnectoradmin:${Tag}
    container_name: znode10xcommerceconnectoradmin
    build:
      context: .
      dockerfile: Commerce Connector/Dockerfile

    networks: 
             Znode10xAPINetwork:            
                 aliases: 
                     - Znode10xAPINetwork_GraphQLAlias
             Znode10xAPINetworkExternal:
                 aliases:
                     - Znode10xAPINetworkExternal_GraphQLAlias
             Znode10xMongoNetwork:
                 aliases:
                     - Znode10xMongoNetwork_GraphQLAlias
    ports:
           - "7089:80"
           - "7090:443"
    environment: 
           - ASPNETCORE_HTTP_PORT=http://+:80
           - ASPNETCORE_HTTPS_PORT=https://+:443
           #- ASPNETCORE_ENVIRONMENT=Development
           #----------------------------------------------------------------------------
           - ASPNETCORE_URLS=https://+:443;http://+:80
           - ASPNETCORE_Kestrel__Certificates__Default__Password=Amla@987
           - ASPNETCORE_Kestrel__Certificates__Default__Path=docker.internal.pfx 
           #------------------------------------------------------------------------- 
    volumes:
           - ~/.aspnet/https:/https:ro
