﻿.page-container {
    padding: 90px 16px 20px;

    .page-content {
        .ag-theme-alpine {
            height: auto;

            .ag-layout-normal {
                height: auto;
            }

            .ag-root-wrapper {
                box-shadow: 0 5px 8px -5px $box-border-shadow;
                border: solid .5px $grid-border !important;

                .ag-root-wrapper-body {
                    background: $base-color-white;
                    margin-bottom: 0;
                    border: 0.5px solid $border-bgcolor-default;
                    font-family: $base-font-family;

                    .ag-center-cols-viewport {
                        .ag-cell-value {
                            .fa {
                                font-size: $base-font-size + 1;
                            }

                            .fa-times, .fa-check {
                                font-size: $base-font-size + 4;
                            }

                            .fa-times {
                                color: $error-msg-fgcolor;
                            }

                            .fa-check {
                                color: $base-bgcolor-button;
                            }
                        }
                    }
                }
            }

            .ag-center-cols-container {
                min-width: 100% !important;

                .ag-row-odd {
                    background: $base-bglogin-color;
                }

                .exchange-link {
                    text-decoration: underline !important;
                }

                .ag-row-hover.ag-full-width-row.ag-row-group:before, .ag-row-hover:not(.ag-full-width-row):before {
                    background-color: $base-color-tertiary;
                }
            }
        }

        .ag-header {
            background-color: $base-color-tertiary;

            .ag-header-cell-label {
                font-weight: normal;
                border: 0;
                padding: 15px 0;
                text-transform: uppercase;
                font-family: $base-font-family-bold;
            }
        }
    }

    .show-per-page {
        font-size: $base-font-size - 2;
        text-transform: uppercase;

        .total-record, .show-page-count {
            display: inline-block;
            border-right: 1px solid $border-bgcolor-primary;
            line-height: 24px;
            padding-right: 10px;
            vertical-align: top;
            color: $form-group-label-color;
            font-family: $base-font-family;
        }

        .show-page-count {
            border-right: none;
        }

        select {
            min-width: 50px;
            float: none;
            text-align: center;
            text-align-last: center;
            line-height: 17px;
            height: 25px;
            border-radius: 5px;
        }

        .page-limit {
            border-right: 1px solid $border-bgcolor-primary;
            padding-right: 10px;

            .pagerTxt {
                width: 25px;
                text-align: center;
                height: 25px;
                border-radius: 5px;
                margin: 0 7px;
                padding: 0 2px;
                font-size: $base-font-size - 2;
            }
        }
    }
    /*Data Exchange Library CSS*/

    .data-exchange-library {
        input[type=checkbox] {
            opacity: 1 !important;
            margin: 12px 16px;
            display: block;
            border: 2px solid $switch-button-color;
            border-radius: 50%;
            width: 16px;
            height: 16px;
        }

        .form-check-input {
            &:checked {
                background-color: $base-color-secondary;
                border-color: $base-color-secondary;
                background-image: radial-gradient(circle, $base-color-white 30%, transparent 40%);
                background-size: 100% 100%;
                background-position: center;
                background-repeat: no-repeat;
            }

            &:focus {
                box-shadow: none !important;
            }
        }

        a {
            cursor: default;
        }

        .data-exchange-note {
            font-size: $base-font-size - 2;
        }
    }
    /*Manage Data Exchange (Base Definition) CSS*/


    .base-definition {
        .heading {
            font-family: $base-font-family-bold;
        }

        .view-details {
            word-break: break-word;
        }

        input[type="checkbox"] {
            opacity: 1 !important;
        }

        input[type="checkbox"]#isCustomProcessingRequired, input[type="checkbox"]#isEnable {
            accent-color: $base-color-button-secondary;
        }

        input[disabled] {
            cursor: not-allowed !important;
        }
    }
    /*Schedule Configuration*/

    .scheduler-status {
        .one-time-frequency {
            .schedule {
                em {
                    top: 10px;
                    right: 10px;
                    cursor: pointer;
                }

                input.flatpickr-input[readonly] {
                    background-color: $base-color-white !important;
                    cursor: pointer;
                }
            }
        }

        .cron-expression-info {
            background: rgba(242, 245, 245, 0.8);
            margin: 0 10px;

            label {
                font-family: $base-font-family-bold;
            }

            #toggleCronTable {
                cursor: pointer;
            }
        }

        #schedulerSetting {
            h6 {
                padding-left: 10px;
            }
        }
    }
    /* Processing Log CSS */

    .processing-log, .import-log {
        table {
            margin-left: auto;

            .import-field {
                background: $base-color-tertiary;
            }

            .import-value {
                width: 70px;
                text-align: center;
            }
        }
    }


    #logModalDialog .modal-content:empty {
        max-height: none;
        overflow: hidden;
    }

    #logModalDialog .modal-content {
        max-height: 520px;
        overflow-y: auto;
    }
    /*Mapping CSS*/

    .mapping {
        .CodeMirror-gutter {
            width: 30px !important;
            border-right: 1px solid $box-border-shadow;
            background-color: $popup-bg-color;
            white-space: nowrap;
        }

        .CodeMirror-sizer {
            margin-left: 30px !important;
        }

        .CodeMirror {
            background-color: $base-color-white !important;
            border: 1px solid $box-border-shadow !important;
            height: 200px !important;
        }

        .CodeMirror-cursor {
            border: 1px solid $box-border-shadow !important;
        }

        .CodeMirror-placeholder {
            position: absolute;
            left: 8px;
            top: 0px;
            color: $input-placeholder-color;
            pointer-events: none;
            font-style: italic;
            user-select: none;
            z-index: 12;
        }

        .editor-root {
            overflow-y: hidden !important;
        }

        .editor-menu {
            color: $base-color-white;
            background-color: $input-edit-bg-color;
        }

        .yaml-textarea {
            .editor-menu {
                width: 100%;
                height: 35px;
                margin: 0;
                -moz-box-sizing: border-box;
                -webkit-box-sizing: border-box;
                box-sizing: border-box;
                color: $base-color-white;
                background-color: $input-edit-bg-color;
                border-bottom: 1px solid $input-edit-bg-color;
            }
        }

        .download-upload-yml {
            .btn-text-secondary {
                width: 140px;
            }

            .download-btn {
                margin-left: 26px;
            }
        }
    }

    #headingMapping {
        .accordion-button {
            box-shadow: inset 0 -1px 0 $accordian-border-btn;
        }
    }

    .accordion-body {
        .query-field-btn {
            margin-left: 23px;
        }
    }
}
