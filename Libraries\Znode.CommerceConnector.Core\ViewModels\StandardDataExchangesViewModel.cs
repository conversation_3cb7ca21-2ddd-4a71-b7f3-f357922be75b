﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class StandardDataExchangesViewModel
    {
        public int DataExchangeLibraryId { get; set; }
        public int ExchangeId { get; set; }
        [Display(Name = "Exchange Name")]
        public string? ExchangeName { get; set; }
        public string? Library { get; set; }

        public string? Format { get; set; }
        public string? Source { get; set; }
        public string? Destination { get; set; }

        public string? Schedule { get; set; }
        [Display(Name = "Last Run Time")]
        public DateTime? LastRunTime { get; set; }

        [Display(Name = "Last Run Result")]
        public DateTime? LastRunResult { get; set; }
        public bool Status { get; set; }
    }
}
