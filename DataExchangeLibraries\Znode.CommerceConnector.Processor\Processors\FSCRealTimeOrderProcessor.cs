﻿using System.Diagnostics;
using System.Net;
using Znode.CommerceConnector.Client;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.ECommerce.Utilities;
using Constants = Znode.CommerceConnector.OutputHandler.Constants;

namespace Znode.CommerceConnector.Processor
{
    public class FSCRealTimeOrderProcessor
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public FSCRealTimeOrderProcessor(IOutputHandlerInitializer handler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = handler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

        public dynamic ProcessData(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            try
            {
                dynamic data = "";
                MongoLogging.LogMessage(ProcessorConstants.FSCRealTimeOrderProcessorProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                WebHeaderCollection headers = new WebHeaderCollection();
                //Download the data
                data = _inputHandler.InputHandler(erpId, processorDetails.LogId, InputHandlerRequestBody(), headers, inputModel);

                Type outputType = ((object)data).GetType();

                if (outputType.Name == ProcessorConstants.Boolean && data == false)
                {
                    MongoLogging.LogMessage(ProcessorConstants.InvalidTempTableNameNotConstructedProperly, ProcessorConstants.ProcessData, TraceLevel.Error);
                    return false;
                }
                else
                {
                    MongoLogging.LogMessage(ProcessorConstants.ParserFSCRealTimeOrderProcessorProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                    //Process the data
                    dynamic yamlModel = _inputHandler.GetYAMLDataHandler(erpId);
                    data = _iParser.ParseYAMLData(data, erpId, yamlModel);
                    //return data;

                    if (HelperUtility.IsNotNull(data) && data != "")
                    {
                        MongoLogging.LogMessage(ProcessorConstants.OutputHandlerFSCRealTimeOrderProcessorProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);

                        headers = OutputHandlerHeaders(erpId);
                        //Upload the data
                        return _outputHandler.OutputHandler(data, erpId, OutputHandlerRequestBody(data), headers, processorDetails, inputModel);
                    }
                    MongoLogging.LogMessage(ProcessorConstants.OutputHandlerFSCRealTimeOrderProcessorProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerFSCRealTimeOrderProcessorProcessorFailed + ex.Message, ProcessorConstants.ProcessData, TraceLevel.Info);
                return false;
            }
        }

        private dynamic OutputHandlerRequestBody(dynamic data)
        {
            return data.ToString();
        }

        private dynamic InputHandlerRequestBody()
        {
            return new NullableRequestBody();
        }

        private WebHeaderCollection OutputHandlerHeaders(int erpId)
        {
            HTTPConfigurationModel httpConfigurationModel = _outputHandler.GetOutputHandlerAPICredentials(erpId);
            WebHeaderCollection headers = new WebHeaderCollection
                    {
                        { Constants.Authorization, ("Bearer " + GetBearerToken(httpConfigurationModel)) }
                    };

            return headers;
        }

        public dynamic GetBearerToken(HTTPConfigurationModel httpConfigurationModel)
        {
            BearerTokenModel bearerTokenModel = new BearerTokenModel();
            var keyValues = new List<KeyValuePair<string, string>>()
            {
                new KeyValuePair<string, string>(Constants.client_id,httpConfigurationModel.ClientId),
                new KeyValuePair<string, string>(Constants.client_secret, httpConfigurationModel.ClientSecret),
                new KeyValuePair<string, string>(Constants.grant_type, httpConfigurationModel.GrantType),
                new KeyValuePair<string, string>(Constants.scope, httpConfigurationModel.Scope)
            };

            ApiClient apiClient = new ApiClient();
            RequestModel requestModel = apiClient.SetRequestModel(httpConfigurationModel.AccessTokenURL);
            requestModel.RequestBody = string.Join("&", keyValues.Select(kvp => $"{Uri.EscapeDataString(kvp.Key)}={Uri.EscapeDataString(kvp.Value ?? "")}"));
            requestModel.ContentType = "application/x-www-form-urlencoded";
            TokenResponseModel apiResponse = apiClient.PostRequest<TokenResponseModel>(requestModel, out string statusCode);
            return apiResponse?.access_token;
        }
    }
}