{"compilers": {"less": {"autoPrefix": "", "cssComb": "none", "ieCompat": true, "math": null, "strictMath": false, "strictUnits": false, "relativeUrls": true, "rootPath": "", "sourceMapRoot": "", "sourceMapBasePath": "", "sourceMap": false}, "sass": {"autoPrefix": "", "loadPaths": "", "style": "expanded", "relativeUrls": true, "sourceMap": false}, "stylus": {"sourceMap": false}, "babel": {"sourceMap": false}, "coffeescript": {"bare": false, "runtimeMode": "node", "sourceMap": false}, "handlebars": {"root": "", "noBOM": false, "name": "", "namespace": "", "knownHelpersOnly": false, "forcePartial": false, "knownHelpers": [], "commonjs": "", "amd": false, "sourceMap": false}}, "minifiers": {"css": {"enabled": true, "termSemicolons": true, "gzip": false}, "javascript": {"enabled": true, "termSemicolons": true, "gzip": false}}}