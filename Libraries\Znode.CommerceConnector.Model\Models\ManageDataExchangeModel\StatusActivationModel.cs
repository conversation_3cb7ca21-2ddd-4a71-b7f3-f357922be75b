﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Model
{
    public class StatusActivationModel
    {
        public int? StatusId { get; set; }
        public string? Status { get; set; }
        public string? InformationNotification { get; set; }
        public string? WarningNotification { get; set; }
        public string? ErrorNotification { get; set; }

        public int? ERPBaseDefinitionId { get; set; }
    }
}

