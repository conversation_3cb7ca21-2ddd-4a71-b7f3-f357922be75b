﻿using System.Collections.Specialized;

using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Services
{
    public interface IManageDataExchangeService
    {
        BaseDefinitionModel GetDataExchangeDetailsByID(int id);
        bool SaveDataExchangeDetails(BaseDefinitionModel model);
        BaseDefinitionModel GetBaseDefinitionDetailsByID(int id);
        Task<bool> TestConnection(string mode, string request);      
        int CreateEditBaseDataExchange(BaseDefinitionModel model);

        bool UpdateBaseDefinitionDetailsByPutAPIAction(int id,BaseDefinitionModel model);

        bool UpdateBaseDefinitionDetailsByPatchAPIAction(int id, BaseDefinitionModel model);
        ConfigurationChangeLogListModel GetConfigurationChangeList(int id, FilterCollection filters, NameValueCollection sorts, NameValueCollection page);

        bool ValidateSchedulerName(string schedulerName);
    }
}
