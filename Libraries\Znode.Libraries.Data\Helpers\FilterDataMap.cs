﻿using Znode.Libraries.ECommerce.Utilities;

namespace Znode.Libraries.Data.Helpers
{
    public static class FilterDataMap
    {
        public static FilterDataCollection ToFilterDataCollection(this FilterCollection filters)
        {
            FilterDataCollection dataCollection = new FilterDataCollection();
            if (!Equals(filters, null))
            {
                dataCollection.AddRange(ToModel(filters));
            }
            return dataCollection;
        }

        public static List<FilterDataTuple> ToModel(FilterCollection filters)
        {
            List<FilterDataTuple> list = new List<FilterDataTuple>();
            foreach (var item in filters)
            {
                FilterDataTuple filterDataTuple = new FilterDataTuple(item.FilterName, item.FilterOperator, item.FilterValue);
                list.Add(filterDataTuple);
            }
            return list;
        }
    }
}
