﻿//All Global Code Such as Basic HTML, Button, Dropdown, Multiselect dropdown, Input Field, Chackbox & Redio Button, ToolTip, Loader, Yes No Switch, Collapse Panel, Auto Complete, Alerts, Aside Panel.

// Font Family.
@font-face {
    font-family: 'Roboto-Regular';
    src: url('../../fonts/Roboto-Regular.ttf') format('truetype');
    src: url('../../fonts/Roboto-Regular.eot') format('embedded-opentype');
    src: url('../../fonts/Roboto-Regular.svg') format('svg');
    src: url('../../fonts/Roboto-Regular.woff') format('woff');
}

@font-face {
    font-family: 'Roboto-Medium';
    src: url('../../fonts/Roboto-Medium.ttf') format('truetype');
    src: url('../../fonts/Roboto-Medium.eot') format('embedded-opentype');
    src: url('../../fonts/Roboto-Medium.svg') format('svg');
    src: url('../../fonts/Roboto-Medium.woff') format('woff');
}

@font-face {
    font-family: 'Roboto-Light';
    src: url('../../fonts/Roboto-Light.ttf') format('truetype');
    src: url('../../fonts/Roboto-Light.eot') format('embedded-opentype');
    src: url('../../fonts/Roboto-Light.svg') format('svg');
    src: url('../../fonts/Roboto-Light.woff') format('woff');
}

@font-face {
    font-family: 'RobotoCondensed-Regular';
    src: url('../../fonts/RobotoCondensed-Regular.ttf') format('truetype');
    src: url('../../fonts/RobotoCondensed-Regular.eot') format('embedded-opentype');
    src: url('../../fonts/RobotoCondensed-Regular.svg') format('svg');
    src: url('../../fonts/RobotoCondensed-Regular.woff') format('woff');
}

@font-face {
    font-family: 'RobotoCondensed-Light';
    src: url('../../fonts/RobotoCondensed-Light.ttf') format('truetype');
    src: url('../../fonts/RobotoCondensed-Light.eot') format('embedded-opentype');
    src: url('../../fonts/RobotoCondensed-Light.svg') format('svg');
    src: url('../../fonts/RobotoCondensed-Light.woff') format('woff');
}

@font-face {
    font-family: 'RobotoCondensed-Bold';
    src: url('../../fonts/RobotoCondensed-Bold.ttf') format('truetype');
    src: url('../../fonts/RobotoCondensed-Bold.eot') format('embedded-opentype');
    src: url('../../fonts/RobotoCondensed-Bold.svg') format('svg');
    src: url('../../fonts/RobotoCondensed-Bold.woff') format('woff');
}

html, body {
    height: 100%;

    font: {
        family: $base-font-family;
        size: $base-font-size;
    }

    color: $text-fgcolor-default;
    @include -webkit-text-size-adjust;
    -ms-overflow-style: scrollbar;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    background-color: $base-bglogin-color;
}

.body-wrapper {
    form {
        padding: 0;
    }
}

.nopadding {
    padding: 0;
    margin: 0;
}

.required:after {
    content: ' * ';
    color: $required-star;
    padding: 0 2px;
}

a {
    outline: medium none !important;
    text-decoration: none !important;
    color: darken($base-color-secondary, 10%);
    cursor: pointer;

    &:hover {
        outline: medium none;
        text-decoration: none;
        color: $base-color-secondary;
    }
}

h1, h2, h3, h4, h5, h6 {
    color: $base-color-primary;

    font: {
        family: $base-font-family-bold;
    }
}

h1 {
    margin: 5px 0;
    font-size: $base-font-size + 10;
}

h2 {
    margin: 5px 0;
    font-size: $base-font-size + 7;
}

h3 {
    margin: 5px 0;
    font-size: $base-font-size + 5;
}

h4 {
    margin: 5px 0;
    font-size: $base-font-size + 2
}

h5 {
    margin: 4px 0;
    font-size: $base-font-size + 2;
}

h6 {
    margin: 4px 0;
    font-size: $base-font-size;
}

label {
    margin-bottom: 0;
}

.dropdown-menu {
    border-radius: 0;
    z-index: 99;
}

.error-msg, .field-validation-error, .field-validation-error span {
    color: $error-msg-fgcolor !important;
    float: left;
    width: 98%;
    font-size: $base-font-size;
    font-family: $base-font-family;
}

.success-msg {
    color: $success-msg-fgcolor;
}

.input-validation-error {
    border: 1px solid $error-msg-fgcolor !important;
}

.caret-color {
    color: $base-color-white;
}

// Input Field.
input[type="text"], input[type="password"], input[type="email"], input[type="date"], input[type="number"] {
    @include input-field;
    height: 34px;
}

input[type=number] {
    -moz-appearance: textfield;
    -webkit-appearance: textfield;

    &::-webkit-outer-spin-button, &::-webkit-inner-spin-button {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
    }
}

textarea {
    @include input-field;
    resize: vertical;
    overflow-y: auto;
}
select{
    cursor:pointer;
}
html input[disabled], html select[disabled], html textarea[disabled], html input[readonly], html select[readonly], html textarea[readonly] {
    cursor: not-allowed;
    background-color: $border-bgcolor-default;
}

// Checkbox & Radio Button.
input[type=checkbox], input[type=radio] {
    opacity: 0 !important;
    position: absolute;
    z-index: 12;
    width: 18px;
    height: 18px;
    cursor: pointer;
    margin: 0;
}

input[type=checkbox]:checked, input[type=radio]:checked, input[type=checkbox]:focus, input[type=radio]:focus {
    outline: none !important;
    cursor: pointer;
}

input[type=checkbox] + .lbl, input[type=radio] + .lbl {
    z-index: 11;
    display: inline-block;
    margin: 0;
    min-height: 14px;
    min-width: 14px;
    font-weight: normal;
    cursor: pointer;
}

input[type=checkbox] + .lbl.padding-8::before, input[type=radio] + .lbl.padding-8::before {
    margin-right: 8px;
}

input[type=checkbox] + .lbl::before {
    font-family: Glyphicons Halflings;
    font-weight: normal;
    font-size: $base-font-size - 1;
    color: $base-color-secondary;
    content: "\a0";
    background-color: $input-bg-color;
    border: 2px solid $base-color-primary;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 2px;
    display: inline-block;
    text-align: center;
    vertical-align: baseline;
    height: 15px;
    line-height: 9px;
    min-width: 16px;
    margin-right: 1px;
    margin-top: -5px;
}

input[type=radio] + .lbl::before {
    font-family: Glyphicons Halflings;
    font-weight: normal;
    font-size: $base-font-size - 1;
    color: $base-color-secondary;
    content: "\a0";
    background-color: $input-bg-color;
    border: 1px solid $border-bgcolor-primary;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 0;
    display: inline-block;
    text-align: center;
    vertical-align: baseline;
    height: 16px;
    line-height: 15px;
    min-width: 16px;
    margin-right: 1px;
    margin-top: -3px;
}

input[type=radio] + .lbl::before {
    vertical-align: middle;
}

input[type=checkbox]:checked + .lbl::before, input[type=radio]:checked + .lbl::before {
    display: inline-block;
    content: '\e013';
    background-color: $base-color-secondary;
    border-color: $base-color-secondary;
    color: $base-color-white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),inset 0 -15px 10px -12px rgba(0, 0, 0, 0.05),inset 15px 10px -12px rgba(255, 255, 255, 0.1);
    cursor: pointer;
}

input[type=checkbox]:hover + .lbl::before, input[type=radio]:hover + .lbl::before, input[type=checkbox] + .lbl:hover::before, input[type=radio] + .lbl:hover::before { /*border-color:$border-bgcolor-primary;*/
    cursor: pointer;
}

input[type=checkbox]:active + .lbl::before, input[type=radio]:active + .lbl::before, input[type=checkbox]:checked:active + .lbl::before, input[type=radio]:checked:active + .lbl::before { /*box-shadow:0 1px 2px rgba(0, 0, 0, 0.05),inset 0px 1px 3px rgba(0, 0, 0, 0.1);*/
}

input[type=checkbox]:disabled + .lbl::before, input[type=radio]:disabled + .lbl::before, input[type=checkbox][disabled] + .lbl::before, input[type=radio][disabled] + .lbl::before, input[type=checkbox].disabled + .lbl::before, input[type=radio].disabled + .lbl::before {
    background-color: $border-bottom-color !important;
    border-color: $border-bgcolor-default !important;
    box-shadow: none !important;
    color: $input-checkbox-color;
}

input[type=radio] + .lbl::before {
    border-radius: 32px;
    font-family: $base-font-family;
    font-size: $base-font-size - 18;
}

input[type=radio]:checked + .lbl::before {
    content: "\2022";
}

.disabled-checkbox {
    pointer-events: none !important;
    cursor: not-allowed;

    &:before {
        pointer-events: none !important;
        background-color: $base-color-tertiary !important;
        color: $base-color-tertiary !important;
    }
}


.overlay, .ui-widget-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
}

.overlay {
    z-index: 99999;
}

.overlay-boxed {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 99;
    background-color: rgba(255, 255, 255, 0.82);
}

.dashboard-panel-heading {
    background: $base-color-white;
    color: $dashboard-panel-heading;
    border-color: $border-bottom-color;
    border-bottom: 0.5px solid $border-bgcolor-default;
}

.accordion {
    box-shadow: 0 5px 8px -5px $box-border-shadow;

    .accordion-header {
        font-family: $base-font-family-bold;
    }

    .accordion-button {
        &:not(.collapsed) {
            color: $accordion-not-collapsed-color;
            background-color: transparent;
        }

        &:focus {
            box-shadow: 0 0 0 .5px $box-border-shadow;
        }
    }

    .accordion-body {
        .col-md-12 {
            padding: 2px 20px;
        }
    }
}
.form-select, .form-control {

    &:focus {
        box-shadow: 0 0 0 .5px $box-border-shadow;
        border: 1px solid $border-bgcolor-primary;
    }

    border: 1px solid $border-bgcolor-primary;
}
.search-close-icon {
    background: transparent;
    border: none;

    .z-close-circle {
        font-size: $base-font-size + 2;
        color: $base-color-button-secondary;
    }
}

.full-height {

    h1 {
        font-size: $base-font-size + 20;
    }

    h3 {
        font-size: $base-font-size + 14;
    }

    p {
        font-size: $base-font-size + 2;
    }
}

// Bootstrap container code override to avoid responsive approach on browser(For Tab And Mobile View).
@media (max-width: 991px) {
    .container, body, html, header {
        width: 970px !important;
    }
}

//PopUp Box

.modal-title {
    font-size: $base-font-size + 6;
    font-family: $base-font-family-bold;
    display: block;
    line-height: 38px;
    color: $base-color-white;
    text-transform: uppercase;
}

.modal-header {
    background-color: $base-color-primary;
    padding: 10px 14px;

    .modal-title {
        font-size: $base-font-size + 6;
        font-family: $base-font-family-bold;
        display: block;
        line-height:0;
        color: $base-color-white;
        text-transform: uppercase;
    }

    .modal-header {
        background-color: $base-color-primary;

        .close {
            color: $base-color-white;
            opacity: 1;
            margin-top: 3px;
        }
    }

    .btn-text-icon {
        color: $base-color-white;

        &:hover, &:focus {
            color: $base-color-white !important;
        }
    }
}

.my-validation-error {
    border: 1px solid #FC5961 !important;
}

// Loader
.loader {
    position: fixed;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99;

    img {
        width: 175px;
        height: auto;
    }
}