using System.Diagnostics;
using System.IO.Compression;
using System.Net;
using System.Runtime.Remoting;
using System.Text;
using System;
using System.IO;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;
using System.Collections.Generic;
using Newtonsoft.Json;
using Znode.CommerceConnector.Client;

namespace Znode.CommerceConnector.Processor
{
    public class ZnodeProductImportProcessor : IProcessor
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public ZnodeProductImportProcessor(IOutputHandlerInitializer handler, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = handler;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

        public dynamic ProcessData(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            try
            {
                dynamic data = "";
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);

                WebHeaderCollection headers = new WebHeaderCollection
            {
                { "Authorization", $"Bearer {GetBearerToken(erpId)}"}
            };

                //Download the data
                data = _inputHandler.InputHandler(erpId, processorDetails.LogId, InputHandlerRequestBody(), headers, inputModel);
                Type outputType = ((object)data).GetType();

                if (outputType.Name == ProcessorConstants.Boolean && data == false)
                {
                    MongoLogging.LogMessage(ProcessorConstants.InvalidTempTableNameNotConstructedProperly, ProcessorConstants.ProcessData, TraceLevel.Error);
                    return false;
                }
                else
                {
                    MongoLogging.LogMessage(ProcessorConstants.ParserInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                    //Process the data
                    dynamic yamlModel = _inputHandler.GetYAMLDataHandler(erpId);
                    data = _iParser.ParseYAMLData(data, erpId, yamlModel);

                    if (HelperUtility.IsNotNull(data) && data != "")
                    {
                        MongoLogging.LogMessage(ProcessorConstants.OutputHandlerInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);

                        //Upload the data
                        WebHeaderCollection outputhandlerheaders = new WebHeaderCollection();
                        return _outputHandler.OutputHandler(data, erpId, OutputHandlerRequestBody(data), outputhandlerheaders, processorDetails, inputModel);
                    }
                    MongoLogging.LogMessage(ProcessorConstants.OutputHandlerInventoryProcessorCalled, ProcessorConstants.ProcessData, TraceLevel.Info);
                    return false;
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(ProcessorConstants.InputHandlerInventoryProcessorFailed + ex.Message, ProcessorConstants.ProcessData, TraceLevel.Info);
                return false;
            }
        }

        private dynamic OutputHandlerRequestBody(dynamic data)
        {
            byte[] byteArray = Encoding.UTF8.GetBytes(data);

            using (var outputStream = new MemoryStream())
            {
                using (var brotliStream = new BrotliStream(outputStream, CompressionLevel.Optimal))
                {
                    brotliStream.Write(byteArray, 0, byteArray.Length);
                    brotliStream.Flush();
                }
                byteArray = outputStream.ToArray();
            }

            ImportModel importModel = new ImportModel
            {
                ImportType = "Product",
                ImportData = byteArray,
                LocaleCode = "1",
                TouchPointName = "ProductRefresh",
                Properties = new Dictionary<string, string>() { { "FamilyCode", "Default" } }
            };
            return importModel;
        }

        private dynamic InputHandlerRequestBody()
        {
            return new NullableRequestBody();
        }

        public dynamic GetBearerToken(int erpId)
        {
            HTTPConfigurationModel httpConfigurationModel = _inputHandler.GetInputHandlerHTTPCredentials(erpId);
            WebHeaderCollection headers = new WebHeaderCollection
            {
                { "Authorization", $"Basic {Convert.ToBase64String(Encoding.UTF8.GetBytes($"{httpConfigurationModel.ClientId.Trim()}:{httpConfigurationModel.ClientSecret.Trim()}"))}" }
            };
            
            BearerTokenModel bearerTokenModel = new BearerTokenModel();
            var keyValues = new Dictionary<string, string>()
            {
                { "username","znodepimconnector_0220" },
                { "password", "6ec200640" },
                { "grant_type", httpConfigurationModel.GrantType }
            };

            ApiClient apiClient = new ApiClient();
            RequestModel requestModel = apiClient.SetRequestModel(httpConfigurationModel.AccessTokenURL);
            requestModel.RequestBody = JsonConvert.SerializeObject(keyValues);
            requestModel.HeaderCollection = headers;
            requestModel.ContentType = "application/json";
            TokenResponseModel apiResponse = apiClient.PostRequest<TokenResponseModel>(requestModel, out string statusCode);
            return apiResponse?.access_token;
        }
    }
}
