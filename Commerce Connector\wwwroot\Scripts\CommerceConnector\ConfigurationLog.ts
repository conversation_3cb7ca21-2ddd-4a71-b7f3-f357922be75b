﻿declare var agGrid: any;

class ConfigurationLog {
    private gridInitialized = false;
    private gridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0 // disables AG Grid's internal sorting
        },
        rowData: [],
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,

        onGridReady: (params: any) => {
            this.gridOptions.api = params.api;
            this.gridOptions.columnApi = params.columnApi;
            $(window).on("resize", () => {
                if (this.gridOptions.api) {
                    this.gridOptions.api.sizeColumnsToFit();
                }
            });
            setTimeout(() => {
                params.api.sizeColumnsToFit();
            }, 0);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const activeSort = sortState.find((c: any) => c.sort);
                DynamicGrid.prototype.setSorting(activeSort?.colId || null, activeSort?.sort || null);
                this.LoadConfigurationLogGrid(1);
            });
        }
    };

    public LoadConfigurationLogGrid(page: number) {
        ZnodeGlobal.prototype.ShowLoader();
        const request = {
            page: page,
            pageSize: Number($('#pageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#globalSearch").val()
        };
        const erpId = (document.getElementById("processLogErpId") as HTMLInputElement)?.value;
        $.ajax({
            url: `/commerce-connector/ManageDataExchange/RenderConfigurationChangeGrid/${erpId}`,
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(request),
            success: (res) => {
                const rowCount = res?.rows?.length || 0;

                if (rowCount === 0) {
                    if (this.gridInitialized) {
                        this.gridOptions.api.setRowData([]);
                    }

                    $("#configurationChangeLogGrid").hide();
                    $("#paginationControls").hide();
                    $(".show-per-page").hide();
                    $(".show-page-count").hide();
                    $("#customRowInfo").hide();
                    $("#noRecordsMessage").show();
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    $("#noRecordsMessage").hide();
                    $(".show-per-page").show();
                    $(".show-page-count").show();
                    $("#customRowInfo").show();
                    $("#configurationChangeLogGrid").show();
                    $("#paginationControls").show();
                }
                DynamicGrid.prototype.setTotalRows(res.totalCount);

                if (!this.gridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#configurationChangeLogGrid');
                    new agGrid.Grid(gridDiv!, this.gridOptions);
                    this.gridInitialized = true;
                }
                if (!this.gridOptions.columnDefs?.length && res.columns) {
                    const columnsToSkipTooltips = ['configurationLogID'];
                    const dateFields = ['changeDateTime'];
                    const dynamicCols = res.columns.map(c => {
                        const field = c.field as string;
                        const column: any = {
                            field: c.field,
                            headerName: c.title,
                            flex: 1,
                            sortable: c.sortable === true,
                            filter: false,
                            hide: ["configurationLogID", "selected"].indexOf(c.field) !== -1
                        };
                        if (['changeDateTime'].indexOf(field) > -1) {
                            column.valueFormatter = (params) => ZnodeGlobal.prototype.ConvertDateTimeToLocal(params.value);
                        }
                        if (columnsToSkipTooltips.indexOf(field) === -1) {
                            if (!(dateFields.indexOf(field) === -1)) {
                                column.tooltipValueGetter = (params) => ZnodeGlobal.prototype.ConvertDateTimeToLocal(params.value);
                            }
                            else {
                                column.tooltipField = field;
                            }
                        }
                        return column;
                    });

                    this.gridOptions.api.setColumnDefs([ ...dynamicCols]);
                }

                this.gridOptions.api.setRowData(res.rows);
                setTimeout(() => {
                    this.gridOptions.api.sizeColumnsToFit();
                }, 100);

                // Auto-size columns to fit content
                const allColumnIds = [];
                this.gridOptions.columnApi.getAllColumns().forEach(col => allColumnIds.push(col.getColId()));
                this.gridOptions.columnApi.autoSizeColumns(allColumnIds);
                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, '', (page) => {
                    this.LoadConfigurationLogGrid(page);
                });
                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount);
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    public getGridOptions() {
        return this.gridOptions;
    }
}

$(document).ready(function () {
    const instance = new ConfigurationLog();
    if ($("#configurationChangeLogGrid").length > 0) {
        const id = $(this).data('id');
        instance.LoadConfigurationLogGrid(1);
        $('#pageSizeSelect').on('change', () => {
            this.currentPageSize = + $('#pageSizeSelect').val();
            instance.LoadConfigurationLogGrid(1);
        });
        $('#globalSearchbtn').on('click', () => {
            instance.LoadConfigurationLogGrid(1)
        });
        $('#globalSearch').keydown(function (e) {
            if (e.keyCode === 13) {
                instance.LoadConfigurationLogGrid(1);
            }
        });
    }
}); 
