﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeTransmissionConfigurartionTable
{
    public int ConfigurationId { get; set; }

    public int Erpid { get; set; }

    public string? TransmissionMode { get; set; }

    public string? TransmissionRole { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }

    public virtual ZnodeBaseDefinationTable Erp { get; set; } = null!;

    public virtual ICollection<ZnodeHttpconfigurationTable> ZnodeHttpconfigurationTables { get; set; } = new List<ZnodeHttpconfigurationTable>();

    public virtual ICollection<ZnodeSftpconfigurationTable> ZnodeSftpconfigurationTables { get; set; } = new List<ZnodeSftpconfigurationTable>();
}
