﻿@using Znode.Libraries.Resources.CommerceConnector_Resources
@using System.Configuration

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Commerce Connector 2.0</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/Content/css/site.css" asp-append-version="true" />
    <!-- Flatpickr styles -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css" />
</head>
<body>
    <header>
        <div class="container-fluid">
            <a class="logo" href="#" data-test-selector="lnkCommerceConnector" aria-label="Commerce Connector Logo">@CommerceConnector_Resources.CommerceConnectorLogo</a>

        </div>
    </header>
    <div class="container-fluid wrapper">
        <div role="main" class="row pb-3 body-wrapper">
            @RenderBody()
        </div>
    </div>
    <div id="loading-div-background" class="overlay pointer-none" style="display:none">
        <div class="loader">
            <img src="~/Content/Images/loadingDots.gif" alt="Loading" data-test-selector="imgLoader" />
        </div>
    </div>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Flatpickr -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    <script src="~/Scripts/Bundles/DataExchange.min.js"></script>
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/ag-grid-community@30.2.0/dist/ag-grid-community.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validation-unobtrusive/4.0.0/jquery.validate.unobtrusive.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/yaml/yaml.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/codemirror.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/addon/display/placeholder.min.js"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
