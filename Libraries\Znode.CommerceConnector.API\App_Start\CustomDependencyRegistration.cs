﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.API.IHelpers;
using Znode.CommerceConnector.API.Services;
using Znode.CommerceConnector.API.Services.Service;
using Znode.CommerceConnector.Hangfire;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.InputHandler.HandlerHelper;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor;
using Znode.Libraries.Hangfire;


namespace Znode.CommerceConnector.Core
{
    public static class CustomDependencyRegistration
    {
        /// <summary>
        /// Configure or register custom Dependency Injection (DI).
        /// </summary>
        /// <param name="builder"></param>
        public static void RegisterDI(this WebApplicationBuilder builder)
        {
            builder.Services.AddTransient<IDataExchangeLibraryService, DataExchangeLibraryService>();
            builder.Services.AddTransient<ITaskSchedularService, TaskSchedularService>();
            builder.Services.AddTransient<IOutputHandlerInitializer, OutputHandlerInitializer>();
            builder.Services.AddTransient<IInputHandlerInitializer, InputHandlerInitializer>();
            builder.Services.AddTransient<IParser, Znode.CommerceConnector.Parser.Parser>();
            builder.Services.AddTransient<IAPIDataHelper, APIDataHelper>();
            builder.Services.AddTransient<IHandlerDataHelper, HandlerDataHelper>();
            builder.Services.AddTransient<IManageDataExchangeService, ManageDataExchangeService>();
            builder.Services.AddTransient<IYAMLService, YAMLService>();
            builder.Services.AddTransient<IRealTimeDataExchangeService, RealTimeDataExchangeService>();
            builder.Services.AddTransient<IHangfireJob, HangfireJob>();
            builder.Services.AddTransient<ITaskSchedulerConfiguration, TaskSchedulerConfiguration>();
            builder.Services.AddTransient<IHangfireHelper, HangfireHelper>();
        }
    }
}
