﻿using Microsoft.AspNetCore.Mvc;
using System.Collections.Specialized;
using Znode.CommerceConnector.API.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API
{
    public class BaseController: ControllerBase
    {

        private QueryStringParser _queryStringParser;

        protected QueryStringParser QueryStringParser =>
            _queryStringParser ??= new QueryStringParser(HttpContext?.Request?.QueryString.Value ?? string.Empty);

        protected FilterCollection Filters => QueryStringParser.Filters;

        protected NameValueCollection Sorts => QueryStringParser.Sorts;

        protected NameValueCollection Page => QueryStringParser.Page;

        protected IActionResult CreateOKResponse<T>(T data)
        {
            return new ObjectResult(APIHelpers.ToJson(data)) { StatusCode = 200 };
        }

        protected IActionResult CreateInternalServerErrorResponse<T>(T data)
        {
            return new Microsoft.AspNetCore.Mvc.StatusCodeResult(500);
        }
    }
}
