﻿using System.Collections.Specialized;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Services
{
    public interface IDataExchangeLibraryService
    {
        DataExchangeListModel GetDataExchangeLibraryList(FilterCollection filters, NameValueCollection sorts, NameValueCollection page);
        StandardDataExchangeListModel GetDataExchangeList(FilterCollection filters, NameValueCollection sorts, NameValueCollection page);

        /// <summary>
        /// Insert the data into znodeBaseDefinitionTable based on ids.
        /// </summary>
        /// <param name="selectedIds"></param>
        /// <returns></returns>
        bool AddSelectedExchange(AddSelectedExchangeModel addSelectedExchangeModel);

        bool DeleteDataExchangeSchedular(int erpId);

        ProcessorLogListModel GetGenericLogList(FilterCollection filters, NameValueCollection sorts, NameValueCollection page, int logId = 0);

        ProcessingLogDetailListModel GetGenericLogDetails(FilterCollection filters, NameValueCollection sorts, NameValueCollection page, int logId);
        bool ValidateExchangeName(string exchangeName);
    }
}
