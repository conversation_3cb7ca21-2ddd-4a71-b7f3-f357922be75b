﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Diagnostics;
using System.Net;

using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.OutputHandler.IHandlers;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.OutputHandler
{
    public class OutputHandlerInitializer : IOutputHandlerInitializer
    {
        IAPIHandler _aPIHandler;
        ISFTPHandler _iSFTPHandler;
        IFTPHandler _iFTPHandler;

        public OutputHandlerInitializer(IAPIHandler aPIHandler, ISFTPHandler sFTPHandler, IFTPHandler iFTPHandler)
        {
            _aPIHandler = aPIHandler;
            _iSFTPHandler = sFTPHandler;
            _iFTPHandler = iFTPHandler;
        }

        public dynamic OutputHandler(string data, int erpId, dynamic requestBody, WebHeaderCollection headers, ProcessorDetails processorDetails, dynamic inputModel)
        {
            MongoLogging.LogMessage(Constants.CallOutputHandlerMethodInOutputHandlerInitializerCalled, Constants.CallOutputHandler, TraceLevel.Info);
            string destination = GetOutputTransmissionType(erpId);
            bool isRealTime = false;

            switch (destination)
            {
                case "APIHandler":
                    HTTPConfigurationModel hTTPConfigurationModel = new HTTPConfigurationModel();
                    string schedulerType = GetSchedulerType(erpId);
                    if (schedulerType == "RealTime")
                    {
                        hTTPConfigurationModel = GetInputHandlerHTTPCredentials(erpId);
                        isRealTime = true;
                    }
                    else
                    {
                        hTTPConfigurationModel = GetOutputHandlerAPICredentials(erpId);
                    }
                    return _aPIHandler.APIOutputDataHandler(data, erpId, requestBody, headers, processorDetails, hTTPConfigurationModel, inputModel, isRealTime);
                case "SFTPHandler":
                    return _iSFTPHandler.SFTPOutputDataHandler(data, erpId, processorDetails);
                case "FTPHandler":
                    return _iFTPHandler.FTPOutputDataHandler(data, erpId, processorDetails);
                default:
                    return false;
            }
        }

        public string GetOutputTransmissionType(int erpId)
        {
            //Get Scheduler Type            
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            DataTable response = new DataTable();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeTransmissionConfigurartionModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ZnodeTransmissionConfigurartionModel>(response);
            //Get source or destination transmission mode based on scheduler type
            if (dataModelList.FirstOrDefault().SchedulerType == "RealTime")
                return dataModelList?.FirstOrDefault()?.SourceTransmissionMode;
            return dataModelList?.FirstOrDefault()?.DestinationTransmissionMode;
        }

        public HTTPConfigurationModel GetOutputHandlerAPICredentials(int erpId)
        {
            MongoLogging.LogMessage(Constants.GetOutputHandlerAPICredentials, Constants.APIHandler, TraceLevel.Info);
            string schedulerType = GetSchedulerType(erpId);
            List<HTTPConfigurationModel> sftpCredentials = new List<HTTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            if (schedulerType == "RealTime")
            {
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
            }
            else
            {
                sqlParameters.Add(new SqlParameter("@Type", Constants.Destination));
            }
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPITransmissionDetails", sqlParameters);
            List<HTTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<HTTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }

        public HTTPConfigurationModel GetInputHandlerHTTPCredentials(int erpId)
        {
            List<HTTPConfigurationModel> dataModelList;
            try
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsCalled, Constants.APIHandler, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPITransmissionDetails", sqlParameters);
                dataModelList = znodeViewRepository.ConvertDataTableToList<HTTPConfigurationModel>(response);
                return dataModelList?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsCalled + ex.Message, Constants.APIHandler, TraceLevel.Error);
                return new HTTPConfigurationModel();
            }
        }

        public string GetSchedulerType(int erpId)
        {
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            DataTable schedulerTypeTable = new DataTable();
            schedulerTypeTable = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeErpbaseDefinitionScheduler> schedulerType = znodeViewRepository.ConvertDataTableToList<ZnodeErpbaseDefinitionScheduler>(schedulerTypeTable);
            return schedulerType?.FirstOrDefault()?.SchedulerType;
        }
    }
}
