﻿using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using System;
using System.Data;
using System.Diagnostics;
using System.Text;
using YamlDotNet.Serialization;
using YamlDotNet.Serialization.NamingConventions;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Parser.Helper
{
    public class ParserDataHelper
    {
        private readonly AppDbContext _context;

        public ParserDataHelper(AppDbContext context)
        {
            _context = context;
        }

        public dynamic ReadTempTableToModel(string tempTableName)
        {
            try
            {
                var csvModel = new List<object>();

                using (var connection = new SqlConnection(HelperMethods.ConnectionString))
                {
                    connection.Open();

                    string query = $"SELECT * FROM {tempTableName}";

                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var rowDict = new Dictionary<string, string>();

                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                string columnName = reader.GetName(i);
                                string value = reader.IsDBNull(i) ? null : reader.GetValue(i)?.ToString();
                                rowDict[columnName] = value;
                            }

                            csvModel.Add(rowDict);
                        }
                    }
                }

                return csvModel;
            }
            catch
            {
                return new List<object>();
            }
        }

        public dynamic GetYAMLData(byte[] yamlFile)
        {
            string yamlString = Encoding.UTF8.GetString(yamlFile);

            // creates a YAML Deserializer object to convert YAML to .net object
            dynamic deserializer = new DeserializerBuilder().Build();

            dynamic yaml = deserializer.Deserialize<object>(yamlString);

            //convert the YAML file with source and destination columns to a json request body
            var json = JsonConvert.SerializeObject(yaml, Formatting.Indented);
            return json;
        }

        public virtual List<Dictionary<string, string>> ConvertTempTableDataToImport(List<object> csvModel, List<ZnodeImportTemplateMappingModel> importTemplate)
        {
            var importList = new List<Dictionary<string, string>>();
            try
            {
                var orderedTemplate = importTemplate.OrderBy(x => x.DisplayOrder).ToList();

                foreach (var csvRow in csvModel)
                {
                    if (csvRow is Dictionary<string, string> rowDict)
                    {
                        var mappedRow = new Dictionary<string, string>();

                        foreach (var mapping in orderedTemplate)
                        {
                            if (rowDict.TryGetValue(mapping.SourceColumnName, out var value))
                            {
                                mappedRow[mapping.TargetColumnName] = value;
                            }
                        }

                        importList.Add(mappedRow);
                    }
                }

                return importList;
            }
            catch
            {
                return importList;
            }
        }

        public string ConvertToCsv(List<Dictionary<string, string>> importList)
        {
            StringBuilder csvBuilder = new StringBuilder();

            // Handle headers (use keys from the first row dictionary)
            if (importList.Count > 0)
            {
                var headers = importList[0].Keys.ToList();
                csvBuilder.AppendLine(string.Join(",", headers));
            }

            // Handle rows
            foreach (var row in importList)
            {
                var rowValues = row.Values.Select(v => $"\"{v}\"").ToList(); // Add quotes around each value (to handle commas, etc.)
                csvBuilder.AppendLine(string.Join(",", rowValues));
            }

            return csvBuilder.ToString();
        }

        public string GetSFTPFormat(int erpId, string type)
        {
            try
            {
                MongoLogging.LogMessage("GetFormat method called in SFTP inputhandler ", "GetFormat", TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", type));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPFormat", sqlParameters);
                List<ZnodeSftpconfiguration> sftpConfigurationTable = znodeViewRepository.ConvertDataTableToList<ZnodeSftpconfiguration>(response);
                return sftpConfigurationTable?.FirstOrDefault().Format;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage("GetFormat method failed in SFTP inputhandler " + ex.Message, "GetFormat", TraceLevel.Info);
                return "";
            }
        }

        public string GetAPIFormat(int erpId, string type)
        {
            try
            {
                List<SqlParameter> schedulerTypeSqlParameters = new List<SqlParameter>();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                schedulerTypeSqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                DataTable schedulerTypeTable = new DataTable();
                schedulerTypeTable = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", schedulerTypeSqlParameters);
                List<ZnodeErpbaseDefinitionScheduler> schedulerType = znodeViewRepository.ConvertDataTableToList<ZnodeErpbaseDefinitionScheduler>(schedulerTypeTable);
                type = schedulerType.FirstOrDefault().SchedulerType == "RealTime" ? "Source" : type;

                MongoLogging.LogMessage("GetFormat method called in SFTP inputhandler ", "GetFormat", TraceLevel.Info);
                DataTable response = new DataTable();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", type));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPIFormat", sqlParameters);
                List<ZnodeApiconfiguration> apiConfigurationTable = znodeViewRepository.ConvertDataTableToList<ZnodeApiconfiguration>(response);
                return apiConfigurationTable?.FirstOrDefault().Format;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage("GetFormat method failed in SFTP inputhandler " + ex.Message, "GetFormat", TraceLevel.Info);
                return "";
            }
        }

        public string GetTransmissionType(int erpId)
        {
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            DataTable schedulerTypeTable = new DataTable();
            schedulerTypeTable = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeErpbaseDefinitionScheduler> schedulerType = znodeViewRepository.ConvertDataTableToList<ZnodeErpbaseDefinitionScheduler>(schedulerTypeTable);

            //Get source or destination transmission mode based on scheduler type
            DataTable response = new DataTable();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeTransmissionConfigurartionModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ZnodeTransmissionConfigurartionModel>(response);
            if (schedulerType.FirstOrDefault().SchedulerType == "RealTime")
                return dataModelList?.FirstOrDefault()?.SourceTransmissionMode;
            return dataModelList?.FirstOrDefault()?.DestinationTransmissionMode;
        }
    }
}
