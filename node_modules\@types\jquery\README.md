# Installation
> `npm install --save @types/jquery`

# Summary
This package contains type definitions for jquery (https://jquery.com).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jquery.

### Additional Details
 * Last updated: Wed, 23 Oct 2024 03:36:41 GMT
 * Dependencies: [@types/sizzle](https://npmjs.com/package/@types/sizzle)

# Credits
These definitions were written by [<PERSON>](https://github.com/leonard-thieu), [<PERSON>](https://github.com/b<PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/choffmeister), [<PERSON>](https://github.com/<PERSON>), [<PERSON><PERSON><PERSON>](https://github.com/<PERSON><PERSON>ei), [<PERSON><PERSON>](https://github.com/tasoili), [<PERSON>](https://github.com/seanski), [<PERSON><PERSON><PERSON>](https://github.com/Guuz), [<PERSON>](https://github.com/ksummerlin), [<PERSON><PERSON><PERSON>](https://github.com/basarat), [<PERSON>on](https://github.com/nwolverson), [<PERSON> <PERSON>ne](https://github.com/derekcicerone), [Andrew Gaspar](https://github.com/AndrewGaspar), [Seikichi Kondo](https://github.com/seikichi), [Benjamin Jackman](https://github.com/benjaminjackman), [Josh Strobl](https://github.com/JoshStrobl), [John Reilly](https://github.com/johnnyreilly), [Dick van den Brink](https://github.com/DickvdBrink), [Thomas Schulz](https://github.com/King2500), [Terry Mun](https://github.com/terrymun), [Martin Badin](https://github.com/martin-badin), and [Chris Frewin](https://github.com/princefishthrower).
