﻿using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.ViewModels;

namespace Znode.CommerceConnector.Core.IAgents
{
    public interface IManageDataExchangeAgent
    {
        BaseDefinitionViewModel GetDataExchangeDetailsByID(int id);

        bool SaveDataExchangeDetails(BaseDefinitionViewModel model);

        SchedulerConfigurationViewModel CheckValidation(SchedulerConfigurationViewModel schedulerModel, out bool status);

        BaseDefinitionViewModel GetBaseDefinitionDetailsByID(int erpId);

        bool TestConnection(string mode, string request);

        ConfigurationChangeLogListViewModel GetConfigurationChangeList(GridModel gridModel, int id);

        SchedulerConfigurationViewModel CheckSchedulerNameValidation(SchedulerConfigurationViewModel schedulerModel, out bool status);
    }
}
