﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Diagnostics.Eventing.Reader;
using System.Dynamic;
using System.IO;
using System.Runtime.InteropServices.JavaScript;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using System.Xml;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Parser.Helper;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Parser
{
    public class Parser : IParser
    {
        ParserDataHelper _parserDataHelper;

        public Parser(ParserDataHelper dataHelper)
        {
            _parserDataHelper = dataHelper;
        }

        //Method to parse csv data using mappings stored in YAML
        public dynamic ParseYAMLData(dynamic inputHandlerData, int erpId, dynamic model)
        {
            dynamic output = "";
            string format = "";

            try
            {
                MongoLogging.LogMessage(Constants.ParseYAMLDataMethodCalledinParser, Constants.ParseYAMLData, TraceLevel.Info);
                List<object> csvModel = (Convert.ToString((inputHandlerData)).Contains("tempdb"))? _parserDataHelper.ReadTempTableToModel(inputHandlerData) : new List<object>();
                dynamic yamlTemplateJson = _parserDataHelper.GetYAMLData(model);

                if (HelperUtility.IsNotNull(yamlTemplateJson))
                {
                    //Get destination format
                    string destinationTransmissionType = _parserDataHelper.GetTransmissionType(erpId);
                    switch (destinationTransmissionType)
                    {
                        case "SFTPHandler":
                            format = _parserDataHelper.GetSFTPFormat(erpId, Constants.Destination);
                            break;
                        case "APIHandler":
                            format = _parserDataHelper.GetAPIFormat(erpId, Constants.Destination);
                            break;
                        case "FTPHandler":
                            format = _parserDataHelper.GetSFTPFormat(erpId, Constants.Destination);
                            break;
                    }
                    switch (format)
                    {
                        case "CSV":
                            //CSV
                            MongoLogging.LogMessage(Constants.CSVMethodCalled, Constants.ParseYAMLData, TraceLevel.Info);
                            Dictionary<string, string> yamlTemplateModel = JsonConvert.DeserializeObject<Dictionary<string, string>>(yamlTemplateJson);
                            yamlTemplateModel = yamlTemplateModel.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Trim('{', '}'));
                            if (csvModel.Count > 0)
                            {
                                List<Dictionary<string, string>> importModel = ConvertTempTableDataToImport(csvModel, yamlTemplateModel);
                                output = _parserDataHelper.ConvertToCsv(importModel);
                            }
                            else
                            {
                                JObject jsonObject = JObject.Parse(inputHandlerData);
                                Dictionary<string, object> keyValuePairs = ExtractKeyValuePairs(jsonObject);
                                List<Dictionary<string, string>> importModel = ConvertTempTableDataToImport(keyValuePairs, yamlTemplateModel);
                                output = _parserDataHelper.ConvertToCsv(importModel);
                            }
                            break;
                        case "JSON":
                            //JSON
                            MongoLogging.LogMessage(Constants.JSONMethodCalled, Constants.ParseYAMLData, TraceLevel.Info);
                            dynamic jsonTemplateModel = JsonConvert.DeserializeObject<ExpandoObject>(yamlTemplateJson, new ExpandoObjectConverter());
                            IDictionary<string, object> dict = (IDictionary<string, object>)jsonTemplateModel;
                            var newdict = ((IDictionary<string, object>)jsonTemplateModel).ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString());
                            var results = new List<object>();
                            if (csvModel.Count > 0)
                            {
                                string csvJson = JsonConvert.SerializeObject(csvModel);
                                var jsonObj = JsonConvert.DeserializeObject<List<ExpandoObject>>(csvJson, new ExpandoObjectConverter());
                                var stringDictList = jsonObj.Select(obj => ((IDictionary<string, object>)obj).ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString() ?? string.Empty)).ToList();
                                var outputList = new List<object>();

                                foreach (var singleDict in stringDictList)
                                {
                                    dynamic jsonInstance = JsonConvert.DeserializeObject<ExpandoObject>(yamlTemplateJson, new ExpandoObjectConverter());

                                    ReplaceValuesRecursive(jsonInstance, singleDict);
                                    outputList.Add(jsonInstance);
                                }
                                output = JsonConvert.SerializeObject(outputList, Newtonsoft.Json.Formatting.Indented);
                            }
                            else
                            {
                                //
                                var jsonDoc = JsonDocument.Parse(yamlTemplateJson);
                                bool hasArray = ContainsArray(jsonDoc.RootElement);
                                if (hasArray)
                                {
                                    JObject jsonObject = JObject.Parse(inputHandlerData);
                                    Dictionary<string, object> keyValuePairs = ExtractKeyValuePairs(jsonObject);

                                    var outputList = new List<object>();
                                    foreach (var kvp in keyValuePairs)
                                    {
                                        // Expecting kvp.Value to be Dictionary<string, object>
                                        var outputDict = new Dictionary<string, Dictionary<string, object>>{{ kvp.Key, kvp.Value as Dictionary<string, object> }};

                                        dynamic json = JsonConvert.DeserializeObject<ExpandoObject>(yamlTemplateJson, new ExpandoObjectConverter());
                                        ReplaceJsonValuesRecursive(json, outputDict);
                                        outputList.Add(json);
                                    }

                                    output = JsonConvert.SerializeObject(outputList, Newtonsoft.Json.Formatting.Indented);
                                }
                                else
                                {
                                    var keys = new List<string>(newdict.Keys);
                                    foreach (var key in keys)
                                    {
                                        if (newdict[key] is string strValue)
                                        {
                                            newdict[key] = strValue.Trim('{', '}');
                                        }
                                    }
                                    output = Transform(inputHandlerData, newdict);
                                }
                            }
                            break;
                        case "XML":
                            //XML
                            MongoLogging.LogMessage(Constants.XMLMethodCalled, Constants.ParseYAMLData, TraceLevel.Info);

                            using (StringWriter writer = new StringWriter())
                            {
                                string json = JsonConvert.SerializeObject(yamlTemplateJson);
                                XmlDocument doc = JsonConvert.DeserializeXmlNode(json, "XMLFile");
                            }
                            break;
                    }
                    return output;
                }
                MongoLogging.LogMessage(Constants.TemplateNotFound, Constants.ParseYAMLData, TraceLevel.Info);
                return "";
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.ParseYAMLDataMethodFailedinParser + ex.Message, Constants.ParseYAMLData, TraceLevel.Error);
                return output;
            }
        }

        public static List<Dictionary<string, string>> MapJson(string json, Dictionary<string, string> template)
        {
            var jToken = JToken.Parse(json);
            List<Dictionary<string, string>> result = new();

            bool isArrayMapping = false;
            string arrayPath = null;

            foreach (var kvp in template)
            {
                if (kvp.Value.Contains("[]"))
                {
                    isArrayMapping = true;
                    arrayPath = kvp.Value.Substring(1, kvp.Value.IndexOf("[]") - 1);
                        break;
                    }
                }

            if (isArrayMapping)
            {
                JToken arrayToken = jToken.SelectToken(arrayPath);
                if (arrayToken is JArray arr)
                {
                    foreach (var item in arr)
                    {
                        var dict = new Dictionary<string, string>();
                        foreach (var kvp in template)
                {
                            string val = kvp.Value;

                            if (val.Contains("[]"))
                    {
                                string path = val.Replace(arrayPath + "[]", "").TrimStart('.');
                                var token = string.IsNullOrEmpty(path) ? item : item.SelectToken(path);
                                if (token == null || token.Type == JTokenType.Null || token.Type == JTokenType.Undefined)
                                {
                                    dict[kvp.Key] = val;  // fallback to template string itself
                    }
                                else
                    {
                                    dict[kvp.Key] = token.ToString();
                                }
                            }
                            else if (val.StartsWith("{") && val.EndsWith("}"))
                        {
                                string path = val.Trim('{', '}');
                                var token = jToken.SelectToken(path);
                                if (token == null || token.Type == JTokenType.Null || token.Type == JTokenType.Undefined)
                            {
                                    dict[kvp.Key] = val;  // fallback to template string itself
                            }
                                else
                                {
                                    dict[kvp.Key] = token.ToString();
                        }
                    }
                            else
                            {
                                dict[kvp.Key] = val;
                }
            }
                        result.Add(dict);
        }
                }
            }
            else
        {
                var dict = new Dictionary<string, string>();
                foreach (var kvp in template)
                {
                    string val = kvp.Value;

                    if (val.StartsWith("{") && val.EndsWith("}"))
        {
                        string path = val.Trim('{', '}');
                        var token = jToken.SelectToken(path);
                        if (token == null || token.Type == JTokenType.Null || token.Type == JTokenType.Undefined)
            {
                            dict[kvp.Key] = val;
                        }
                        else
                    {
                            dict[kvp.Key] = token.ToString();
                    }
                    }
                    else
                    {
                        dict[kvp.Key] = val;
                    }
            }
                result.Add(dict);
        }

            return result;
        }

        public static Dictionary<string, object> ExtractKeyValuePairs(JToken token, string parentKey = "")
        {
            var result = new Dictionary<string, object>();

            if (token.Type == JTokenType.Object)
            {
                foreach (var property in token.Children<JProperty>())
                {
                    string key = string.IsNullOrEmpty(parentKey) ? property.Name : $"{property.Name}";
                    var childPairs = ExtractKeyValuePairs(property.Value, key);
                    foreach (var childPair in childPairs)
                    {
                        result[childPair.Key] = childPair.Value;
                    }
                }
            }
            else if (token.Type == JTokenType.Array)
            {
                int index = 0;
                foreach (var arrayItem in token.Children())
                {
                    string key = $"{parentKey}[{index}]";
                    result[key] = ExtractKeyValuePairs(arrayItem);
                    index++;
                }
            }
            else
            {
                result[parentKey] = token.ToString();
            }

            return result;
        }

        public static string Transform(string inputJson, Dictionary<string, string> mappings)
        {
            var input = JObject.Parse(inputJson);
            var outputArray = new JArray();

            // Separate mappings into array groups and static mappings (no arrays)
            var arrayMappings = mappings
                .Where(m => m.Value.Contains("[]"))
                .GroupBy(m => GetArrayPrefix(m.Value))
                .ToList();

            var staticMappings = mappings
                .Where(m => !m.Value.Contains("[]"))
                .ToList();

            // Process array groups
            foreach (var group in arrayMappings)
            {
                var sourceArrayPath = group.Key;
                var sourceArray = input.SelectToken(sourceArrayPath) as JArray;

                if (sourceArray == null)
                    continue;

                for (int i = 0; i < sourceArray.Count; i++)
                {
                    var outputItem = new JObject();

                    // Set array based dynamic mappings
                    foreach (var mapping in group)
                    {
                        var sourcePath = mapping.Value.Replace("[]", $"[{i}]");
                        var destPath = mapping.Key.Replace("root[]", "").TrimStart('.');

                        var value = input.SelectToken(sourcePath);
                        if (value != null)
                            SetValueByPath(outputItem, destPath, value.DeepClone());
                    }

                    // Add static mappings to each item
                    foreach (var staticMapping in staticMappings)
                    {
                        var destPath = staticMapping.Key.Replace("root[]", "").TrimStart('.');
                        var valueToken = input.SelectToken(staticMapping.Value);

                        if (valueToken != null)
                            SetValueByPath(outputItem, destPath, valueToken.DeepClone());
                        else if (TryParseLiteral(staticMapping.Value, out JToken literalValue))
                            SetValueByPath(outputItem, destPath, literalValue);
                        else
                            SetValueByPath(outputItem, destPath, staticMapping.Value);
                    }

                    outputArray.Add(outputItem);
                }
            }

            // If no array mappings, output one object with static mappings
            if (!arrayMappings.Any() && staticMappings.Any())
            {
                var outputItem = new JObject();
                foreach (var staticMapping in staticMappings)
                {
                    var destPath = staticMapping.Key.Replace("root[]", "").TrimStart('.');

                    //if (TryParseLiteral(staticMapping.Value, out JToken literalValues))
                    //{
                    //    SetValueByPath(outputItem, destPath, literalValues);
                    //    continue;
                    //}
                    var valueToken = input.SelectToken(staticMapping.Value);

                    if (valueToken != null)
                        SetValueByPath(outputItem, destPath, valueToken.DeepClone());
                    else if (TryParseLiteral(staticMapping.Value, out JToken literalValue))
                        SetValueByPath(outputItem, destPath, literalValue);
                    else
                        SetValueByPath(outputItem, destPath, staticMapping.Value);
                }
                outputArray.Add(outputItem);
            }
            string output = JsonConvert.SerializeObject(outputArray, Newtonsoft.Json.Formatting.Indented);
            if (output.StartsWith("[") && output.EndsWith("]"))
            {
                output = output.Substring(1, output.Length - 2).Trim();
            }

            return output;
        }

        private static bool TryParseLiteral(string input, out JToken token)
        {
            token = null;
            if (input.Contains(""))
            {
                token = new JValue(input);
                return true;
            }
            if (int.TryParse(input, out int intVal))
            {
                token = new JValue(intVal);
                return true;
            }
            if (bool.TryParse(input, out bool boolVal))
            {
                token = new JValue(boolVal);
                return true;
            }
            if (!string.IsNullOrEmpty(input) && input.StartsWith("\"") && input.EndsWith("\""))
            {
                // Remove quotes and treat as string
                token = new JValue(input.Trim('\"'));
                return true;
            }
            if (string.Equals(input, "null", StringComparison.OrdinalIgnoreCase))
            {
                token = JValue.CreateNull();
                return true;
            }

            if (input.Length > 1)
            {
                token = new JValue(input);
                return true;
            }

            return false;
        }

        private static string GetArrayPrefix(string path)
        {
            var match = Regex.Match(path, @"^(.*?)\[\]");
            return match.Success ? match.Groups[1].Value : path;
        }

        private static void SetValueByPath(JObject root, string path, JToken value)
        {
            var parts = path.Split('.');
            JObject current = root;

            for (int i = 0; i < parts.Length; i++)
            {
                string part = parts[i];
                if (i == parts.Length - 1)
                {
                    current[part] = value;
                }
                else
                {
                    if (current[part] == null || !(current[part] is JObject))
                        current[part] = new JObject();

                    current = (JObject)current[part];
                }
            }
        }

        public virtual List<Dictionary<string, string>> ConvertTempTableDataToImport(List<object> csvModel, Dictionary<string, string> yamlTemplateModel)
        {
            var importList = new List<Dictionary<string, string>>();
            try
            {
                foreach (var csvRow in csvModel)
                {
                    if (csvRow is Dictionary<string, string> rowDict)
                    {
                        var mappedRow = new Dictionary<string, string>();

                        foreach (var mapping in yamlTemplateModel)
                        {
                            if (rowDict.TryGetValue(mapping.Value, out var value))
                            {
                                mappedRow[mapping.Key.Replace("root[].", "")] = value;
                            }
                            else
                            {
                                mappedRow[mapping.Key.Replace("root[].", "")] = mapping.Value;
                            }
                        }

                        importList.Add(mappedRow);
                    }
                }

                return importList;
            }
            catch
            {
                return importList;
            }
        }

        public virtual List<Dictionary<string, string>> ConvertTempTableDataToImport(Dictionary<string, object> csvModel, Dictionary<string, string> yamlTemplateModel)
        {
            var importList = new List<Dictionary<string, string>>();

            try
            {
                List<Dictionary<string, object>> dataRows = ExtractDataRows(csvModel, yamlTemplateModel);

                foreach (var row in dataRows)
                {
                    var mappedRow = new Dictionary<string, string>();

                    foreach (var map in yamlTemplateModel)
                    {
                        var targetKey = map.Key.Replace("root[].", "");
                        var sourceKey = "";
                        if (map.Value.Contains("[]"))
                        {
                            int lastDotIndex = map.Value.LastIndexOf('.');
                            var placeholderKey = (lastDotIndex == -1 ? map.Value : map.Value.Substring(lastDotIndex + 1)).Trim('{', '}');
                            sourceKey = placeholderKey;
                        }
                        else
                        {
                            sourceKey = map.Value;
                        }

                        string value = string.Empty;

                        // Try to get value from row
                        if (!string.IsNullOrEmpty(sourceKey) && row.TryGetValue(sourceKey, out var rowVal))
                        {
                            value = rowVal?.ToString() ?? string.Empty;
                            mappedRow[targetKey] = value;
                        }
                        else if (csvModel.TryGetValue(sourceKey, out var csvVal))
                        {
                            value = csvVal?.ToString() ?? string.Empty;
                            mappedRow[targetKey] = value;
                        }
                        else if (csvModel.Any((x => x.Key == map.Value)))
                        {
                            mappedRow[targetKey] = csvModel.First(x => x.Key == map.Value).Value.ToString();
                        }
                        else
                        {
                            mappedRow[targetKey] = sourceKey;
                        }
                    }

                    importList.Add(mappedRow);

                    if (!(yamlTemplateModel.Any(x => x.Value.Contains("[]"))))
                    {
                        break;
                    }

                    List<Dictionary<string, string>> duplicates = importList.GroupBy(d => string.Join("|", d.OrderBy(kv => kv.Key).Select(kv => $"{kv.Key}:{kv.Value}"))).Where(g => g.Count() > 1).SelectMany(g => g).ToList();
                    if (duplicates.Count > 0)
                    {
                        importList = new List<Dictionary<string, string>> { importList[0] };
                        break;
                    }
                }

                return importList;
            }
            catch
            {
                return importList;
            }
        }

        public dynamic ConvertJsonToDataTable(string json)
        {

            var jObject = JObject.Parse(json);
            Dictionary<string, object> dict = jObject.ToObject<Dictionary<string, object>>();

            JArray array;

            if (jObject["value"] is JArray valueArray)
            {
                array = valueArray;
            }
            else
            {
                var valueProps = jObject.Properties()
                    .Where(p => p.Name.StartsWith("value["))
                    .OrderBy(p => p.Name)
                    .Select(p => p.Value)
                    .ToArray();

                array = new JArray(valueProps);
            }

            return array.ToString();
        }

        private List<Dictionary<string, object>> ExtractDataRows(Dictionary<string, object> input, Dictionary<string, string> yamlTemplateModel)
        {
            var rows = new List<Dictionary<string, object>>();
            var seenLists = new HashSet<List<object>>();

            foreach (var kv in input)
            {
                if (kv.Value is List<object> list)
                {
                    // Ensure we don't process the same list again
                    if (!seenLists.Contains(list))
                    {
                        seenLists.Add(list);

                        foreach (var item in list)
                        {
                            if (item is Dictionary<string, object> dict)
                            {
                                rows.Add(dict);
                            }
                        }
                    }
                }
                else if (kv.Value is Dictionary<string, object> dict)
                {
                    rows.Add(dict);
                }
            }

            return rows;
        }

        public static void ReplaceValuesRecursive(dynamic json, Dictionary<string, string> csvModel)
        {
            if (json is ExpandoObject)
            {
                IDictionary<string, object> dict = (IDictionary<string, object>)json;
                foreach (var key in dict.Keys.ToList())
                {
                    var value = dict[key];

                    if (value is string strVal)
                    {
                        if (strVal.StartsWith("{") && strVal.EndsWith("}"))
                        {
                            var placeholderKey = strVal.Trim('{', '}');
                            if (csvModel.ContainsKey(placeholderKey))
                            {
                                dict[key] = csvModel[placeholderKey];
                            }
                        }
                        else if (csvModel.ContainsKey(strVal))
                        {
                            dict[key] = csvModel[strVal];
                        }
                    }
                    else
                    {
                        ReplaceValuesRecursive(value, csvModel);
                    }
                }
            }
            else if (json is IEnumerable<object> list)
            {
                foreach (var item in list)
                {
                    ReplaceValuesRecursive(item, csvModel);
                }
            }
        }

        public bool ContainsArray(JsonElement element)
        {
            if (element.ValueKind == JsonValueKind.Array)
                return true;

            if (element.ValueKind == JsonValueKind.Object)
            {
                foreach (var property in element.EnumerateObject())
                {
                    if (ContainsArray(property.Value))
                        return true;
                }
            }

            return false;
        }

        public static Dictionary<string, string> ConvertToStringDictionary(Dictionary<string, object> dict)
        {
            var result = new Dictionary<string, string>();

            foreach (var kvp in dict)
            {
                if (kvp.Value == null)
                {
                    result[kvp.Key] = string.Empty;
                }
                else if (kvp.Value is JObject jObj)
                {
                    // Serialize nested JObject to JSON string
                    result[kvp.Key] = jObj.ToString(Newtonsoft.Json.Formatting.None);
                }
                else if (kvp.Value is JArray jArr)
                {
                    // Serialize array to JSON string
                    result[kvp.Key] = jArr.ToString(Newtonsoft.Json.Formatting.None);
                }
                else if (kvp.Value is Dictionary<string, object> nestedDict)
                {
                    // Serialize nested dictionary to JSON string
                    result[kvp.Key] = JsonConvert.SerializeObject(nestedDict);
                }
                else
                {
                    // Convert primitive values to string
                    result[kvp.Key] = kvp.Value.ToString();
                }
            }

            return result;
        }

        public static void ReplaceJsonValuesRecursive(dynamic json, Dictionary<string, Dictionary<string, object>> csvModel)
        {
            if (json is ExpandoObject)
            {
                var dict = (IDictionary<string, object>)json;
                foreach (var key in dict.Keys.ToList())
                {
                    var value = dict[key];
                    if (value is string strVal)
                    {
                        if (strVal.StartsWith("{") && strVal.EndsWith("}"))
                        {
                            int lastDotIndex = strVal.LastIndexOf('.');
                            var placeholderKey = (lastDotIndex == -1 ? strVal : strVal.Substring(lastDotIndex + 1)).Trim('{', '}');
                            foreach (var nestedDict in csvModel.Values)
                            {
                                if (HelperUtility.IsNotNull(nestedDict))
                                {
                                    if (nestedDict.TryGetValue(placeholderKey, out var replacement))
                                    {
                                        dict[key] = replacement;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    else
                    {
                        ReplaceJsonValuesRecursive(value, csvModel);
                    }
                }
            }
            else if (json is IEnumerable<object> list)
            {
                foreach (var item in list)
                {
                    ReplaceJsonValuesRecursive(item, csvModel);
                }
            }
        }

    }
}
