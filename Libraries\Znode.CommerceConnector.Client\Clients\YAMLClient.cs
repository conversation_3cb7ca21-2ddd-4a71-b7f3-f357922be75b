﻿using System.Text;
using Newtonsoft.Json;
using Znode.CommerceConnector.Client.Endpoints;
using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Models;

namespace Znode.CommerceConnector.Client.Clients
{
    public class YAMLClient : IYAMLClient
    {       
        public dynamic DownloadSample(MediaConfigurationModel mediaConfiguration)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.DownloadSample();
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(mediaConfiguration);
            dynamic response = apiClient.PostRequest<dynamic>(requestModel, out string status);
            byte[] bytes = Encoding.ASCII.GetBytes(response);
            return bytes;
        }

        public dynamic SaveYaml(YAMLSaveRequestModel content)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.SaveYaml();
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(content);
            TrueFalseResponse response = apiClient.PostRequest<TrueFalseResponse>(requestModel, out string statusCode);
            return response.IsSuccess;
        }

        public dynamic GetSavedYamlData(int erpId, MediaConfigurationModel mediaConfiguration)
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetSavedYamlData(erpId);
            RequestModel requestModel = apiClient.SetRequestModel(endpoint);
            requestModel.RequestBody = JsonConvert.SerializeObject(mediaConfiguration);
            dynamic response = apiClient.PostRequest<dynamic>(requestModel, out string statusCode);
            return response;

        }

        public MediaConfigurationModel GetMediaConfiguration()
        {
            ApiClient apiClient = new ApiClient();
            string endpoint = ExchangeLibraryEndpoint.GetDefaultMediaConfiguration();
            MediaConfigurationResponse response = apiClient.GetRequest<MediaConfigurationResponse>(endpoint);
            return response.MediaConfiguration;
        }
    }
}
