﻿using Microsoft.Data.SqlClient;

using System.Data;
using System.Diagnostics;
using System.Text;

using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler.HandlerHelper
{
    public class HandlerDataHelper : IHandlerDataHelper
    {
        public List<string> CreateTempTableWithData(DataSet tablesToInsert)
        {
            Guid tableGuid = Guid.NewGuid();

            if (IsNull(tablesToInsert) || tablesToInsert.Tables.Count <= 0)
            {
                return new List<string>();
            }

            List<string> tablesNames = new List<string>();
            string tableName = string.Empty;
            try
            {
                foreach (DataTable tableToinsert in tablesToInsert.Tables)
                {
                    if (IsNull(tableToinsert) || tableToinsert.Rows.Count <= 0)
                    {
                        continue;
                    }

                    tableName = CreateTempTable(tableToinsert, tableGuid);

                    InsertDataIntoTempTable(tableToinsert, tableName);
                    tablesNames.Add(tableName);
                }
            }
            catch (Exception ex)
            {
                return new List<string>();
            }
            return tablesNames;
        }

        public string CreateTempTable(DataTable tableToinsert, Guid tableGuid)
        {
            string tableName = $"tempdb..[##{tableToinsert.TableName}_{tableGuid}]";
            SqlConnection conn = GetSqlConnection();
            SqlCommand cmd = new SqlCommand("CREATE TABLE " + tableName + "  " + GenerateTableColumns(tableToinsert) + " ", conn);
            if (conn.State.Equals(ConnectionState.Closed))
            {
                conn.Open();
            }
            cmd.ExecuteNonQuery();

            return tableName;
        }

        public string GenerateTableColumns(DataTable tableDump)
        {
            if (tableDump == null || tableDump.Columns.Count == 0)
                return string.Empty;

            StringBuilder tableColumns = new StringBuilder();
            tableColumns.Append("(");

            var columnDefs = tableDump.Columns
                .Cast<DataColumn>()
                .Select(col => $"[{col.ColumnName}] nvarchar(max)");

            tableColumns.Append(string.Join(", ", columnDefs));
            tableColumns.Append(")");

            return tableColumns.ToString();
        }

        public void InsertDataIntoTempTable(DataTable tableToInsert, string tableName)
        {
            try
            {
                if (IsNotNull(tableToInsert) && tableToInsert.Rows?.Count > 0)
                {
                    int chunkSize = int.Parse(HelperMethods.ZnodeImportChunkLimit);
                    int startIndex = 0;
                    int totalRows = tableToInsert.Rows.Count;
                    int totalRowsCount = totalRows / chunkSize;

                    if (totalRows % chunkSize > 0)
                    {
                        totalRowsCount++;
                    }

                    for (int iCount = 0; iCount < totalRowsCount; iCount++)
                    {
                        DataTable fileData = tableToInsert.Rows.Cast<DataRow>().Skip(startIndex).Take(chunkSize).CopyToDataTable();
                        startIndex = startIndex + chunkSize;
                        InsertData(tableName, fileData);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in HandlerDataHelper.InsertDataIntoTempTable {ex.Message}");
            }
        }

        public void InsertData(string tableName, DataTable fileData)
        {
            SqlConnection conn = GetSqlConnection();
            try
            {
                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(conn))
                {
                    bulkCopy.DestinationTableName = tableName;

                    if (conn.State.Equals(ConnectionState.Closed))
                    {
                        conn.Open();
                    }

                    bulkCopy.WriteToServer(fileData);
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in HandlerDataHelper.InsertData {ex.Message}");
            }
            finally
            {
                if (conn.State.Equals(ConnectionState.Open))
                {
                    conn.Close();
                }
            }
        }

        public SqlConnection GetSqlConnection()
        {
            return new SqlConnection(HelperMethods.ConnectionString);
        }

        public static bool IsNull(object value)
        {
            return object.Equals(value, null);
        }

        public static bool IsNotNull(object value)
        {
            return !object.Equals(value, null);
        }

        public string GetInputTransmissionType(int erpId)
        {
            MongoLogging.LogMessage("GetTransmissionType method in InputHandlerInitializer called", "GetTransmissionType", TraceLevel.Info);

            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeTransmissionConfigurartionModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ZnodeTransmissionConfigurartionModel>(response);
            return dataModelList?.FirstOrDefault()?.SourceTransmissionMode;

        }

        public string GetSFTPFormat(int erpId, string type)
        {
            try
            {
                MongoLogging.LogMessage(Constants.GetFormatCalled, Constants.HandlerDataHelper, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", type));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPFormat", sqlParameters);
                List<ZnodeSftpconfiguration> sftpConfigurationTable = znodeViewRepository.ConvertDataTableToList<ZnodeSftpconfiguration>(response);
                return sftpConfigurationTable?.FirstOrDefault().Format;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetFormatFailed + ex.Message, Constants.HandlerDataHelper, TraceLevel.Info);
                return "";
            }
        }

        public string GetAPIFormat(int erpId, string type)
        {
            try
            {
                MongoLogging.LogMessage(Constants.GetAPIFormatCalled, Constants.HandlerDataHelper, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", type));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPIFormat", sqlParameters);
                List<ZnodeApiconfiguration> apiConfigurationTable = znodeViewRepository.ConvertDataTableToList<ZnodeApiconfiguration>(response);
                return apiConfigurationTable?.FirstOrDefault().Format;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetAPIFormatFailed + ex.Message, Constants.HandlerDataHelper, TraceLevel.Error);
                return "";
            }
        }

        public string GetYAMLFileNameByErpId(int erpId)
        {
            try
            {
                MongoLogging.LogMessage(Constants.GetYAMLFileNameByErpIdCalled, Constants.HandlerDataHelper, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetYAMLFileNameFromBaseDefinition", sqlParameters);
                List<ZnodeErpbaseDefinition> dataModelList = znodeViewRepository.ConvertDataTableToList<ZnodeErpbaseDefinition>(response);
                return dataModelList?.FirstOrDefault()?.YamlfileName;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetYAMLFileNameByErpIdFailed, Constants.HandlerDataHelper, TraceLevel.Info);
                return "";
            }
        }

        public HTTPConfigurationModel GetInputHandlerHTTPCredentials(int erpId)
        {
            List<HTTPConfigurationModel> dataModelList;
            try
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsCalledInAPIInputHandler, Constants.APIDataHandler, TraceLevel.Info);
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPITransmissionDetails", sqlParameters);
                dataModelList = znodeViewRepository.ConvertDataTableToList<HTTPConfigurationModel>(response);
                return dataModelList?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.GetInputHandlerHTTPCredentialsFailedInAPIInputHandler + ex.Message, Constants.APIDataHandler, TraceLevel.Error);
                return new HTTPConfigurationModel();
            }
        }

        public SFTPConfigurationModel GetInputHandlerSFTPCredentials(int erpId)
        {
            try
            {
                MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerCalled, Constants.SFTPHandler, TraceLevel.Info);
                List<SFTPConfigurationModel> sftpCredentials = new List<SFTPConfigurationModel>();
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
                List<SFTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<SFTPConfigurationModel>(response);
                MongoLogging.LogMessage("GetSFTPCredentials : " + dataModelList?.FirstOrDefault(), Constants.SFTPHandler, TraceLevel.Info);
                return dataModelList?.FirstOrDefault();
            }
            catch
            {
                MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerFailed, Constants.SFTPHandler, TraceLevel.Info);
                return new SFTPConfigurationModel();
            }
        }

        public FTPConfigurationModel GetInputHandlerFTPCredentials(int erpId)
        {
            List<FTPConfigurationModel> FTPCredentials = new List<FTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPCredentialsByERPId", sqlParameters);
            List<FTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<FTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }

        public string GetOutputTransmissionType(int erpId)
        {
            //Get Scheduler Type            
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            DataTable response = new DataTable();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeTransmissionConfigurartionModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ZnodeTransmissionConfigurartionModel>(response);
            //Get source or destination transmission mode based on scheduler type
            if (dataModelList.FirstOrDefault().SchedulerType == "RealTime")
                return dataModelList?.FirstOrDefault()?.SourceTransmissionMode;
            return dataModelList?.FirstOrDefault()?.DestinationTransmissionMode;
        }

        public string GetSchedulerType(int erpId)
        {
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            DataTable schedulerTypeTable = new DataTable();
            schedulerTypeTable = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeErpbaseDefinitionScheduler> schedulerType = znodeViewRepository.ConvertDataTableToList<ZnodeErpbaseDefinitionScheduler>(schedulerTypeTable);
            return schedulerType?.FirstOrDefault()?.SchedulerType;
        }

        public HTTPConfigurationModel GetOutputHandlerAPICredentials(int erpId)
        {
            //MongoLogging.LogMessage(Constants.GetOutputHandlerAPICredentials, Constants.APIHandler, TraceLevel.Info);
            List<HTTPConfigurationModel> sftpCredentials = new List<HTTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Destination));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetAPITransmissionDetails", sqlParameters);
            List<HTTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<HTTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }

        public SFTPConfigurationModel GetOutputHandlerSFTPCredentials(int erpId)
        {
            //MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerCalled, Constants.SFTPOutputDataHandler, TraceLevel.Info);
            List<SFTPConfigurationModel> sftpCredentials = new List<SFTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Destination));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
            List<SFTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<SFTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }

        public FTPConfigurationModel GetOutputHandlerFTPCredentials(int erpId)
        {
            MongoLogging.LogMessage("GetFTPCredentials method in FTPHandler called", "FTPHandler", TraceLevel.Info);
            List<FTPConfigurationModel> FTPCredentials = new List<FTPConfigurationModel>();
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@Type", Constants.Destination));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
            List<FTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<FTPConfigurationModel>(response);
            return dataModelList?.FirstOrDefault();
        }
    }
}
