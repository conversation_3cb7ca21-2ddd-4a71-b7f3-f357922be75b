﻿using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;
using Znode.CommerceConnector.API.Services;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.Model.Models;

namespace Znode.CommerceConnector.API.Controllers
{
    public class RealTimeDataController : BaseController
    {
        private readonly IRealTimeDataExchangeService _realTimeDataExchangeService;

        public RealTimeDataController(IRealTimeDataExchangeService realTimeDataExchangeService)
        {
            _realTimeDataExchangeService = realTimeDataExchangeService;
        }

        [Route("CallRealTimeDataExchange")]
        [HttpPost]
        public IActionResult CallRealTimeDataExchange([FromQuery] int erpId, [FromBody] dynamic inputModel)
        {
            IActionResult response;
            try
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.CallRealTimeDataExchangeControllerCalled, CommerceConnectorConstants.APIController, TraceLevel.Info);
                dynamic data = _realTimeDataExchangeService.RealTimeDataExchange(erpId, inputModel);
                response = HelperUtility.IsNotNull(data) ? Ok(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.CallRealTimeDataExchangeControllerFailed, CommerceConnectorConstants.APIController, TraceLevel.Error);
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }
    }
}
