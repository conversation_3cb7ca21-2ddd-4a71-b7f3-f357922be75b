﻿@using Znode.CommerceConnector.Core.Helper;
@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel

@{
    string displayStyle = Model.SchedulerConfigurationViewModel.SchedulerType?.ToLower() == CCAdminConstant.OnDemand.ToLower() ? "display:none" : string.Empty;
}

<div class="mt-2">
    <div class="scheduler-status">
        <div class="row">
            <div class="col-md-12 col-lg-6 mb-3">
                <div class="form-group">
                    <div class="control-label mb-2">
                        @Html.LabelFor(model => model.SchedulerConfigurationViewModel.SchedulerType, new { @data_test_selector = "lblSchedulerType" })
                    </div>
                    <div class="control-md">
                        @Html.DropDownListFor(model => model.SchedulerConfigurationViewModel.SchedulerType, Model.SchedulerConfigurationViewModel.ScheduleTypeList, new { @id = "ScheduleType", @class = "form-select", aria_label = "Scheduler Type" })
                        @Html.ValidationMessageFor(model => model.SchedulerConfigurationViewModel.SchedulerType, "", new { @id = "valSchedulerType", @data_test_selector = "valSchedulerType" })
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                <div class="form-group">
                    <div class="control-label mb-2">
                        @Html.LabelFor(model => model.SchedulerConfigurationViewModel.SchedulerName, new { @class = "required", @data_test_selector = "lblSchedulerName" })
                    </div>
                    <div class="control-md">
                        @if (Model.SchedulerConfigurationViewModel.ERPBaseDefinitionId == 0 || string.IsNullOrWhiteSpace(Model.SchedulerConfigurationViewModel.SchedulerName))
                        {
                            @Html.TextBoxFor(model => model.SchedulerConfigurationViewModel.SchedulerName, new { @id = "schedulerName", @class = "form-control", @data_test_selector = "txtSchedulerName", @maxlength = 100, aria_label = "Scheduler Name" })
                        }
                        else
                        {
                            @Html.TextBoxFor(model => model.SchedulerConfigurationViewModel.SchedulerName, new { @id = "schedulerName", Readonly = "true", @class = "form-control", @data_test_selector = "txtSchedulerName", @maxlength = 100, aria_label = "Scheduler Name" })
                        }
                        @Html.ValidationMessageFor(model => model.SchedulerConfigurationViewModel.SchedulerName, "", new { @id = "valSchedulerName", @data_test_selector = "valSchedulerName" })
                        <span class="text-danger field-validation-error" id="SchedulerNameError" data-test-selector="spnSchedulerName" aria-label="Scheduler Name Error"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="py-2" id="schedulerSetting" style="@displayStyle">
            @Html.Partial("_SchedulerSetting", Model)
        </div>

    </div>
</div>
@Html.HiddenFor(model => model.SchedulerConfigurationViewModel.ERPBaseDefinitionSchedulerId)
@Html.HiddenFor(model => model.SchedulerConfigurationViewModel.ERPBaseDefinitionId)
@Html.HiddenFor(model => model.SchedulerConfigurationViewModel.LastRunTime)
