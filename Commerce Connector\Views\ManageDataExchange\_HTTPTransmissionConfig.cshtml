﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources
@using Znode.Libraries.ECommerce.Utilities;


<div class="mt-3">
    @Html.HiddenFor(model => model.ERPBaseDefinitionId)
    @Html.HiddenFor(Model => Model.TransmissionConfigurations.TransmissionConfigurationId)
    @Html.HiddenFor(Model => Model.TransmissionConfigurations.HttpsConfig.APIConfigurationId)
    <input type="hidden" asp-for="ERPBaseDefinitionId" />
    <div class="row">
        <div class="col-md-12 col-lg-6 mb-3" id="DivSourceHTTPAction">
            <label asp-for="TransmissionConfigurations.HttpsConfig.HttpAction" class="form-label" data-test-selector="lblHttpAction" aria-label="Http Action"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.HttpsConfig.HttpAction" class="form-select" id="SourceHTTPAction" data-test-selector="drpHttpAction" aria-label="Source API method">
                <option value="">@CommerceConnector_Resources.SelectAPIMethod</option>
                <option value="GET">@CommerceConnector_Resources.Get</option>
                <option value="POST">@CommerceConnector_Resources.Post</option>
            </select>
            <span id="SourceHTTPActionError" class="text-danger field-validation-error"> </span>
        </div>



        <div class="col-md-12 col-lg-6 mb-3" id="DivSourceAuthType">
            <label asp-for="TransmissionConfigurations.HttpsConfig.AuthenticationType" class="form-label" data-test-selector="lblAuthenticationType" aria-label="Authentication Type"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.HttpsConfig.AuthenticationType" class="form-select" id="SourceDropdownAuthType" onchange="ManageDataExchange.prototype.HandleAuthType('Source', $(this))" data-test-selector="drpAuthenticationType" aria-label="Source Authentication Type">
                <option value="">@CommerceConnector_Resources.SelectAuthType</option>
                <option value="Basic">@CommerceConnector_Resources.Basic</option>
                <option value="OAuth2">@CommerceConnector_Resources.OAuth</option>
                <option value="Bearer">@CommerceConnector_Resources.Bearer</option>
            </select>
            <span id="SourceAuthTypeError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceEndpoint">
            <label asp-for="TransmissionConfigurations.HttpsConfig.Endpoint" class="form-label"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfig.Endpoint" class="form-control" id="SourceEndpoint" placeholder="API Endpoint" data-test-selector="txtEndpoint" aria-label="Source Endpoint" />
            <span id="SourceEndpointError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 position-relative d-none" id="DivSourceAPIKey">
            <label asp-for="TransmissionConfigurations.HttpsConfig.APIKey" class="form-label" data-test-selector="lblPassword" aria-label="API Configuration Password"></label>
            <div class="input-group">
                <input class="form-control"
                       type="password"
                       name="TransmissionConfigurations.HttpsConfig.APIKey"
                       id="SourceAPIKey"
                       value="@Model.TransmissionConfigurations.HttpsConfig?.APIKey" data-test-selector="txtAPIKey" aria-label="Source API Key" placeholder="Your API key" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle password visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceGrantType">
            <label asp-for="TransmissionConfigurations.HttpsConfig.GrantType" class="form-label" data-test-selector="lblGrantType" aria-label="Grant Type"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfig.GrantType" class="form-control" id="SourceGrantType" placeholder="Authentication grant type" data-test-selector="txtGrantType" aria-label="Source Grant Type" />
            <span id="SourceGrantTypeError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceAccessTokenUrl">
            <label asp-for="TransmissionConfigurations.HttpsConfig.AccessTokenUrl" class="form-label" data-test-selector="lblAccessTokenUrl" aria-label="Access Token Url"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfig.AccessTokenUrl" class="form-control" id="SourceAccessTokenUrl" placeholder="Access token URL" data-test-selector="txtAccessTokenUrl" aria-label="Source Access Token URL" />
            <span id="SourceAccessTokenUrlError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceClientId">
            <label asp-for="TransmissionConfigurations.HttpsConfig.ClientId" class="form-label" data-test-selector="lblClientId" aria-label="Client Id"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <input asp-for="TransmissionConfigurations.HttpsConfig.ClientId" id="SourceClientId" class="form-control" placeholder="Client ID" data-test-selector="txtClientId" aria-label="Source Client ID" />
            <span id="SourceClientIdError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 position-relative d-none" id="DivSourceClientSecret">
            <label asp-for="TransmissionConfigurations.HttpsConfig.ClientSecret" class="form-label" data-test-selector="lblClientSecret" aria-label="Client Secret"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <div class="input-group">
                <input class="form-control" type="password" name="TransmissionConfigurations.HttpsConfig.ClientSecret" id="SourceClientSecret" placeholder="Client Secret" value="@Model.TransmissionConfigurations?.HttpsConfig?.ClientSecret" data-test-selector="txtTransmissionConfigurationsPassword" aria-label="Source Client Secret" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle Client Secret visibility"> <i class="fa-solid fa-eye"></i></button>
            </div>
            <span id="SourceClientSecretError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceScope">
            <label asp-for="TransmissionConfigurations.HttpsConfig.Scope" class="form-label" data-test-selector="lblScope" aria-label="Scope"></label>
            <input asp-for="TransmissionConfigurations.HttpsConfig.Scope" class="form-control" id="SourceScope" data-test-selector="txtScope" aria-label="Source Scope" />
            <span id="SourceScopeError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceLocationOfCredentials">
            <label asp-for="TransmissionConfigurations.HttpsConfig.LocationOfCredentials" class="form-label" data-test-selector="lblLocationOfCredentials" aria-label="Location Of Credentials"></label>
            <input asp-for="TransmissionConfigurations.HttpsConfig.LocationOfCredentials" class="form-control" id="SourceLocationOfCredentials" placeholder="Location Of Credentials" data-test-selector="txtLocationOfCredentials" aria-label="Source Location Of Credentials" />
            <span id="SourceLocationOfCredentialsError" class="text-danger field-validation-error"> </span>
        </div>

        <div class="col-md-12 col-lg-6 mb-3 d-none" id="DivSourceAPIUsername">
            <label asp-for="TransmissionConfigurations.HttpsConfig.Username" class="form-label" data-test-selector="lblUsername"></label>
            <input asp-for="TransmissionConfigurations.HttpsConfig.Username" class="form-control" data-test-selector="txtAPIUsername" aria-label="Source Username" />
        </div>

        <div class="col-md-12 col-lg-6 mb-3 position-relative d-none" id="DivSourceAPIPassword">
            <label asp-for="TransmissionConfigurations.HttpsConfig.Password" class="form-label" data-test-selector="lblPassword" aria-label="API Configuration Password"></label>
            <div class="input-group">
                <input class="form-control"
                       type="password"
                       name="TransmissionConfigurations.HttpsConfig.Password"
                       id="SourceAPIPassword"
                       value="@Model.TransmissionConfigurations?.HttpsConfig?.Password" data-test-selector="txtAPIPassword" aria-label="Source Password" />
                <button type="button" class="btn btn-outline-secondary toggle-password" onclick="ManageDataExchange.prototype.togglePassword(this)" data-test-selector="btnTogglePassword" aria-label="Toggle password visibility"><i class="fa-solid fa-eye"></i></button>
            </div>
        </div>

        <div class="col-md-12 col-lg-6 mb-3" id="DivSourceFileFormat">
            <label asp-for="TransmissionConfigurations.HttpsConfig.Format" class="form-label" data-test-selector="lblSourceFileFormat" aria-label="Source Format"></label><span class="text-danger required" data-test-selector="spnRequired"></span>
            <select asp-for="TransmissionConfigurations.HttpsConfig.Format" class="form-select" id="HTTPSourceDropdownFileFormat" data-test-selector="drpFileFormat" aria-label="Source File Format">
                <option value="">@CommerceConnector_Resources.SelectSourceFileFormat</option>
                <option value="CSV">@CommerceConnector_Resources.CSV</option>
                <option value="JSON">@CommerceConnector_Resources.JSON</option>
                <option value="XML">@CommerceConnector_Resources.XML</option>
            </select>
            <span id="HTTPSourceFileFormatError" class="text-danger field-validation-error"> </span>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 col-lg-6">
            <div class="d-flex align-items-center mt-4">
                <h6 data-test-selector="hdgQueryParams" aria-label="Query Params">@CommerceConnector_Resources.QueryParams</h6>
                <button type="button" class="btn-text btn-text-secondary query-field-btn" onclick="ManageDataExchange.prototype.addParameters('source', 'QueryParams')" data-test-selector="btnAddField" aria-label="Add Query Parameter field">@CommerceConnector_Resources.AddField</button>
            </div>
            <div class="source-query-params" data-test-selector="divQueryParams">

                <div id="sourceQueryParamHeader" class="row">
                    <div class="col-md-5 my-2" data-test-selector="divKey" >@CommerceConnector_Resources.Key</div>
                    <div class="col-md-5 my-2" data-test-selector="divValue" >@CommerceConnector_Resources.Value</div>
                </div>
                <div id="queryParamBodySource">
                    @if (HelperUtility.IsNotNull(Model.TransmissionConfigurations.HttpsConfig?.QueryParams) && Model.TransmissionConfigurations.HttpsConfig.QueryParams.Any())
                    {
                        for (int i = 0; i < Model.TransmissionConfigurations.HttpsConfig.QueryParams.Count; i++)
                        {
                            <div class="row align-items-center" id="source-query-row-@i">
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.QueryParams[@i].Key" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfig.QueryParams[i].Key" data-test-selector="txtQueryParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="SourceQueryParameterError-@i" class="text-danger"></span></div>
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.QueryParams[@i].Value" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfig.QueryParams[i].Value" data-test-selector="txtQueryParamsValue" /></div>
                                <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('source-query-row-@i')" data-test-selector="btnDelete" aria-label="Delete Query Params Field"><em class="z-close-circle"></em></button></div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="row align-items-center" id="source-query-row-0">
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.QueryParams[0].Key" class="form-control" data-test-selector="txtQueryParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="SourceQueryParameterError-0" class="text-danger"></span></div>
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.QueryParams[0].Value" class="form-control" data-test-selector="txtQueryParamsValue" /></div>
                            <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('source-query-row-0')" data-test-selector="btnDelete" aria-label="Delete Query Params Field"><em class="z-close-circle"></em></button></div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 col-lg-6">
            <div class="d-flex align-items-center mt-4">
                <h6 data-test-selector="hdgHeaderParams" aria-label="Header Params">@CommerceConnector_Resources.HeaderParams</h6>
                <button type="button" class="btn-text btn-text-secondary ms-3" onclick="ManageDataExchange.prototype.addParameters('source','HeaderParams')" data-test-selector="btnAddField" aria-label="Add Header Parameter field">@CommerceConnector_Resources.AddField</button>
            </div>
            <div class="source-header-params" data-test-selector="divHeaderParams">

                <div id="sourceHeaderParamHeader" class="row">
                    <div class="col-md-5 my-2" data-test-selector="divKey">@CommerceConnector_Resources.Key</div>
                    <div class="col-md-5 my-2" data-test-selector="divValue">@CommerceConnector_Resources.Value</div>
                </div>

                <div id="headerParamBodySource">
                    @if (HelperUtility.IsNotNull(Model.TransmissionConfigurations.HttpsConfig?.HeaderParams) && Model.TransmissionConfigurations.HttpsConfig.HeaderParams.Any())
                    {
                        for (int i = 0; i < Model.TransmissionConfigurations.HttpsConfig.HeaderParams.Count; i++)
                        {
                            <div class="row align-items-center" id="source-header-row-@i">
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.HeaderParams[@i].Key" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfig.HeaderParams[i].Key" data-test-selector="txtHeaderParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="SourceHeaderParameterError-@i" class="text-danger"></span></div>
                                <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.HeaderParams[@i].Value" class="form-control" value="@Model.TransmissionConfigurations.HttpsConfig.HeaderParams[i].Value" data-test-selector="txtHeaderParamsValue" /></div>
                                <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('source-header-row-@i')" data-test-selector="btnDelete" aria-label="Delete Header Params Field"><em class="z-close-circle"></em></button></div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="row align-items-center" id="source-header-row-0">
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.HeaderParams[0].Key" class="form-control" data-test-selector="txtHeaderParamsKey" oninput="ManageDataExchange.prototype.ValidateUniqueKeys()" /><span id="SourceHeaderParameterError-0" class="text-danger"></span></div>
                            <div class="col-md-5 my-2"><input type="text" name="TransmissionConfigurations.HttpsConfig.HeaderParams[0].Value" class="form-control" data-test-selector="txtHeaderParamsValue" /></div>
                            <div class="col-1 my-2 px-0"><button type="button" class="search-close-icon mt-1" onclick="ManageDataExchange.prototype.removeRow('source-header-row-0')" data-test-selector="btnDelete" aria-label="Delete Header Params Field"><em class="z-close-circle"></em></button></div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
<input type="hidden" id="sourceHeaderIndex" value="@Model.TransmissionConfigurations.HttpsConfig?.HeaderParams?.Count() ?? 0" data-test-selector="txtSourceHeaderIndex" aria-label="Source Header Index Text" />
<input type="hidden" id="sourceQueryIndex" value="@Model.TransmissionConfigurations.HttpsConfig?.QueryParams?.Count() ?? 0" data-test-selector="txtSourceQueryIndex" aria-label="Source Query Index Text" />



