﻿using Microsoft.Data.SqlClient;
using System.Data;
using Znode.CommerceConnector.API.IHelpers;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;

namespace Znode.CommerceConnector.API.Helpers
{
    public class APIDataHelper : IAPIDataHelper
    {
        public bool GetTransmissionType(int erpId)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetTransmissionType", sqlParameters);
            List<ZnodeTransmissionConfigurartionModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ZnodeTransmissionConfigurartionModel>(response);
            if (dataModelList?.FirstOrDefault()?.SchedulerType == "RealTime")
                return true;
            return false;
        }


    }
}
