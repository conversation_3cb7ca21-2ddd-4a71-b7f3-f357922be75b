﻿using System.ComponentModel.DataAnnotations;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class ProcessingLogsViewModel : BaseViewModel
    {
        [Display(Name = "Log Id")]
        public int ImportProcessLogId { get; set; }
        [Display(Name = "Template Id")]
        public int ImportTemplateId { get; set; }

        [Display(Name = "Template Name")]
        public string TemplateName { get; set; }
        [Display(Name = "Import Name")]
        public string ImportName { get; set; }
        public string Status { get; set; }
        [Display(Name = "Start Date")]
        public DateTime? ProcessStartedDate { get; set; }
        [Display(Name = "End Date")]
        public DateTime? ProcessCompletedDate { get; set; }
        public int SuccessRecordCount { get; set; }
        public int FailedRecordcount { get; set; }
        public int TotalProcessedRecords { get; set; }
    }
}
