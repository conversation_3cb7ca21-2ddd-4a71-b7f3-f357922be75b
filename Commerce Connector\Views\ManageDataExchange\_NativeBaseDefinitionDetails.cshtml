﻿@model Znode.CommerceConnector.Core.ViewModels.BaseDefinitionViewModel
@using Znode.Libraries.Resources.CommerceConnector_Resources
@{
    var isFromQuickView = ViewBag.isFromQuickView ?? false;
}
@if (Model.ERPBaseDefinitionId > 0 && !string.IsNullOrWhiteSpace(Model.Name))
{
    <div class="base-definition my-3">
        <div class="row">
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divExchangeName">@CommerceConnector_Resources.ExchangeName</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divName">@Model.Name <input type="hidden" asp-for="Name" data-test-selector="txtName" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divDataSource">@CommerceConnector_Resources.DataSource</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divDataSource">@Model.DataSource  <input type="hidden" asp-for="DataSource" data-test-selector="txtDataSource" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divDataDestination">@CommerceConnector_Resources.DataDestination</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divDataDestination">@Model.DataDestination <input type="hidden" asp-for="DataDestination" data-test-selector="txDataDestination" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divVersion">@CommerceConnector_Resources.Version</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divVersion">@Model.Version <input type="hidden" asp-for="Version" data-test-selector="txtVersion" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divAccess">@CommerceConnector_Resources.Access</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divAccess">@Model.Access <input type="hidden" asp-for="Access" data-test-selector="txtAccess" /></div>
                </div>
                @if (Model.Library.Equals(CommerceConnector_Resources.CustomLibrary))
                {
                    <div class="row">
                        <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divIsCustomProcessingRequired">@CommerceConnector_Resources.LabelIsCustomProcessingRequired</div>
                        <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divIsCustomProcessingRequired">
                            @(Model.IsCustomProcessingRequired ? CommerceConnector_Resources.LabelCustomProcessingRequired : CommerceConnector_Resources.LabelCustomProcessingNotRequired)
                        </div>
                    </div>
                }
                @if ((Model.Library.Equals(CommerceConnector_Resources.CustomLibrary) && Model.IsCustomProcessingRequired) || Model.Library.Equals("Native"))
                {
                    <div class="row">
                        <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divProcessorFileName">@CommerceConnector_Resources.ProcessorFileName</div>
                        <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divProcessorFileName">@Model.ProcessorFileName <input type="hidden" asp-for="ProcessorFileName" data-test-selector="txtProcessorFileName" /></div>
                    </div>
                }
            </div>
            <div class="col-md-6">
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divTriggerOrigin">@CommerceConnector_Resources.TriggerOrigin</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divTriggerOrigin">@Model.TriggerOrigin <input type="hidden" asp-for="TriggerOrigin" data-test-selector="txtTriggerOrigin" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divCollection">@CommerceConnector_Resources.Collection</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divCollection">@Model.Collection <input type="hidden" asp-for="Collection" data-test-selector="txtCollection" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divTags">@CommerceConnector_Resources.Tags</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divTags">@Model.Tags <input type="hidden" asp-for="Tags" data-test-selector="txtTags" /></div>
                </div>
                <div class="row">
                    <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divDescription">@CommerceConnector_Resources.Description</div>
                    <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divDescription">@Model.Description <input type="hidden" asp-for="Description" data-test-selector="txtDescription" /></div>
                </div>
                @if (!isFromQuickView)
                {
                    <div class="row">
                        <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divEnable">@CommerceConnector_Resources.LabelEnableStatus</div>
                        <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divEnable">
                            @Html.CheckBoxFor(model => model.StatusActivationViewModel.Status, new { style = "opacity: revert !important", @data_test_selector = "isEnable", @id = "isEnable", aria_label = "Scheduler Status" })
                        </div>

                    </div>
                }
                else
                {
                    <div class="row">
                        <div class="col-md-6 col-lg-5 heading mb-2" data-test-selector="divEnabled">@CommerceConnector_Resources.LabelEnableStatus</div>
                        <div class="col-md-6 col-lg-7 mb-2 view-details" data-test-selector="divEnabled">
                            @(Model.StatusActivationViewModel.Status ? CommerceConnector_Resources.LabelEnable : CommerceConnector_Resources.LabelDisable)
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}
else
{
    <div class="text-center">@CommerceConnector_Resources.NoRecordsFound</div>
}
<input type="hidden" id="erpID" value="@Model.ERPBaseDefinitionId" />
<input type="hidden" name="ProcessorFileName" value="@Model.ProcessorFileName" />
<input type="hidden" name="ERPExchangeId" value="@Model.DataExchangeLibraryId" />