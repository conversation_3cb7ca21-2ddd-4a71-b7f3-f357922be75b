﻿using Microsoft.Data.SqlClient;
using System.Data;
using System.Reflection;
using System.Reflection.Emit;
using System.Text;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.Processor.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;

namespace Znode.CommerceConnector.Processor.Helper
{
    public class ProcessorDataHelper
    {
        public dynamic ReadTempTableToModel(string tempTableName)
        {
            try
            {
                var csvModel = new List<object>();

                using (var connection = new SqlConnection(HelperMethods.ConnectionString))
                {
                    connection.Open();

                    string query = $"SELECT * FROM {tempTableName}";

                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var rowDict = new Dictionary<string, string>();

                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                string columnName = reader.GetName(i);
                                string value = reader.IsDBNull(i) ? null : reader.GetValue(i)?.ToString();
                                rowDict[columnName] = value;
                            }

                            csvModel.Add(rowDict);
                        }
                    }
                }

                return csvModel;
            }
            catch
            {
                return new List<object>();
            }
        }

        private static object MapDataToModel(Type modelType, object modelInstance, IDictionary<string, object> objDict)
        {
            // Loop through each column in the dictionary and set the corresponding property in the model instance
            foreach (var keyValue in objDict)
            {
                // Ensure the key exists as a property in the model type
                var property = modelType.GetProperty(keyValue.Key);
                if (property != null && property.CanWrite)
                {
                    // Convert the dictionary value to the correct property type
                    var value = Convert.ChangeType(keyValue.Value, property.PropertyType);
                    property.SetValue(modelInstance, value);
                }
            }

            return modelInstance;
        }

        public static DynamicModel CreateModelFromCsv(string csv, string ProcessorName)
        {
            DynamicModel dynamicModel = new DynamicModel();

            var className = ProcessorName;
            var fields = csv.Split(',');

            var sb = new StringBuilder();
            sb.AppendLine("public class " + className);
            sb.AppendLine("{");
            foreach (var field in fields)
            {
                sb.AppendLine($"    public object {field.Trim()} {{ get; set; }}");
            }
            sb.AppendLine("}");

            dynamicModel.modelCode = sb.ToString();

            var properties = new List<(string, Type)>();
            foreach (var field in fields)
            {
                properties.Add((field.Trim(), typeof(object)));
            }

            dynamicModel.modelType = CompileDynamicClass(className, properties);

            dynamicModel.modelInstance = Activator.CreateInstance(dynamicModel.modelType);

            return dynamicModel;
        }

        private static Type CompileDynamicClass(string className, List<(string Name, Type Type)> properties)
        {
            var assemblyName = new AssemblyName("DynamicAssembly");
            var assemblyBuilder = AssemblyBuilder.DefineDynamicAssembly(assemblyName, AssemblyBuilderAccess.Run);
            var moduleBuilder = assemblyBuilder.DefineDynamicModule("MainModule");
            var typeBuilder = moduleBuilder.DefineType(className, TypeAttributes.Public);

            foreach (var prop in properties)
            {
                var fieldBuilder = typeBuilder.DefineField("_" + prop.Name, prop.Type, FieldAttributes.Private);
                var propBuilder = typeBuilder.DefineProperty(prop.Name, PropertyAttributes.HasDefault, prop.Type, null);

                var getter = typeBuilder.DefineMethod("get_" + prop.Name,
                    MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.HideBySig,
                    prop.Type, Type.EmptyTypes);

                var getterIL = getter.GetILGenerator();
                getterIL.Emit(OpCodes.Ldarg_0);
                getterIL.Emit(OpCodes.Ldfld, fieldBuilder);
                getterIL.Emit(OpCodes.Ret);

                var setter = typeBuilder.DefineMethod("set_" + prop.Name,
                    MethodAttributes.Public | MethodAttributes.SpecialName | MethodAttributes.HideBySig,
                    null, new[] { prop.Type });

                var setterIL = setter.GetILGenerator();
                setterIL.Emit(OpCodes.Ldarg_0);
                setterIL.Emit(OpCodes.Ldarg_1);
                setterIL.Emit(OpCodes.Stfld, fieldBuilder);
                setterIL.Emit(OpCodes.Ret);

                propBuilder.SetGetMethod(getter);
                propBuilder.SetSetMethod(setter);
            }

            return typeBuilder.CreateType();
        }

        public string ConvertToCsv(List<Dictionary<string, string>> importList)
        {
            StringBuilder csvBuilder = new StringBuilder();

            // Handle headers (use keys from the first row dictionary)
            if (importList.Count > 0)
            {
                var headers = importList[0].Keys.ToList();
                csvBuilder.AppendLine(string.Join(",", headers));
            }

            // Handle rows
            foreach (var row in importList)
            {
                var rowValues = row.Values.Select(v => $"\"{v}\"").ToList(); // Add quotes around each value (to handle commas, etc.)
                csvBuilder.AppendLine(string.Join(",", rowValues));
            }

            return csvBuilder.ToString();
        }

        public List<string> CreateTempTableWithData(DataSet tablesToInsert)
        {
            Guid tableGuid = Guid.NewGuid();

            if (IsNull(tablesToInsert) || tablesToInsert.Tables.Count <= 0)
            {
                return new List<string>();
            }

            List<string> tablesNames = new List<string>();
            string tableName = string.Empty;
            try
            {
                foreach (DataTable tableToinsert in tablesToInsert.Tables)
                {
                    if (IsNull(tableToinsert) || tableToinsert.Rows.Count <= 0)
                    {
                        continue;
                    }

                    tableName = CreateTempTable(tableToinsert, tableGuid);

                    InsertDataIntoTempTable(tableToinsert, tableName);
                    tablesNames.Add(tableName);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in HandlerDataHelper.CreateTempTableWithData {ex.Message}");
            }
            return tablesNames;
        }

        private string CreateTempTable(DataTable tableToinsert, Guid tableGuid)
        {
            string tableName = $"tempdb..[##{tableToinsert.TableName}_{tableGuid}]";
            SqlConnection conn = GetSqlConnection();
            SqlCommand cmd = new SqlCommand("CREATE TABLE " + tableName + "  " + GenerateTableColumns(tableToinsert) + " ", conn);
            if (conn.State.Equals(ConnectionState.Closed))
            {
                conn.Open();
            }
            cmd.ExecuteNonQuery();

            return tableName;
        }

        private string GenerateTableColumns(DataTable tableDump)
        {
            if (IsNull(tableDump))
            {
                return string.Empty;
            }

            StringBuilder tableColumns = new StringBuilder();
            tableColumns.Append("(");
            tableColumns.Append(string.Join(" nvarchar(max) , ", (from dc in tableDump.Columns.Cast<DataColumn>() select dc.ColumnName).ToArray()));
            tableColumns.Append(" nvarchar(max))");
            return tableColumns.ToString();
        }

        private void InsertDataIntoTempTable(DataTable tableToInsert, string tableName)
        {
            try
            {
                if (IsNotNull(tableToInsert) && tableToInsert.Rows?.Count > 0)
                {
                    int chunkSize = int.Parse(HelperMethods.ZnodeImportChunkLimit);
                    int startIndex = 0;
                    int totalRows = tableToInsert.Rows.Count;
                    int totalRowsCount = totalRows / chunkSize;

                    if (totalRows % chunkSize > 0)
                    {
                        totalRowsCount++;
                    }

                    for (int iCount = 0; iCount < totalRowsCount; iCount++)
                    {
                        DataTable fileData = tableToInsert.Rows.Cast<DataRow>().Skip(startIndex).Take(chunkSize).CopyToDataTable();
                        startIndex = startIndex + chunkSize;
                        InsertData(tableName, fileData);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in HandlerDataHelper.InsertDataIntoTempTable {ex.Message}");
            }
        }

        private SqlConnection GetSqlConnection()
        {
            return new SqlConnection(HelperMethods.ConnectionString);
        }

        private void InsertData(string tableName, DataTable fileData)
        {
            SqlConnection conn = GetSqlConnection();
            try
            {
                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(conn))
                {
                    bulkCopy.DestinationTableName = tableName;

                    if (conn.State.Equals(ConnectionState.Closed))
                    {
                        conn.Open();
                    }

                    bulkCopy.WriteToServer(fileData);
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in HandlerDataHelper.InsertData {ex.Message}");
            }
            finally
            {
                if (conn.State.Equals(ConnectionState.Open))
                {
                    conn.Close();
                }
            }
        }

        public static bool IsNull(object value)
        {
            return object.Equals(value, null);
        }

        public static bool IsNotNull(object value)
        {
            return !object.Equals(value, null);
        }

        public virtual List<Dictionary<string, string>> ConvertTempTableDataToImport(List<object> csvModel, List<ZnodeImportTemplateMappingModel> importTemplate)
        {
            var importList = new List<Dictionary<string, string>>();

            var orderedTemplate = importTemplate.OrderBy(x => x.DisplayOrder).ToList();

            foreach (var csvRow in csvModel)
            {
                if (csvRow is Dictionary<string, string> rowDict)
                {
                    var mappedRow = new Dictionary<string, string>();

                    foreach (var mapping in orderedTemplate)
                    {
                        if (rowDict.TryGetValue(mapping.SourceColumnName, out var value))
                        {
                            mappedRow[mapping.TargetColumnName] = value;
                        }
                    }

                    importList.Add(mappedRow);
                }
            }

            return importList;
        }
    }
}
