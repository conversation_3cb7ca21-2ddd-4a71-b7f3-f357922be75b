﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class DestinationConfigurationTable
{
    public int TransmissionId { get; set; }

    public int ConfigurationId { get; set; }

    public string? ServerAddress { get; set; }

    public string? FolderPath { get; set; }

    public string? FileName { get; set; }

    public string? UserName { get; set; }

    public string? Password { get; set; }

    public string? ActionAfterRetrival { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }
}
