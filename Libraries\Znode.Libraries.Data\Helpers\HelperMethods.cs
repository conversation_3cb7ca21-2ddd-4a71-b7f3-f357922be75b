﻿using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Data;
using System.Diagnostics;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data.Models;
using Znode.Libraries.ECommerce.Utilities;
namespace Znode.Libraries.Data.Helpers
{
    public static class HelperMethods
    {
        public static IServiceProvider _staticServiceProvider;
        private static IConfiguration _configuration => _staticServiceProvider.GetService<IConfiguration>();

        public static void Configure(IServiceProvider serviceProvider)
        {
            _staticServiceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        public static string ConnectionString => _configuration.GetConnectionString("ZnodeCommerceConnectorDB");
        public static string ZnodeImportChunkLimit => "1000";
        public static string YAMlFilepath => _configuration["appsettings:YAMLFilePath"];

        public static List<ZnodeErpdataExchangeHeader> GetHeaderParameter(int transmissionId, string type)
        {
            MongoLogging.LogMessage("GetHeaderParameter method in APIHandler called", "HandlerDataHelper", TraceLevel.Info);
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@APIConfigurationId", transmissionId));
            sqlParameters.Add(new SqlParameter("@Type", type));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetHeaderParameterDetails", sqlParameters);
            return znodeViewRepository.ConvertDataTableToList<ZnodeErpdataExchangeHeader>(response);
        }

        public static bool InsertUpdateProcessorLogs(int erpId, int logId, string csv, bool status, string errorMessage = "")
        {
            int rowCount = 0;
            int userId = 0;
            int successRecords = 0;
            int failedRecords = 0;
            string statusName = status ? "Completed Successfully" : "Failed";
            if (logId > 0)
            {
                if (!string.IsNullOrWhiteSpace(csv))
                {
                    rowCount = GetRowCountFromCsv(csv);
                    successRecords = status ? rowCount : 0;
                    failedRecords = status ? 0 : rowCount;
                }
            }
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@CustomProcessLogId", logId));
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            sqlParameters.Add(new SqlParameter("@StatusName", statusName));
            sqlParameters.Add(new SqlParameter("@ImportLogMessage", errorMessage));
            sqlParameters.Add(new SqlParameter("@TotalProcessedRecords", rowCount));
            sqlParameters.Add(new SqlParameter("@FailedRecordCount ", failedRecords));
            sqlParameters.Add(new SqlParameter("@SuccessRecordCount", successRecords));
            sqlParameters.Add(new SqlParameter("@ProcessStartedDate", logId > 0 ? DBNull.Value : DateTime.UtcNow));
            sqlParameters.Add(new SqlParameter("@ProcessCompletedDate", logId > 0 ? DateTime.UtcNow : DBNull.Value));
            sqlParameters.Add(new SqlParameter("@UserId", userId));
            sqlParameters.Add(new SqlParameter("@Status", SqlDbType.Bit) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_InsertUpdateImportDataExchangeProcessLog", sqlParameters, out outValue);
            return outValue?.Count() > 0 ? (bool)outValue["@Status"] : false;
        }

        private static int GetRowCountFromCsv(string csv)
        {
            var lines = csv
                .Split(new[] { "\r\n", "\n", "\r" }, StringSplitOptions.None)
                .Where(line => !string.IsNullOrWhiteSpace(line))
                .ToArray();

            return lines.Length > 1 ? lines.Length - 1 : 0;
        }

        public static ProcessorDetails GetProcessorDetails(int erpId)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetProcessorNameByERPId", sqlParameters);

            ProcessorDetails processorDetails = znodeViewRepository.ConvertDataTableToList<ProcessorDetails>(response).FirstOrDefault();
            processorDetails.ErpId = erpId;
            return processorDetails;
        }

        public static SchedulerConfigurationModel GetSchedulerConfiguration(int id)
        {
            SchedulerConfigurationModel schedulerConfigurationModel = new();
            if (id > 0)
            {
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter> { new SqlParameter("@ERPBaseDefinitionId", id) };
                DataTable configurationResponse = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSchedulerDataByERPId", sqlParameters);
                List<SchedulerConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<SchedulerConfigurationModel>(configurationResponse);

                return dataModelList.Count > 0 ? dataModelList.FirstOrDefault() : new SchedulerConfigurationModel();
            }
            return schedulerConfigurationModel;
        }

        public static bool SaveSchedulerConfiguration(SchedulerConfigurationModel model)
        {
            bool status = false;
            if (model?.ERPBaseDefinitionId > 0 && !string.IsNullOrWhiteSpace(model?.SchedulerName))
            {
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@SchedulerType", model.SchedulerType));
                sqlParameters.Add(new SqlParameter("@SchedulerName", model.SchedulerName));
                sqlParameters.Add(new SqlParameter("@SchedulerFrequency", model.SchedulerFrequency));
                sqlParameters.Add(new SqlParameter("@StartDate", model.StartDate));
                sqlParameters.Add(new SqlParameter("@LastRunTime", model.LastRunTime));
                sqlParameters.Add(new SqlParameter("@CronExpression", model.CronExpression));
                sqlParameters.Add(new SqlParameter("@ProcessorFileName", model.ProcessorFileName));
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", model.ERPBaseDefinitionId));
                sqlParameters.Add(new SqlParameter("@HangfireJobId", model.HangfireJobId));
                sqlParameters.Add(new SqlParameter("@UserId", 0));
                sqlParameters.Add(new SqlParameter("@Status", SqlDbType.Bit) { Direction = ParameterDirection.Output });
                Dictionary<string, object> outValue = new Dictionary<string, object>();
                DataTable response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_InsertUpdateERPBaseDefinitionScheduler", sqlParameters, out outValue);
                status = (bool)outValue["@Status"];
            }
            return status;
        }
    }
}
