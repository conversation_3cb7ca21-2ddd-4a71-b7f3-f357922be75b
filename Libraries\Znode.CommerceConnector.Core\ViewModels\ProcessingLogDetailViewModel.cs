﻿using System.ComponentModel.DataAnnotations;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class ProcessingLogDetailViewModel
    {
        [Display(Name = "ID")]
        public int ImportDataExchangeLogId { get; set; }
        [Display(Name = "Process Log Id")]
        public int ImportDataExchangeProcessLogId { get; set; }
        [Display(Name = "ERP Id")]
        public int ErpBaseDefinitionId { get; set; }
        [Display(Name = "Log Message")]
        public string ImportLogMessage { get; set; }
        public int SuccessRecordCount { get; set; } = 0;
        public int FailedRecordcount { get; set; } = 0;
        public int TotalProcessedRecords { get; set; } = 0;
    }
}
