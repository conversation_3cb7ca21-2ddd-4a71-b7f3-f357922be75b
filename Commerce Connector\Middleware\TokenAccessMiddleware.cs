﻿using Azure.Core;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using Znode.CommerceConnector.Core.Helper;

public class TokenAccessMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IConfiguration _config;

    public TokenAccessMiddleware(RequestDelegate next, IConfiguration config)
    {
        _next = next;
        _config = config;
    }

    public async Task Invoke(HttpContext context)
    {
        var path = context.Request.PathBase.Value;

        if (path.StartsWith("/commerce-connector") || path.StartsWith("/css") || path.StartsWith("/js"))
        {
            var token = context.Request.Query[CCAdminConstant.CCAdminToken].ToString();
            string sessionExists;
            string referer = Convert.ToString( context.Request.Headers["Referer"].ToString());
            if(string.IsNullOrEmpty(token))
            {
                token = Convert.ToString(context.Request.Headers[CCAdminConstant.CCAdminToken].ToString());
            }

            if (!string.IsNullOrEmpty(token))
            { 
                if (!context.Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken) || string.IsNullOrWhiteSpace(context.Request.Headers[CCAdminConstant.CCAdminToken]))
                {
                    context.Request.Headers[CCAdminConstant.CCAdminToken] = token;
                }
            }
            else
            {

                if (!string.IsNullOrWhiteSpace(referer) && referer.Contains(CCAdminConstant.CCAdminToken))
                {
                    Uri refererUri = new Uri(referer);
                    var queryParams = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(refererUri.Query);

                    if (queryParams.TryGetValue(CCAdminConstant.CCAdminToken, out var tokenRequest))
                    {
                        token = tokenRequest.ToString();
                    }
                }
            }
            if (!string.IsNullOrEmpty(token))
            {
                if (!context.Request.Headers.ContainsKey(CCAdminConstant.CCAdminToken))
                {
                    context.Request.Headers.Append(CCAdminConstant.CCAdminToken, token);
                }
            }
                if (!string.IsNullOrEmpty(token))
            {

                if (ValidateToken(token))
                {
                        await _next(context);
                    return;
                }
                else
                {
                    context.Response.StatusCode = 403;
                    await context.Response.WriteAsync("token-expired");
                    return;
                }
            }
            // Skip authentication for specific request paths triggered internally via jQuery/AJAX.
            // These endpoints (like SaveYaml or GetSavedYAMlData) are intended for background/script use
            // and do not require user authentication validation.
            else if (context.Request.Path.ToString().IndexOf(AuthenticationBypassPaths.GetSavedYamlData, StringComparison.OrdinalIgnoreCase) >= 0 ||
            context.Request.Path.ToString().IndexOf(AuthenticationBypassPaths.SaveYaml, StringComparison.OrdinalIgnoreCase) >= 0 
            || referer.ToString().IndexOf(AuthenticationBypassPaths.ManageDataExchangedetails, StringComparison.OrdinalIgnoreCase) >= 0
            || context.Request.Path.ToString().IndexOf(AuthenticationBypassPaths.RenderDataExchangeGrid, StringComparison.OrdinalIgnoreCase) >= 0
            )
            {
                await _next(context);
                return;

            }

            context.Response.StatusCode = 403;
            await context.Response.WriteAsync("token-expired");
            return;
        }

        await _next(context);
    }

    private bool ValidateToken(string token)
    {
        var tokenHandler = new JwtSecurityTokenHandler();

        var base64Key = "YXBpLXoxMC1xYS5hbWxhLmlvfGEyYzMyZDI2LWZiNTYtNGUyZC1hYTQ0LTEwYWU0MDFhMDk3MA==";
        var key = Convert.FromBase64String(base64Key); // decode from base64

        try
        {
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),

                ValidIssuer = "commerce-connector",
                ValidAudience = "admin-app",
                ValidateIssuer = true,
                ValidateAudience = true,

                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            return true;
        }
        catch
        {
            return false;
        }
    }
}