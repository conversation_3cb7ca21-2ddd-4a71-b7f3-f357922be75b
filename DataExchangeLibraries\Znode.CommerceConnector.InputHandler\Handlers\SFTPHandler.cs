﻿using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Data;
using System.Diagnostics;
using System.Text;
using System.Xml;
using Znode.CommerceConnector.Model;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.InputHandler
{
    public class SFTPHandler : ISFTPHandler
    {
        ISFTPClient _sFTPClient;
        IHandlerDataHelper _handlerDataHelper;

        public SFTPHandler(ISFTPClient sFTPClient, IHandlerDataHelper handlerDataHelper)
        {
            _sFTPClient = sFTPClient;
            _handlerDataHelper = handlerDataHelper;
        }

        // call sftp client
        public dynamic SFTPDataHandler(int erpId, int logId)
        {
            try
            {
                dynamic inputHandlerData = "";
                MongoLogging.LogMessage(Constants.SFTPDataHandlerMethodCalledInSFTPHandler, Constants.SFTPHandler, TraceLevel.Info);
                SFTPConfigurationModel sFTPConfigurationModel = GetInputHandlerSFTPCredentials(erpId);
                byte[] file = _sFTPClient.DownloadData<bool>(erpId, logId, sFTPConfigurationModel, false);

                if (file.Length > 0)
                {
                    //Get source format
                    string format = _handlerDataHelper.GetSFTPFormat(erpId, Constants.Source);

                    if (format == Constants.JSON)
                    {
                        MongoLogging.LogMessage(Constants.JSONFormat, Constants.SFTPHandler, TraceLevel.Info);
                        //returning the json file as it is without creating temptable
                        inputHandlerData = file;
                    }
                    else if (format == Constants.CSV)
                    {
                        MongoLogging.LogMessage(Constants.CSVFormat, Constants.SFTPHandler, TraceLevel.Info);
                        inputHandlerData = ParseCSVData(file, erpId);
                    }
                    else if (format == Constants.XML)
                    {
                        MongoLogging.LogMessage(Constants.XMLFormat, Constants.SFTPHandler, TraceLevel.Info);
                        XmlDocument doc = new XmlDocument();
                        string xml = Encoding.UTF8.GetString(file);
                        doc.LoadXml(xml);
                        DataSet dataSet = new DataSet();
                        using (StringReader stringReader = new StringReader(xml))
                        {
                            dataSet.ReadXml(stringReader);
                        }
                        inputHandlerData = _handlerDataHelper.CreateTempTableWithData(dataSet).FirstOrDefault();
                    }
                    MongoLogging.LogMessage(Constants.TemptableGeneratedInInputHandler + inputHandlerData, Constants.SFTPHandler, TraceLevel.Info);
                    return inputHandlerData;
                }
                MongoLogging.LogMessage(Constants.FileNotFound + file.Length, Constants.SFTPHandler, TraceLevel.Info);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.SFTPFileNotFound + file.Length);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.SFTPHandler, TraceLevel.Error);
                return false;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.SFTPDataHandlerMethodInSFTPDataHandlerFailed + ex.Message, Constants.SFTPHandler, TraceLevel.Error);
                bool logResponse = HelperMethods.InsertUpdateProcessorLogs(erpId, logId, string.Empty, false, Constants.SFTPDataHandlerMethodInSFTPDataHandlerFailed + ex.Message);
                if (!logResponse)
                    MongoLogging.LogMessage(MongoLoggingConstants.ProcessingLogsNotUpdated, Constants.SFTPHandler, TraceLevel.Error);
                return string.Empty;
            }
        }

        public SFTPConfigurationModel GetInputHandlerSFTPCredentials(int erpId)
        {
            SFTPConfigurationModel sFTPConfigurationModel = GetSFTPCredentials(erpId);
            return sFTPConfigurationModel;
        }

        public SFTPConfigurationModel GetSFTPCredentials(int erpId)
        {
            try
            {
                MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerCalled, Constants.SFTPHandler, TraceLevel.Info);
                List<SFTPConfigurationModel> sftpCredentials = new List<SFTPConfigurationModel>();
                DataTable response = new DataTable();
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", erpId));
                sqlParameters.Add(new SqlParameter("@Type", Constants.Source));
                response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetSFTPTransmissionDetails", sqlParameters);
                List<SFTPConfigurationModel> dataModelList = znodeViewRepository.ConvertDataTableToList<SFTPConfigurationModel>(response);
                MongoLogging.LogMessage("GetSFTPCredentials : " + dataModelList?.FirstOrDefault(), Constants.SFTPHandler, TraceLevel.Info);
                return dataModelList?.FirstOrDefault();
            }
            catch
            {
                MongoLogging.LogMessage(Constants.GetSFTPCredentialsMethodInSFTPHandlerFailed, Constants.SFTPHandler, TraceLevel.Info);
                return new SFTPConfigurationModel();
            }
        }

        public string ParseCSVData(byte[] file, int erpId)
        {
            try
            {
                MongoLogging.LogMessage(Constants.ParseCSVDataMethodInSFTPHandlerCalled, Constants.SFTPHandler, TraceLevel.Info);
                using (var memoryStream = new MemoryStream(file))
                using (var reader = new StreamReader(memoryStream))
                {
                    DataTable dataTable = LoadCsvFromStream(reader);
                    DataSet csvDataSet = new DataSet();
                    csvDataSet.Tables.Add(dataTable);

                    List<string> tempTableName = _handlerDataHelper.CreateTempTableWithData(csvDataSet);
                    MongoLogging.LogMessage(Constants.ParseCSVDataMethodTempTableName + tempTableName, Constants.SFTPHandler, TraceLevel.Info);
                    return tempTableName?.FirstOrDefault();
                }
            }
            catch
            {
                MongoLogging.LogMessage(Constants.ParseCSVDataMethodInSFTPHandlerFailed, Constants.SFTPHandler, TraceLevel.Info);
                return "";
            }
        }

        private DataTable LoadCsvFromStream(TextReader reader)
        {
            DataTable dt = new DataTable();
            bool headersRead = false;

            string? line;
            while ((line = reader.ReadLine()) != null)
            {
                string[] values = line.Split(',');

                if (!headersRead)
                {
                    foreach (var header in values)
                        dt.Columns.Add(header.Trim());

                    headersRead = true;
                }
                else
                {
                    dt.Rows.Add(values);
                }
            }

            return dt;
        }

        public string ConvertJsonToDataTable(string json)
        {
            try
            {
                MongoLogging.LogMessage(Constants.ConvertJsonToDataTableCalled, Constants.SFTPHandler, TraceLevel.Info);
                var jObject = JObject.Parse(json);

                JArray array;

                if (jObject["value"] is JArray valueArray)
                {
                    array = valueArray;
                }
                else
                {
                    var valueProps = jObject.Properties()
                        .Where(p => p.Name.StartsWith("value["))
                        .OrderBy(p => p.Name)
                        .Select(p => p.Value)
                        .ToArray();

                    array = new JArray(valueProps);
                }

                DataTable dataTable = JsonConvert.DeserializeObject<DataTable>(array.ToString());
                DataSet csvDataSet = new DataSet();
                csvDataSet.Tables.Add(dataTable);
                List<string> tempTableName = _handlerDataHelper.CreateTempTableWithData(csvDataSet);
                return tempTableName?.FirstOrDefault();
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage(Constants.ConvertJsonToDataTableFailed + ex.Message, Constants.SFTPHandler, TraceLevel.Error);
                return "";
            }
        }
    }
}
