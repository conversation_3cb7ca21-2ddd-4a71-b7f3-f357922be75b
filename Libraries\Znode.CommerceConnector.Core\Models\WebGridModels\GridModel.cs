﻿namespace Znode.CommerceConnector.Core.Models
{
    public class GridModel
    {
        public int Page { get; set; } = 1; 
        public int PageSize { get; set; } = 10;
        public string? SortField { get; set; }
        public string? SortDirection { get; set; }
        public string? GlobalSearch { get; set; }
        public string FilterLogic { get; set; } = "and";
        public List<FilterColumnModel> FilterColumnModel { get; set; } = new();
    }
}