﻿using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.Client
{
    public interface IGetDataExchangeClient
    {
        StandardDataExchangeListModel GetDataExchangeList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex, int? pageSize);

        bool TriggerDataExchangeSchedular(int erpId);
        dynamic RealTimeDataScheduler(dynamic requestBody, int erpId);

        bool DeleteDataExchangeSchedular(int erpId);
        bool EnableDisableDataExchangeScheduler(int erpId, bool isActive);
        int CreateEditBaseDataExchange(BaseDefinitionModel model);


        ImportLogsListResponse GetProcessingLogList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10);
        ImportLogDetailsListResponse GetProcessingLogDetails(int logId, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10);
        ProcessorLogListResponse GetGenericLogList(FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10);

        ProcessingLogDetailListModel GetGenericLogDetails(int logId, FilterCollection filters, Dictionary<string, string> sorts, int? pageIndex = 1, int? pageSize = 10);
    }
}
