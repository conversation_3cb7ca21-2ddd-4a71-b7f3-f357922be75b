﻿using Znode.CommerceConnector.Client.IClients;
using Znode.CommerceConnector.InputHandler.IHandlers;
using Znode.CommerceConnector.Model;

namespace Znode.CommerceConnector.InputHandler.Handlers
{
    public class YAMLDataHandler : IYAMLDataHandler
    {
        public IYAMLDataClient _yAMLDataClient;
        public IYAMLClient _yAMLClient;

        public YAMLDataHandler(IYAMLDataClient yAMLDataClient, IYAMLClient yAMLClient)
        {
            _yAMLDataClient = yAMLDataClient;
            _yAMLClient = yAMLClient;
        }

        public dynamic GetYAMLData(string yamlFileName)
        {
            MediaConfigurationModel mediaConfiguration = _yAMLClient.GetMediaConfiguration();
            dynamic mappingModels = _yAMLDataClient.GetYAMLData(yamlFileName, mediaConfiguration);
            return mappingModels;
        }
    }
}
