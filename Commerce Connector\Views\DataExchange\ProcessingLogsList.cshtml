﻿@using Znode.CommerceConnector.Core.Helper
@using Znode.Libraries.Resources.CommerceConnector_Resources

@{
    var token = Context.Request.Headers[CCAdminConstant.CCAdminToken].FirstOrDefault();
    var exchangeName = TempData["ExchangeName"];
    var erpBaseDefinitionId = TempData["ERPBaseDefinitionId"];
}

<div class="col-md-12 nopadding dashboard-title">
    <div class="title-container d-flex justify-content-between align-items-center">
        <h1 data-test-selector="hdgDataExchangeList">@CommerceConnector_Resources.DataExchangeList</h1>
        <div class="mt-2" id="processLogList">
            <a href="@Url.Action("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token })" type="button" class="btn-text-icon" onclick="location.href='@Url.Action("GetDataExchangeLibraryList", "DataExchange", new { CCAdminToken = token })'" data-bs-dismiss="modal" data-test-selector="btnBack"> <em class='z-back'></em> @CommerceConnector_Resources.LabelBack </a>
        </div>
    </div>
</div>

<div class="col-md-12 page-container">
    <div class="row">
        <div class="col-md-3 col-lg-2">
            <div class="left-panel-side list-group" id="configTabs" aria-label="Configuration Navigation">
                <ul class="left-panel-side-ul nav nav-tabs border-0 d-block" data-test-selector="listLeftPanelSideUl">
                    <li class="nav-item" data-test-selector="listDataExchange" data-section="DataExchange"><a class="nav-link" href="/commerce-connector/DataExchange/GetDataExchangeLibraryList?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkDataExchange">@CommerceConnector_Resources.DataExchange</a></li>
                    <li class="nav-item" data-test-selector="listProcessingLog" data-section="ProcessingLog"><a class="nav-link active" href="/commerce-connector/DataExchange/GetProcessingLogs?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkProcessingLog">@CommerceConnector_Resources.DataExchangeLog</a></li>
                    <li class="nav-item" data-test-selector="listImportLog" data-section="ImportLog"><a class="nav-link" href="/commerce-connector/DataExchange/GetZnodeImportLogs?@CCAdminConstant.CCAdminToken=@token" data-test-selector="lnkImportLog">@CommerceConnector_Resources.LabelZnodeImportLog</a></li>
                </ul>
            </div>
        </div>

        <!-- Right Panel -->
        <div class="col-md-9 col-lg-10 px-3" id="configContent">
            <div class="processing-log page-content">
                <div class="controls">
                    <div class="filter-search d-flex">
                        <input type="text" id="globalSearch" maxlength="130" placeholder="Search..." data-test-selector="txtSearch" aria-label="Search List" />
                        <button id="globalSearchbtn" class="btn-search" data-test-selector="btnSearchIcon"><em class="z-search"></em></button>
                    </div>
                </div>

                <div id="processingLogsGrid" class="ag-theme-alpine mt-3" data-test-selector="divProcessingLogsGrid"></div>

                <div class="show-per-page d-flex mt-3" style="display: none;">
                    <div id="paginationControls" class="me-3" data-test-selector="divPaginationControl"></div>
                    <div id="customRowInfo" class="total-record" data-test-selector="divCustomRowInfo" style="display: none;"></div>
                    <div class="show-page-count ps-3" style="display: none;">
                        <div class="d-flex align-items-center">
                            <label class="pe-3" data-test-selector="lblShow">@CommerceConnector_Resources.Show</label>
                            <select id="pageSizeSelect" data-test-selector="drpPageSizeSelect" aria-label="Select Number of Page">
                                <option value="10" selected>10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div id="noRecordsMessage" class="text-center mt-3" style="display: none;" data-test-selector="divNoRecordsFound">
                    @CommerceConnector_Resources.NoRecordsFound
                </div>

                <div class="modal fade" id="processingLogDetailsModal" tabindex="-1">
                    <div id="logModalDialog" class="modal-dialog modal-dialog-centered modal-xl">
                        <div id="processingLogDetailsContent" class="modal-content">
                            <div class="modal-header">
                                <h3 class="modal-title" data-test-selector="hdgProcessingLogDetails">@CommerceConnector_Resources.ProcessingLogDetails</h3>
                                <button type="button" class="btn-text-icon" aria-label="Go Back to Processing Log List" data-bs-dismiss="modal" data-test-selector="btnBack">
                                    <em class='z-back'  ></em>
                                    @CommerceConnector_Resources.LabelBack
                                </button>
                            </div>
                            <div class="modal-body" id="target-log-details-to-display" data-test-selector="divProcessingLogDetails">
                                @Html.Partial("_ProcessingLogDetails")
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@Html.Hidden("hdnExchangeName", exchangeName);
@Html.Hidden("hdnERPBaseDefinitionId", erpBaseDefinitionId);
