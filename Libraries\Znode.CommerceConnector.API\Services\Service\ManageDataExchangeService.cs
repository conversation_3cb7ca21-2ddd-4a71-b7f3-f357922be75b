﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using Renci.SshNet;
using System.Collections.Specialized;
using System.Data;
using System.Diagnostics;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Sockets;
using System.Text;
using Znode.CommerceConnector.API.IHelpers;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.CommerceConnector.Processor;
using Znode.CommerceConnector.Processor.Helper;
using Znode.Libraries.Data;
using Znode.Libraries.Data.Data;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Services
{
    public class ManageDataExchangeService : IManageDataExchangeService
    {
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;
        private readonly AppDbContext _context;
        private readonly IMapper _mapper;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IHangfireHelper _hangfireHelper;

        public ManageDataExchangeService(AppDbContext context, IMapper mapper, IHttpClientFactory httpClientFactory, IOutputHandlerInitializer outputHandler, IInputHandlerInitializer inputHandler, IParser iParser, IHttpContextAccessor httpContextAccessor, IHangfireHelper hangfireHelper)
        {
            _context = context;
            _mapper = mapper;
            _httpClientFactory = httpClientFactory;
            _outputHandler = outputHandler;
            _inputHandler = inputHandler;
            _iParser = iParser;
            _httpContextAccessor = httpContextAccessor;
            _hangfireHelper = hangfireHelper;
        }
        public BaseDefinitionModel GetDataExchangeDetailsByID(int id)
        {
            BaseDefinitionModel baseDefinitionModel = new BaseDefinitionModel();
            baseDefinitionModel = GetSourceDestinationDetails(id);
            // Scheduler configuration setting
            baseDefinitionModel.SchedulerSettingModel = HelperMethods.GetSchedulerConfiguration(id);
            return baseDefinitionModel;
        }

        public BaseDefinitionModel GetSourceDestinationDetails(int id)
        {
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter> { new SqlParameter("@ERPBaseDefinitionId", id) };
            DataSet sourceResponse = znodeViewRepository.ExecuteStoredProcedureWithMultipleResults("cco.Znode_GetSourceDataExchangeByERPId", sqlParameters);
            DataSet destinationResponse = znodeViewRepository.ExecuteStoredProcedureWithMultipleResults("cco.Znode_GetDestinationDataExchangeByERPId", sqlParameters);

            if (sourceResponse.Tables.Count > 0 && sourceResponse.Tables[0].Rows.Count > 0 && destinationResponse.Tables.Count > 0 || destinationResponse.Tables[0].Rows.Count > 0)
            {
                return MapToBaseDefinitionModel(sourceResponse, destinationResponse);
            }
            return new BaseDefinitionModel();
        }
        public BaseDefinitionModel MapToBaseDefinitionModel(DataSet sourceDataSet, DataSet destinationDataSet)
        {
            if (sourceDataSet?.Tables.Count == 0 || sourceDataSet.Tables[0].Rows.Count == 0 || destinationDataSet?.Tables.Count == 0 || destinationDataSet.Tables[0].Rows.Count == 0)
                return null;

            DataRow firstRow = sourceDataSet.Tables[0].Rows[0];
            DataRow secondRow = destinationDataSet.Tables[0].Rows[0];

            KeyValueListModel sourceParams = ExtractParams(sourceDataSet.Tables[1], "Header", "Query");
            KeyValueListModel destinationParams = ExtractParams(destinationDataSet.Tables[1], "DestinationHeader", "DestinationQuery");

            int id = GetInt(firstRow, "APIConfigurationId");
            return new BaseDefinitionModel
            {
                ERPBaseDefinitionId = GetInt(firstRow, "ERPBaseDefinitionId"),
                DataExchangeLibraryId = GetInt(firstRow, "DataExchangeLibraryId"),
                Name = GetString(firstRow, "Name"),
                Format = GetString(firstRow, "Format"),
                Version = GetString(firstRow, "Version"),
                DataSource = GetString(firstRow, "DataSource"),
                DataDestination = GetString(firstRow, "DataDestination"),
                TriggerOrigin = GetString(firstRow, "TriggerOrigin"),
                Description = GetString(firstRow, "Description"),
                Tags = GetString(firstRow, "Tags"),
                Access = GetString(firstRow, "Access"),
                Library = GetString(firstRow, "Library"),
                Collection = GetString(firstRow, "Collection"),
                ProcessorFileName = GetString(firstRow, "ProcessorFileName"),
                IsCustomProcessingRequired = GetBool(firstRow, "IsCustomProcessingRequired"),
                MethodName = GetString(firstRow, "MethodName"),

                TransmissionConfigurations = new TransmissionConfigurationModel
                {
                    TransmissionConfigurationId = GetInt(firstRow, "TransmissionConfigurationId"),
                    TransmissionMode = GetString(firstRow, "SourceTransmissionMode"),
                    DestinationTransmissionMode = GetString(firstRow, "DestinationTransmissionMode"),

                    SFTPConfig = new SftpTransmissionConfigurationModel
                    {
                        SFTPConfigurationId = GetInt(firstRow, "SFTPConfigurationId"),
                        TransmissionConfigurationId = GetInt(firstRow, "TransmissionConfigurationId"),
                        ServerAddress = GetString(firstRow, "ServerAddress"),
                        FolderPath = GetString(firstRow, "FolderPath"),
                        Filename = GetString(firstRow, "FileName"),
                        Username = GetString(firstRow, "Username"),
                        Password = GetString(firstRow, "Password"),
                        ActionAfterRetrieval = GetString(firstRow, "ActionAfterRetrieval"),
                        PortNumber = GetIntValue(firstRow, "PortNumber"),
                        Format = GetString(firstRow, "SFTPFormat")
                    },
                    HttpsConfig = new HttpsTransmissionConfigurationModel
                    {
                        APIConfigurationId = GetInt(firstRow, "APIConfigurationId"),
                        TransmissionConfigurationId = GetInt(firstRow, "TransmissionConfigurationId"),
                        HttpAction = GetString(firstRow, "APIAction"),
                        AuthenticationType = GetString(firstRow, "AuthType"),
                        GrantType = GetString(firstRow, "GrantType"),
                        AccessTokenUrl = GetString(firstRow, "AccessTokenUrl"),
                        ClientId = GetString(firstRow, "ClientId"),
                        ClientSecret = GetString(firstRow, "ClientSecret"),
                        Username = GetString(firstRow, "ClientId"),
                        Password = GetString(firstRow, "ClientSecret"),
                        APIKey = GetString(firstRow, "AccessTokenUrl"),
                        Scope = GetString(firstRow, "Scope"),
                        LocationOfCredentials = GetString(firstRow, "LocationOfCredentials"),
                        Endpoint = GetString(firstRow, "Endpoint"),
                        HeaderParams = sourceParams.HeaderParams,
                        QueryParams = sourceParams.QueryParams,
                        Format = GetString(firstRow, "APIFormat")
                    },
                    SftpConfigDestination = new SftpTransmissionConfigurationModel
                    {
                        SFTPConfigurationId = GetInt(secondRow, "SFTPConfigurationId"),
                        TransmissionConfigurationId = GetInt(firstRow, "TransmissionConfigurationId"),
                        ServerAddress = GetString(secondRow, "ServerAddress"),
                        FolderPath = GetString(secondRow, "FolderPath"),
                        Filename = GetString(secondRow, "FileName"),
                        Username = GetString(secondRow, "Username"),
                        Password = GetString(secondRow, "Password"),
                        ActionAfterRetrieval = GetString(secondRow, "ActionAfterRetrieval"),
                        PortNumber = GetIntValue(secondRow, "PortNumber"),
                        Format = GetString(secondRow, "SFTPFormat")
                    },
                    HttpsConfigDestination = new HttpsTransmissionConfigurationModel
                    {
                        APIConfigurationId = GetInt(secondRow, "APIConfigurationId"),
                        TransmissionConfigurationId = GetInt(firstRow, "TransmissionConfigurationId"),
                        HttpAction = GetString(secondRow, "APIAction"),
                        AuthenticationType = GetString(secondRow, "AuthType"),
                        GrantType = GetString(secondRow, "GrantType"),
                        AccessTokenUrl = GetString(secondRow, "AccessTokenUrl"),
                        ClientId = GetString(secondRow, "ClientId"),
                        ClientSecret = GetString(secondRow, "ClientSecret"),
                        Scope = GetString(secondRow, "Scope"),
                        Username = GetString(secondRow, "ClientId"),
                        Password = GetString(secondRow, "ClientSecret"),
                        APIKey = GetString(secondRow, "AccessTokenUrl"),
                        LocationOfCredentials = GetString(secondRow, "LocationOfCredentials"),
                        Endpoint = GetString(secondRow, "Endpoint"),
                        HeaderParams = destinationParams.HeaderParams,
                        QueryParams = destinationParams.QueryParams,
                        Format = GetString(secondRow, "APIFormat")
                    }
                },
                StatusActivationModel = new StatusActivationModel
                {
                    Status = GetString(secondRow, "IsActive"),
                    InformationNotification = GetString(secondRow, "InformationNotifications"),
                    WarningNotification = GetString(secondRow, "WarningNotifications"),
                    ErrorNotification = GetString(secondRow, "ErrorNotifications")
                }
            };
        }

        private int? GetIntValue(DataRow row, string columnName)
        {
            return row[columnName] != DBNull.Value ? Convert.ToInt32(row[columnName]) : (int?)null;
        }

        private int GetInt(DataRow row, string columnName)
        {
            return row[columnName] != DBNull.Value ? Convert.ToInt32(row[columnName]) : 0;
        }

        private string GetString(DataRow row, string columnName)
        {
            return row[columnName] != DBNull.Value ? row[columnName].ToString() : null;
        }

        private bool GetBool(DataRow row, string columnName)
        {
            return row[columnName] != DBNull.Value ? Convert.ToBoolean(row[columnName]) : false;
        }

        private KeyValueListModel ExtractParams(DataTable paramTable, string headerType, string queryType)
        {
            KeyValueListModel keyValueListModel = new KeyValueListModel();

            if (paramTable == null) return keyValueListModel;

            foreach (DataRow row in paramTable.Rows)
            {
                string type = row["type"]?.ToString();
                string key = row["Key"]?.ToString();
                string value = row["Value"]?.ToString();

                KeyValueModel kv = new KeyValueModel { Key = key, Value = value };

                if (string.Equals(type, headerType, StringComparison.OrdinalIgnoreCase))
                    keyValueListModel.HeaderParams.Add(kv);
                else if (string.Equals(type, queryType, StringComparison.OrdinalIgnoreCase))
                    keyValueListModel.QueryParams.Add(kv);
            }

            return keyValueListModel;
        }


        public bool SaveDataExchangeDetails(BaseDefinitionModel model)
        {
            bool status = false;
            // Save transmission source and destination details.
            status = SaveSourceDestinationDetails(model);
            // Save Scheduler configuration details.
            bool schedulerStatus = HelperMethods.SaveSchedulerConfiguration(model?.SchedulerSettingModel);
            // Save Activation details
            bool statusActivation = SaveActivationDetails(model?.StatusActivationModel);

            //Save base definition details
            int erpId = CreateEditBaseDataExchange(model);
            bool isBaseDefinitionSaved = erpId != 0 ? true : false;

            // Save Configuration Change Details
            SaveConfigurationChangeDetails(model.ConfigurationChangeJsonData, model.ERPBaseDefinitionId);
            bool saveStatus = status && schedulerStatus && statusActivation && isBaseDefinitionSaved;
            if (saveStatus)
            {
                _hangfireHelper.CreateHangfireJob(model.SchedulerSettingModel, model.StatusActivationModel.Status);
            }
            return saveStatus;
        }

        private bool SaveSourceDestinationDetails(BaseDefinitionModel model)
        {
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            SqlConnection conn = GetSqlConnection();

            if (HelperUtility.IsNull(model) || HelperUtility.IsNull(model.TransmissionConfigurations))
            {
                return false;
            }

            string transmissionTableName = CreateTempTableForTransmissionMode(conn);
            string sourceTableName = CreateTableForSourceORDestination(conn, model.TransmissionConfigurations.TransmissionMode);
            string destinationTableName = model.SchedulerSettingModel.SchedulerType.Equals("RealTime") ? string.Empty : CreateTableForSourceORDestination(conn, model.TransmissionConfigurations.DestinationTransmissionMode);

            DataTable sourceTable, destinationTable = null;
            DataTable transmissionTable = GetTransmissionTable(model.TransmissionConfigurations);

            TransmissionConfigurationModel sourceConfig = model.TransmissionConfigurations;
            sourceTable = sourceConfig.TransmissionMode.Equals("APIHandler") ? GetAPITableData(sourceConfig.HttpsConfig, "Source") : GETSFTPTableData(sourceConfig.SFTPConfig, "Source");

            // Check schedule type
            if (model.SchedulerSettingModel.SchedulerType.Equals("Scheduled") || model.SchedulerSettingModel.SchedulerType.Equals("OnDemand"))
            {
                TransmissionConfigurationModel destinationConfig = model.TransmissionConfigurations;
                destinationTable = destinationConfig.DestinationTransmissionMode.Equals("APIHandler") ? GetAPITableData(destinationConfig.HttpsConfigDestination, "Destination") : GETSFTPTableData(destinationConfig.SftpConfigDestination, "Destination");
            }

            ProcessData(transmissionTable, sourceTable, destinationTable, transmissionTableName, sourceTableName, destinationTableName);

            List<SqlParameter> sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@TransmissionTable", Value=transmissionTableName},
                new SqlParameter(){ParameterName="@SourceTable", Value=sourceTableName},
                new SqlParameter(){ParameterName="@DestinationTable", Value=destinationTableName},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            int Status = 0;
            znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_InsertUpdateDataExchangeDetails", sqlParameters, 3, out Status);
            return Convert.ToBoolean(Status);
        }

        private bool SaveActivationDetails(StatusActivationModel model)
        {
            int status = 0;
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters1 = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@IsActive", Value=model.Status},
                new SqlParameter(){ParameterName="@InformationNotifications", Value=model.InformationNotification ?? string.Empty},
                new SqlParameter(){ParameterName="@WarningNotifications", Value=model.WarningNotification ?? string.Empty},
                new SqlParameter(){ParameterName="@ErrorNotifications", Value=model.ErrorNotification ?? string.Empty},
                new SqlParameter(){ParameterName="@UserId", Value=0},
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId", Value=model.ERPBaseDefinitionId},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            DataTable response = znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_InsertUpdateERPDataExchangeStatus", sqlParameters1, 6, out status);
            return Convert.ToBoolean(status);
        }

        private bool SaveConfigurationChangeDetails(string configurationChangeJson, int erpId)
        {
            if (HelperUtility.IsNotNull(configurationChangeJson))
            {
                int status = 0;
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>(){
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId ", Value=erpId},
                new SqlParameter(){ParameterName="@ChangeLogJson ", Value=configurationChangeJson},
                new SqlParameter(){ParameterName="@UserName  ", Value=string.Empty},
                new SqlParameter(){ParameterName="@ChangeDateTime", Value=DateTime.UtcNow},
                new SqlParameter(){ParameterName="@UserId  ", Value=0},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output} };
                DataTable response = znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_InsertERPConfigurationDatalog", sqlParameters, 5, out status);
                return Convert.ToBoolean(status);
            }
            return true;
        }

        // Create TableName for TransmissionMode
        public string CreateTempTableForTransmissionMode(SqlConnection conn)
        {
            string tableName = $"tempdb..[##TransmissionTable_{Guid.NewGuid()}]";
            string command = $"CREATE TABLE {tableName} (TransmissionConfigurationId varchar(max),ERPBaseDefinitionId varchar(max) ,SourceTransmissionMode varchar(max),DestinationTransmissionMode varchar(max))";
            MakeTableForTransmissionMode(tableName, command, conn);
            return tableName;
        }

        // Create TableName for Source and Destination Transmission Mode.
        public string CreateTableForSourceORDestination(SqlConnection conn, string mode)
        {
            string tableName = string.Empty, command = string.Empty;
            if (mode.Equals("APIHandler"))
            {
                tableName = $"tempdb..[##APITransmissionTable_{Guid.NewGuid()}]";
                command = $"CREATE TABLE {tableName} (APIConfigurationId varchar(max),TransmissionConfigurationId varchar(max), APIAction varchar(max), Endpoint varchar(max), AuthType varchar(max),GrantType varchar(max) ,AccessTokenUrl varchar(max),ClientId varchar(max), ClientSecret varchar(max), Scope varchar(max), LocationOfCredentials varchar(max),Type varchar(max), QueryParameter varchar(max) ,HeaderParameter varchar(max) ,Format varchar(max))";
            }
            else
            {
                tableName = $"tempdb..[##SFTPTransmissionTable_{Guid.NewGuid()}]";
                command = $"CREATE TABLE {tableName} (SFTPConfigurationId varchar(max),TransmissionConfigurationId varchar(max) ,ServerAddress varchar(max),FolderPath varchar(max), Filename varchar(max), Username varchar(max),Password varchar(max) ,ActionAfterRetrieval varchar(max),PortNumber varchar(max), Type varchar(max), Format varchar(max))";
            }
            MakeTableForTransmissionMode(tableName, command, conn);
            return tableName;
        }

        // DataTable for the Transmission Table
        private DataTable GetTransmissionTable(TransmissionConfigurationModel model)
        {
            DataTable transmissionTable = new DataTable();
            transmissionTable.Columns.Add("TransmissionConfigurationId", typeof(int));
            transmissionTable.Columns.Add("ERPBaseDefinitionId", typeof(int));
            transmissionTable.Columns.Add("SourceTransmissionMode", typeof(string));
            transmissionTable.Columns.Add("DestinationTransmissionMode", typeof(string));

            transmissionTable.Rows.Add(
                model.TransmissionConfigurationId,
                model.ERPBaseDefinitionId,
                model.TransmissionMode,
                model.DestinationTransmissionMode
                );
            return transmissionTable;
        }

        // DataTable for the API
        private DataTable GetAPITableData(HttpsTransmissionConfigurationModel httpModel, string type)
        {
            string accessTokenUrl = httpModel.AuthenticationType.Equals("Basic") ? httpModel.APIKey : httpModel.AccessTokenUrl;
            string clientId = httpModel.AuthenticationType.Equals("Basic") ? httpModel.Username : httpModel.ClientId;
            string clientSecret = httpModel.AuthenticationType.Equals("Basic") ? httpModel.Password : httpModel.ClientSecret;

            DataTable dt = new DataTable();
            dt.Columns.Add("APIConfigurationId", typeof(int));
            dt.Columns.Add("TransmissionConfigurationId", typeof(int));
            dt.Columns.Add("APIAction", typeof(string));
            dt.Columns.Add("Endpoint", typeof(string));
            dt.Columns.Add("AuthType", typeof(string));
            dt.Columns.Add("GrantType", typeof(string));
            dt.Columns.Add("AccessTokenUrl", typeof(string));
            dt.Columns.Add("ClientId", typeof(string));
            dt.Columns.Add("ClientSecret", typeof(string));
            dt.Columns.Add("Scope", typeof(string));
            dt.Columns.Add("LocationOfCredentials", typeof(string));
            dt.Columns.Add("Type", typeof(string));
            dt.Columns.Add("QueryParameter", typeof(string));
            dt.Columns.Add("HeaderParameter", typeof(string));
            dt.Columns.Add("Format", typeof(string));

            dt.Rows.Add(
                httpModel.APIConfigurationId,
                httpModel.TransmissionConfigurationId,
                httpModel.HttpAction,
                httpModel.Endpoint,
                httpModel.AuthenticationType,
                httpModel.GrantType,
                accessTokenUrl,
                clientId,
                clientSecret,
                httpModel.Scope,
                httpModel.LocationOfCredentials,
                type,
                JsonConvert.SerializeObject(httpModel.QueryParams),
                JsonConvert.SerializeObject(httpModel.HeaderParams),
                httpModel.Format
            );
            return dt;
        }


        // Create DataTable for SFTP
        private DataTable GETSFTPTableData(SftpTransmissionConfigurationModel sftpModel, string type)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("SFTPConfigurationId", typeof(int));
            dt.Columns.Add("TransmissionConfigurationId", typeof(int));
            dt.Columns.Add("ServerAddress", typeof(string));
            dt.Columns.Add("FolderPath", typeof(string));
            dt.Columns.Add("Filename", typeof(string));
            dt.Columns.Add("Username", typeof(string));
            dt.Columns.Add("Password", typeof(string));
            dt.Columns.Add("ActionAfterRetrieval", typeof(string));
            dt.Columns.Add("PortNumber", typeof(int));
            dt.Columns.Add("Type", typeof(string));
            dt.Columns.Add("Format", typeof(string));

            dt.Rows.Add(
                sftpModel.SFTPConfigurationId,
                sftpModel.TransmissionConfigurationId,
                sftpModel.ServerAddress,
                sftpModel.FolderPath,
                sftpModel.Filename,
                sftpModel.Username,
                sftpModel.Password,
                sftpModel.ActionAfterRetrieval,
                sftpModel.PortNumber,
                type,
                sftpModel.Format
            );
            return dt;
        }



        public void ProcessData(DataTable transmissionDataTable, DataTable sourceDataTable, DataTable destinationDataTable, string transmissionTableName, string sourceTableName, string destinationTableName)
        {
            try
            {
                if (!string.IsNullOrEmpty(transmissionTableName))
                {
                    InsertData(transmissionTableName, transmissionDataTable);
                }
                if (!string.IsNullOrEmpty(sourceTableName))
                {
                    InsertData(sourceTableName, sourceDataTable);
                }
                if (!string.IsNullOrEmpty(destinationTableName))
                {
                    InsertData(destinationTableName, destinationDataTable);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ManageDataExchange.ProcessData {ex.Message}");
            }
        }

        public void InsertData(string tableName, DataTable fileData)
        {
            SqlConnection conn = GetSqlConnection();
            try
            {
                using (SqlBulkCopy bulkCopy = new SqlBulkCopy(conn))
                {
                    bulkCopy.DestinationTableName = tableName;

                    if (conn.State.Equals(ConnectionState.Closed))
                    {
                        conn.Open();
                    }

                    bulkCopy.WriteToServer(fileData);
                    conn.Close();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ManageDataExchange.InsertData {ex.Message}");
            }
            finally
            {
                if (conn.State.Equals(ConnectionState.Open))
                {
                    conn.Close();
                }
            }
        }

        public virtual void MakeTableForTransmissionMode(string table, string tableCommand, SqlConnection conn)
        {
            try
            {
                SqlCommand cmd = new SqlCommand(tableCommand, conn);

                if (conn.State.Equals(ConnectionState.Closed))
                    conn.Open();

                cmd.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                throw new Exception($"Exception in ManageDataExchange.MakeTableForTransmissionMode {ex.Message}");
            }
        }

        public SqlConnection GetSqlConnection()
        {
            return new SqlConnection(HelperMethods.ConnectionString);
        }

        public BaseDefinitionModel GetBaseDefinitionDetailsByID(int erpId)
        {
            if (erpId > 0)
            {                
                List<BaseDefinitionModel> baseDefinitions = (
                                        from a in _context.ZnodeErpbaseDefinitions
                                        join b in _context.ZnodeErpdataExchangeStatuses
                                            on a.ErpbaseDefinitionId equals b.ErpbaseDefinitionId into statusGroup
                                        from b in statusGroup.DefaultIfEmpty()
                                        where a.ErpbaseDefinitionId == erpId
                                        select new BaseDefinitionModel
                                        {
                                            ERPBaseDefinitionId = a.ErpbaseDefinitionId,
                                            Name = a.Name,
                                            DataSource = a.DataSource,
                                            DataDestination = a.DataDestination,
                                            Version = a.Version,
                                            Collection = a.Collection,
                                            TriggerOrigin = a.TriggerOrigin,
                                            Tags = a.Tags,
                                            Access = a.Access,
                                            Format = a.Format,
                                            IsCustomProcessingRequired = a.IsCustomProcessingRequired,
                                            ProcessorFileName = a.ProcessorFileName,
                                            Description = a.Description,
                                            Library = a.Library,
                                            StatusActivationModel = new StatusActivationModel
                                            {
                                                Status = (b != null && b.IsActive) ? "True" : "False"
                                            }
                                        }).ToList();

                return baseDefinitions.FirstOrDefault();
            }
            return null;
        }

        public int CreateEditBaseDataExchange(BaseDefinitionModel model)
        {
            string jsonData = JsonConvert.SerializeObject(model);
            HttpRequest request = _httpContextAccessor.HttpContext?.Request;
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();

            List<SqlParameter> sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@BaseDefinitionJson", Value=jsonData},
                new SqlParameter(){ParameterName="@UserId", Value=0},
                new SqlParameter(){ParameterName="@ProcessorFileName", Value=model.ProcessorFileName?? string.Empty},
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId ", Value=model.ERPBaseDefinitionId},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            int Status = 0;
            DataTable response = znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_InsertUpdateBaseDefinitionData", sqlParameters, 4, out Status);
            int erpId = Convert.ToInt32(response?.Rows[0]["Id"]);

            if (erpId > 0 && request != null && model.ERPBaseDefinitionId <= 0 && !string.IsNullOrEmpty(model.ProcessorFileName))
            {
                if (request.Host.Host.Equals("localhost", StringComparison.OrdinalIgnoreCase))
                {
                    string className = model.ProcessorFileName.Replace(" ", "");
                    string filePath = GenerateProcessorFile(className);
                    if (!string.IsNullOrEmpty(filePath))
                    {
                        IProcessor processorInstance = CompileAndLoad(filePath, className);
                    }
                }
            }
            return erpId > 0 ? erpId : 0;
        }

        // Generate processor file with dynamic name
        private string GenerateProcessorFile(string className)
        {
            var solutionRoot = Directory.GetParent(AppContext.BaseDirectory).Parent.Parent.Parent.Parent.FullName;
            var targetLibPath = Path.Combine(solutionRoot, "DataExchangeLibraries\\Znode.CommerceConnector.Processor", "Processors", className + ".cs");

            if (File.Exists(targetLibPath))
                return string.Empty;

            var _templatePath = Path.Combine(solutionRoot, "DataExchangeLibraries\\Znode.CommerceConnector.Processor", "Processors", "ProcessorTemplate.txt");
            string template = File.ReadAllText(_templatePath);
            string content = template.Replace("{{ClassName}}", className);
            File.WriteAllText(targetLibPath, content);
            return targetLibPath;
        }

        // Compile newly generated processor file to use it in runtime.
        private IProcessor CompileAndLoad(string filePath, string className)
        {
            var code = File.ReadAllText(filePath);
            var syntaxTree = CSharpSyntaxTree.ParseText(code);

            var references = AppDomain.CurrentDomain
                .GetAssemblies()
                .Where(a => !a.IsDynamic && !string.IsNullOrWhiteSpace(a.Location))
                .Select(a => MetadataReference.CreateFromFile(a.Location))
                .ToList();

            var requiredTypes = new[]{
            typeof(System.Net.WebHeaderCollection),
            typeof(System.IO.Compression.BrotliStream),
            typeof(Microsoft.CSharp.RuntimeBinder.CSharpArgumentInfo)
        };

            foreach (var t in requiredTypes)
            {
                var location = t.Assembly.Location;
                if (!string.IsNullOrWhiteSpace(location) && !references.Any(r => r.Display == location))
                {
                    references.Add(MetadataReference.CreateFromFile(location));
                }
            }

            var compilation = CSharpCompilation.Create(
                assemblyName: "DynamicGeneratedAssembly_" + Guid.NewGuid(),
                syntaxTrees: new[] { syntaxTree },
                references: references,
                options: new CSharpCompilationOptions(OutputKind.DynamicallyLinkedLibrary));

            using var ms = new MemoryStream();
            var result = compilation.Emit(ms);

            if (!result.Success)
            {
                var errors = string.Join("\n", result.Diagnostics.Select(d => d.ToString()));
                throw new Exception("Compilation failed:\n" + errors);
            }

            ms.Seek(0, SeekOrigin.Begin);
            var assemblyBytes = ms.ToArray();
            var context = new InMemoryAssemblyLoadContext();
            var assembly = context.LoadAssembly(assemblyBytes);

            // Dynamically build the full type name (namespace + class)
            var typeName = $"Znode.CommerceConnector.Processor.{className}";

            var type = assembly.GetType(typeName);

            if (type == null)
                throw new Exception($"Type {typeName} not found in assembly.");

            var instance = Activator.CreateInstance(type, _outputHandler, _inputHandler, _iParser);
            return (IProcessor)instance!;
        }

        public ConfigurationChangeLogListModel GetConfigurationChangeList(int id, FilterCollection filters, NameValueCollection sorts, NameValueCollection page)
        {
            DataTable response = new DataTable();
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
            List<SqlParameter> sqlParameters = new List<SqlParameter>();
            var whereClause = DynamicClauseHelper.GenerateDynamicWhereClauseForSP(filters.ToFilterDataCollection());
            var orderBy = DynamicClauseHelper.GenerateDynamicOrderByClause(sorts);
            DynamicClauseHelper.SetPaging(page, out int pagingStart, out int pagingLength);
            sqlParameters.Add(new SqlParameter("@ERPBaseDefinitionId", id));
            sqlParameters.Add(new SqlParameter("@WhereClause", whereClause));
            sqlParameters.Add(new SqlParameter("@Rows", pagingLength));
            sqlParameters.Add(new SqlParameter("@PageNo", pagingStart));
            sqlParameters.Add(new SqlParameter("@Order_BY", orderBy));
            sqlParameters.Add(new SqlParameter("@RowsCount", SqlDbType.Int) { Direction = ParameterDirection.Output });
            Dictionary<string, object> outValue = new Dictionary<string, object>();
            response = znodeViewRepository.ExecuteStoredProcedure("cco.Znode_GetERPExchangeConfigurationDataLog", sqlParameters, out outValue);
            int totalRows = (int)outValue["@RowsCount"];
            List<ConfigurationChangeLogModel> dataModelList = znodeViewRepository.ConvertDataTableToList<ConfigurationChangeLogModel>(response);
            ConfigurationChangeLogListModel configurationChangeListModel = new ConfigurationChangeLogListModel()
            {
                ConfigurationChangeLogList = dataModelList,
                TotalRows = totalRows
            };
            return configurationChangeListModel;
        }

        public async Task<bool> TestConnection(string mode, string request)
        {
            bool status = false;
            if (mode == "SFTPHandler")
            {
                var data = JsonConvert.DeserializeObject<SftpTransmissionConfigurationModel>(request);
                status = TestSFTPConnection(data);
            }
            else if (mode == "APIHandler")
            {
                var data = JsonConvert.DeserializeObject<HttpsTransmissionConfigurationModel>(request);
                status = await TestAPIConnection(data);
            }
            return status;
        }

        public bool UpdateBaseDefinitionDetailsByPutAPIAction(int id, BaseDefinitionModel model)
        {
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();

            List<SqlParameter> sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId", Value=id},
                new SqlParameter(){ParameterName="@Name", Value=model.Name},
                new SqlParameter(){ParameterName="@Format", Value=model.Format},
                new SqlParameter(){ParameterName="@Version", Value=model.Version},
                new SqlParameter(){ParameterName="@DataSource", Value=model.DataSource?? string.Empty},
                new SqlParameter(){ParameterName="@DataDestination", Value=model.DataDestination},
                new SqlParameter(){ParameterName="@TriggerOrigin", Value=model.TriggerOrigin},
                new SqlParameter(){ParameterName="@Description", Value=model.Description},
                new SqlParameter(){ParameterName="@UserId  ", Value=0},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            int Status = 0;
            DataTable response = znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_UpdateBaseDefintionDetailsByPutAPIAction", sqlParameters, 9, out Status);
            return Convert.ToBoolean(Status);
        }

        public bool UpdateBaseDefinitionDetailsByPatchAPIAction(int id, BaseDefinitionModel model)
        {
            ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();

            List<SqlParameter> sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter(){ParameterName="@ERPBaseDefinitionId", Value=id},
                new SqlParameter(){ParameterName="@DataSource", Value=model.DataSource?? string.Empty},
                new SqlParameter(){ParameterName="@DataDestination", Value=model.DataDestination},
                new SqlParameter(){ParameterName="@TriggerOrigin", Value=model.TriggerOrigin},
                new SqlParameter(){ParameterName="@Description", Value=model.Description},
                new SqlParameter(){ParameterName="@UserId  ", Value=0},
                new SqlParameter(){ParameterName = "@Status",SqlDbType = SqlDbType.Bit, Direction = ParameterDirection.Output}
            };
            int Status = 0 ;
            DataTable response = znodeViewRepository.ExecuteStoredProcedureList("cco.Znode_UpdateBaseDefintionDetailsByPatchAPIAction", sqlParameters, 6, out Status);
            return Convert.ToBoolean(Status);
        }

        protected bool TestSFTPConnection(SftpTransmissionConfigurationModel sFTPConfigurationModel)
        {
            string host = sFTPConfigurationModel.ServerAddress.Trim();
            string username = sFTPConfigurationModel.Username;
            string password = sFTPConfigurationModel.Password;
            int portNumber = sFTPConfigurationModel.PortNumber ?? 0;
            try
            {
                var connectionInfo = new Renci.SshNet.ConnectionInfo(host, portNumber, username, new PasswordAuthenticationMethod(username, password));

                using (var sftp = new SftpClient(connectionInfo))
                {
                    try
                    {
                        sftp.Connect();
                        sftp.Disconnect();
                        return true;
                    }
                    catch (Exception ex)
                    {
                        MongoLogging.LogMessage("SFTP Connection Failed: " + ex.Message, "TestSFTPConnection", TraceLevel.Error);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage("Error Message: " + ex.Message, "TestSFTPConnection", TraceLevel.Error);
                return false;
            }
        }

        protected async Task<bool> TestAPIConnection(HttpsTransmissionConfigurationModel model)
        {
            try
            {
                var client = _httpClientFactory.CreateClient();
                client.Timeout = TimeSpan.FromSeconds(30);

                if (model.AuthenticationType == "Basic")
                {
                    if (!string.IsNullOrWhiteSpace(model.APIKey))
                    {
                        client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", model.APIKey);
                    }
                    else if (!string.IsNullOrWhiteSpace(model.ClientId) && !string.IsNullOrWhiteSpace(model.ClientSecret))
                    {
                        var creds = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{model.ClientId}:{model.ClientSecret}"));
                        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", creds);
                    }
                }

                if (HelperUtility.IsNotNull(model.HeaderParams))
                {
                    foreach (var header in model.HeaderParams)
                    {
                        if (header.Key != "Authorization" || !client.DefaultRequestHeaders.Contains("Authorization"))
                        {
                            client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                        }
                    }
                }

                var url = BuildUrlWithQueryParams(model.Endpoint?.ToLower(), model.QueryParams);
                var httpRequest = new HttpRequestMessage(new HttpMethod(model.HttpAction), url);
                if (model.HttpAction == "POST")
                    httpRequest.Content = new StringContent("{}", Encoding.UTF8, "application/json");
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                var statusCode = (int)(await client.SendAsync(httpRequest, HttpCompletionOption.ResponseHeadersRead, cts.Token)).StatusCode;
                return statusCode != 404 && statusCode != 401;
            }
            catch (SocketException ex)
            {
                MongoLogging.LogMessage("SocketException: " + ex.ToString(), "TestSFTPConnection", TraceLevel.Error);
                return false;
            }
            catch (TaskCanceledException ex)
            {
                MongoLogging.LogMessage("Timeout Error Message: " + ex.Message, "TestSFTPConnection", TraceLevel.Error);
                return false;
            }
            catch (Exception ex)
            {
                MongoLogging.LogMessage("Error Message: " + ex.Message, "TestSFTPConnection", TraceLevel.Error);
                return false;
            }
        }

        private string BuildUrlWithQueryParams(string baseUrl, List<KeyValueModel> queryParams)
        {
            if (HelperUtility.IsNull(queryParams) || !queryParams.Any())
                return baseUrl;

            var queryString = string.Join("&", queryParams.Select(query =>
                $"{WebUtility.UrlEncode(query.Key)}={WebUtility.UrlEncode(query.Value)}"));

            return baseUrl.Contains("?") ? $"{baseUrl}&{queryString}" : $"{baseUrl}?{queryString}";
        }

        public bool ValidateSchedulerName(string schedulerName)
        {
            if (!string.IsNullOrWhiteSpace(schedulerName))
            {
                ZnodeViewRepository znodeViewRepository = new ZnodeViewRepository();
                List<SqlParameter> sqlParameters = new List<SqlParameter>();
                sqlParameters.Add(new SqlParameter("@SchedulerName", schedulerName));
                sqlParameters.Add(new SqlParameter("@Exist", SqlDbType.Bit) { Direction = ParameterDirection.Output });
                Dictionary<string, object> outValue = new Dictionary<string, object>();
                znodeViewRepository.ExecuteStoredProcedure("cco.Znode_ValidateDataExchangeSchedulerName", sqlParameters, out outValue);
                return (bool)outValue["@Exist"]; ;
            }
            return false;
        }
    }
}
