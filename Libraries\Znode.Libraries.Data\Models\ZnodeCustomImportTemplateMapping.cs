﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class ZnodeCustomImportTemplateMapping
{
    public int ImportTemplateMappingId { get; set; }

    public int ImportTemplateId { get; set; }

    public string SourceColumnName { get; set; } = null!;

    public string TargetColumnName { get; set; } = null!;

    public int DisplayOrder { get; set; }

    public bool IsActive { get; set; }

    public bool IsAllowNull { get; set; }

    public int CreatedBy { get; set; }

    public DateTime CreatedDate { get; set; }

    public int ModifiedBy { get; set; }

    public DateTime ModifiedDate { get; set; }
}
