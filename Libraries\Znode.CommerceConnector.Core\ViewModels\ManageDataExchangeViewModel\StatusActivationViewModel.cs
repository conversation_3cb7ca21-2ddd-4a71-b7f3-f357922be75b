﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc.Rendering;

namespace Znode.CommerceConnector.Core.ViewModels
{
    public class StatusActivationViewModel
    {
        public int? StatusId { get; set; }

        [Required(ErrorMessage = "Status is required.")]
        public bool Status { get; set; } = false;

        [Display(Name = "Information Notification")]
        public string? InformationNotification { get; set; }

        [Display(Name = "Warning Notification")]
        public string? WarningNotification { get; set; }

        [Display(Name = "Error Notification")]
        public string? ErrorNotification { get; set; }

        //public List<SelectListItem> StatusOption { get; set; }
        public List<SelectListItem> StatusOption { get; set; } = new List<SelectListItem>
        {
             new SelectListItem { Text = "Enable", Value = "True" },
             new SelectListItem { Text = "Disable", Value = "False" }
        };

        public int? ERPBaseDefinitionId { get; set; }
    }
}
