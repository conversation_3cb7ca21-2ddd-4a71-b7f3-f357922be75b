﻿using System;
using System.Collections.Generic;

namespace Znode.Libraries.Data.Models;

public partial class T2SourceTable
{
    public int? TransmissionId { get; set; }

    public int? ConfigurationId { get; set; }

    public string? Apiaction { get; set; }

    public string? Endpoint { get; set; }

    public string? AuthType { get; set; }

    public string? GrantType { get; set; }

    public string? AccessTokenUrl { get; set; }

    public string? ClientId { get; set; }

    public string? ClientSecret { get; set; }

    public string? Scope { get; set; }

    public string? LocationOfCredentials { get; set; }

    public string? Type { get; set; }

    public string? QueryParameter { get; set; }

    public string? HeaderParameter { get; set; }
}
