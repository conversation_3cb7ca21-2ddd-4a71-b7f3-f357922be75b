﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;

using System.Web.Http.Description;

using Znode.CommerceConnector.API.Services;
using Znode.CommerceConnector.Model;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Controllers
{
    [ApiController]
    [Route("ManageDataExchang")]
    public class ManageDataExchangController : BaseController
    {
        private readonly IManageDataExchangeService _service;

        public ManageDataExchangController(IManageDataExchangeService service)
        {
            _service = service;
        }

        [HttpGet("GetDataExchangeDetailsByID/{id}")]
        [ResponseType(typeof(BaseDefinitionModel))]
        public IActionResult GetDataExchangeDetailsByID(int id)
        {
            IActionResult response;
            try
            {
                BaseDefinitionModel data = _service.GetDataExchangeDetailsByID(id);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();

            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }

        [HttpPost]
        [Route("SaveDataExchangeDetails")]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult SaveDataExchangeDetails([FromBody] BaseDefinitionModel model)
        {
            try
            {
                bool result = _service.SaveDataExchangeDetails(model);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = result });
            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }

        [HttpGet("GetBaseDefinitionDetailsByID/{erpId}")]
        [ResponseType(typeof(BaseDefinitionModel))]
        public IActionResult GetBaseDefinitionDetailsByID(int erpId)
        {
            IActionResult response;
            try
            {
                BaseDefinitionModel data = _service.GetBaseDefinitionDetailsByID(erpId);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();

            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }


        [HttpPost]
        [Route("CreateEditBaseDataExchange")]
        public IActionResult CreateEditBaseDataExchange([FromBody] BaseDefinitionModel model)
        {
            IActionResult response;
            int erpId = 0;
            try
            {
                 erpId = _service.CreateEditBaseDataExchange(model);
                 response = HelperUtility.IsNotNull(erpId) ? CreateOKResponse(erpId) : new NoContentResult();
            }
            catch (Exception ex)
            {
                response = HelperUtility.IsNotNull(erpId) ? CreateOKResponse(erpId) : new NoContentResult(); ;
            }
            return response;
        }
        
        [Route("GetConfigurationChangeList/{id}")]
        [HttpGet]
        [ResponseType(typeof(ConfigurationChangeLogListModel))]
        public IActionResult GetConfigurationChangeList(int id)
        {
            IActionResult response;
            try
            {
                ConfigurationChangeLogListModel data = _service.GetConfigurationChangeList(id, Filters, Sorts, Page);
                response = HelperUtility.IsNotNull(data) ? CreateOKResponse(data) : new NoContentResult();
            }
            catch (Exception ex)
            {
                response = CreateInternalServerErrorResponse(new BaseResponse { HasError = true, ErrorMessage = ex.Message, ErrorCode = 501 });
            }
            return response;
        }   

        [HttpPost]
        [Route("TestConnection")]
        [ResponseType(typeof(TrueFalseResponse))]
        public async Task<IActionResult> TestConnection([FromBody] string request, string mode)
        {
            IActionResult response;
            try
            {
                bool data = await _service.TestConnection(mode, request);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = data });

            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }

        [HttpGet]
        [Route("ValidateSchedulerName/{schedulerName}")]
        [ResponseType(typeof(TrueFalseResponse))]
        public IActionResult ValidateSchedulerName(string schedulerName)
        {
            IActionResult response;
            try
            {
                bool data = _service.ValidateSchedulerName(schedulerName);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = data });
            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }

        [HttpPut]
        [Route("UpdateBaseDefinitionDetailsByPutAPIAction/{id}")]
        public IActionResult UpdateBaseDefinitionDetailsByPutAPIAction(int id, [FromBody] BaseDefinitionModel model)
        {
            try
            {
                bool data = _service.UpdateBaseDefinitionDetailsByPutAPIAction(id, model);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = data });
            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }

        [HttpPatch("UpdateBaseDefinitionDetailsByPatchAPIAction/{id}")]
        public IActionResult UpdateBaseDefinitionDetailsByPatchAPIAction(int id, [FromBody] BaseDefinitionModel model)
        {
            try
            {
                bool data =  _service.UpdateBaseDefinitionDetailsByPatchAPIAction(id, model);
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = data });
            }
            catch (Exception ex)
            {
                return CreateOKResponse(new TrueFalseResponse { IsSuccess = false });
            }
        }
    }
}
