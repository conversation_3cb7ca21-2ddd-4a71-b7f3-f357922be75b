﻿declare var agGrid: any;

class ZnodeImportLogs {
    private logsGridInitialized = false;
    private detailsGridInitialized = false;
    private defaultZoom = window.devicePixelRatio;

    private logsGridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0 // disables AG Grid's internal sorting
        },
        rowData: [],
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,

        onGridReady: (params: any) => {
            this.logsGridOptions.api = params.api;
            this.logsGridOptions.columnApi = params.columnApi;
            const handleResize = () => {
                if (this.logsGridOptions?.api) {
                    this.logsGridOptions.api.sizeColumnsToFit();
                }
            };

            $(window).on("resize", handleResize);
            setTimeout(() => {
                if (this.logsGridOptions?.api) {
                    this.logsGridOptions.api.sizeColumnsToFit();
                }
            }, 100);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const activeSort = sortState.find((c: any) => c.sort);
                DynamicGrid.prototype.setSorting(activeSort?.colId || null, activeSort?.sort || null);
                this.LoadZnodeImportLogsGrid(1);
            });
        }
    };

    private detailsGridOptions: any = {
        columnDefs: [],
        defaultColDef: {
            sortable: true,
            filter: false,
            resizable: true,
            comparator: () => 0
        },
        rowData: [],
        context: {},
        suppressPaginationPanel: true,
        suppressScrollOnNewData: true,
        rowSelection: "multiple",
        suppressRowClickSelection: true,
        rowStyle: {
            borderBottom: '1px solid #e0e0e0'
        },

        onGridReady: (params: any) => {
            this.detailsGridOptions.api = params.api;
            this.detailsGridOptions.columnApi = params.columnApi;
            // Window resize or zoom handler
            const handleResizeOrZoom = () => {
                const currentZoom = window.devicePixelRatio;

                // If zoom level changed, re-auto-size
                if (currentZoom >= this.defaultZoom) {
                    const allColumnIds = params.columnApi.getAllColumns().map(col => col.getColId());
                    params.columnApi.autoSizeColumns(allColumnIds, false);
                } else if (currentZoom < this.defaultZoom) {
                    // Only resized, so fit to width
                    params.api.sizeColumnsToFit();
                }
            };

            $(window).on("resize", handleResizeOrZoom);
            setTimeout(() => {
                const allColumnIds = params.columnApi.getAllColumns().map(col => col.getColId());
                params.columnApi.autoSizeColumns(allColumnIds);
            }, 100);
            params.api.addEventListener('sortChanged', () => {
                const sortState = params.columnApi.getColumnState();
                const activeSort = sortState.find((c: any) => c.sort);
                const logId = params.context.logId;
                DynamicGrid.prototype.setSorting(activeSort?.colId || null, activeSort?.sort || null);
                ProcessingLog.prototype.ShowLogDetails(1, logId);
            });
        }
    };

    LoadZnodeImportLogsGrid(page: number) {
        ZnodeGlobal.prototype.ShowLoader();
        var exchangeName = $('#hdnImportExchangeName').val();
        const request = {
            page: page,
            pageSize: Number($('#pageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#globalSearch").val()
        };

        $.ajax({
            url: '/commerce-connector/DataExchange/RenderLogsGrid',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                gridModel: request,
                exchangeName: exchangeName,
                isFromImportLogs: true
            }),
            success: (res) => {
                const rowCount = res?.rows?.length || 0;

                if (rowCount === 0) {
                    if (this.logsGridInitialized) {
                        this.logsGridOptions.api.setRowData([]);
                    }

                    DataExchange.prototype.showNoRecordsMsg("znodeImportLogsGrid");
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    DataExchange.prototype.showRecords("znodeImportLogsGrid");
                }
                DynamicGrid.prototype.setTotalRows(res.totalCount);

                if (!this.logsGridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#znodeImportLogsGrid');
                    new agGrid.Grid(gridDiv!, this.logsGridOptions);
                    this.logsGridInitialized = true;
                }
                if (!this.logsGridOptions.columnDefs?.length && res.columns) {
                    const columnsToSkipTooltips = ['importProcessLogId', 'importTemplateId'];
                    const dateFields = ['processStartedDate', 'processCompletedDate'];
                    const dynamicCols = res.columns.map(c => {
                        const field = c.field as string;
                        const column: any = {
                            field: c.field,
                            headerName: c.title,
                            sortable: c.sortable === true,
                            filter: false,
                            hide: ["successRecordCount", "failedRecordcount", "totalProcessedRecords", "createdBy", "createdDate", "modifiedBy", "modifiedDate", "errorMessage", "hasError"].indexOf(field) !== -1,
                            headerTooltip: c.title,
                            resizable: true
                        };
                        if (['importProcessLogId'].indexOf(field) > -1) {
                            column.width = 110;
                            column.maxWidth = 130;
                        }
                        else if (['importTemplateId'].indexOf(field) > -1) {
                            column.width = 130;
                            column.maxWidth = 160;
                        }
                        if (['processStartedDate', 'processCompletedDate'].indexOf(field) > -1) {
                            column.valueFormatter = (params) => ZnodeGlobal.prototype.FormatDateTime24Hour(params.value)
                        }
                        if (columnsToSkipTooltips.indexOf(field) === -1) {
                            if (!(dateFields.indexOf(field) === -1)) {
                                column.tooltipValueGetter = (params) => ZnodeGlobal.prototype.FormatDateTime24Hour(params.value);
                            }
                            else {
                                column.tooltipField = field;
                            }
                        }
                        return column;
                    });
                    const actionCol = {
                        headerName: "Actions",
                        width: 120,
                        sortable: false,

                        cellRenderer: function (params) {
                            const id = params.data?.importProcessLogId;
                            return `
                                <a href="#" id="viewImportLogDetails" data-action="view" data-id="${id}" title="View" data-bs-toggle="modal" data-bs-target="#importLogDetailsModal"><i class="z-view"></i></a>
                            `;
                        }
                    };

                    this.logsGridOptions.api.setColumnDefs([...dynamicCols, actionCol]);
                }

                this.logsGridOptions.api.setRowData(res.rows);
                setTimeout(() => {
                    this.logsGridOptions.api.sizeColumnsToFit();
                }, 100);

                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, '', (page) => {
                    this.LoadZnodeImportLogsGrid(page);
                });
                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount);
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    ShowImportLogDetails(page: number, logId: number = 0) {
        ZnodeGlobal.prototype.ShowLoader();
        $('#processLogId').val(logId);

        const request = {
            page: page,
            pageSize: Number($('#importLogPageSizeSelect').val()),
            sortField: DynamicGrid.prototype.currentSortField,
            sortDirection: DynamicGrid.prototype.currentSortDirection,
            globalSearch: $("#importLogsGlobalSearch").val()
        };

        $.ajax({
            url: '/commerce-connector/DataExchange/ShowLogDetails',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                request: request,
                processLogId: logId,
                isFromImportLogs: true
            }),
            success: (res) => {
                const rowCount = res?.rows?.length || 0;
                const target = "#target-import-log-details-to-display";

                $('#totalRecordsField').html(res.totalProcessedRecords);
                $('#succeededRecordsField').html(res.successRecordCount);
                $('#failedRecordsField').html(res.failedRecordcount);

                this.detailsGridOptions.context = { logId: logId };

                if (rowCount === 0) {
                    if (this.detailsGridInitialized) {
                        this.detailsGridOptions.api.setRowData([]);
                    }

                    $("#importLogDetailsGrid").hide();
                    $("#importLogsPaginationControls").hide();
                    $("#logsShowPerPage").hide();
                    $("#logsShowPerCount").hide();
                    $("#logsCustomRowInfo").hide();
                    $("#logsNoRecordsMessage").show();
                    $("#importLogDetailsContent").show();
                    $("body").css("overflow", "hidden");
                    $(target).append(res);
                    $(target).fadeIn("fast");
                    ZnodeGlobal.prototype.HideLoader();
                    return;
                } else {
                    $("#logsNoRecordsMessage").hide();
                    $("#logsShowPerPage").show();
                    $("#logsShowPerCount").show();
                    $("#logsCustomRowInfo").show();
                    $("#importLogDetailsGrid").show();
                    $("#importLogsPaginationControls").show();
                }

                DynamicGrid.prototype.setTotalRows(res.totalCount);

                $('#importLogDetailsGrid').addClass('ag-grid-hidden');

                if (!this.detailsGridInitialized) {
                    const gridDiv = document.querySelector<HTMLElement>('#importLogDetailsGrid');
                    new agGrid.Grid(gridDiv!, this.detailsGridOptions);
                    this.detailsGridInitialized = true;
                }

                let dynamicCols = [];
                if (!this.detailsGridOptions.columnDefs?.length && res.columns) {
                    dynamicCols = res.columns.map(c => ({
                        field: c.field,
                        headerName: c.title,
                        sortable: c.sortable === true,
                        filter: false,
                        hide: ["importProcessLogId"].indexOf(c.field) !== -1,
                    }));
                }

                $("#importLogDetailsContent").show();
                $("body").css("overflow", "hidden");
                $(target).append(res);
                $(target).fadeIn("fast");

                setTimeout(() => {
                    const gridDiv = document.querySelector<HTMLElement>('#importLogDetailsGrid');

                    if (this.detailsGridInitialized && this.detailsGridOptions.api) {
                        this.detailsGridOptions.api.destroy();
                        this.detailsGridInitialized = false;
                    }

                    gridDiv.innerHTML = '';

                    new agGrid.Grid(gridDiv!, this.detailsGridOptions);

                    if (dynamicCols.length) {
                        this.detailsGridOptions.api.setColumnDefs(dynamicCols);
                    }

                    this.detailsGridOptions.api.setRowData(res.rows);
                    this.detailsGridOptions.api.setDomLayout('normal');
                    this.detailsGridOptions.api.sizeColumnsToFit();

                    $('#importLogDetailsGrid').removeClass('ag-grid-hidden');
                    this.detailsGridInitialized = true;
                }, 350);

                DynamicGrid.prototype.RenderPagination(res.page, res.pageCount, "importLogsPaginationControls", (page) => {
                    this.ShowImportLogDetails(page, logId);
                });

                DynamicGrid.prototype.UpdateRowInfo(res.page, res.pageSize, res.filteredCount, res.totalCount, "logsCustomRowInfo");

                $("#importLogDetailsContent").show();
                $("body").css("overflow", "hidden");
                $(target).append(res);
                $(target).fadeIn("fast");
                ZnodeGlobal.prototype.HideLoader();
            },
            error: function (err) {
                console.error('Error loading page:', err);
                ZnodeGlobal.prototype.HideLoader();
            }
        });
    }

    ShowImportLogDetailsPopup(logId) {
        const target = "#target-import-log-details-to-display";
        $(target).hide();
        $('#importLogDetailsContent').hide();
        $('#importLogsGlobalSearch').val('');
        this.ShowImportLogDetails(1, logId);
    }
}

$(document).ready(function () {
    const instance = new ZnodeImportLogs();
    $("#znodeImportLogsGrid").hide();
    $("#noRecordsMessage").hide();
    if ($("#znodeImportLogsGrid").length > 0) {
        instance.LoadZnodeImportLogsGrid(1);
        $('#pageSizeSelect').on('change', () => {
            this.currentPageSize = + $('#pageSizeSelect').val();
            instance.LoadZnodeImportLogsGrid(1);
        });

        $('#globalSearchbtn').on('click', () => {
            instance.LoadZnodeImportLogsGrid(1)
        });
        $('#globalSearch').keydown(function (e) {
            if (e.keyCode === 13) {
                instance.LoadZnodeImportLogsGrid(1);
            }
        });
    }

    $('#importLogPageSizeSelect').on('change', () => {
        var logId = parseInt($('#processLogId').val().toString());
        this.currentPageSize = + $('#importLogPageSizeSelect').val();
        instance.ShowImportLogDetails(1, logId);
    });

    $('#importLogsGlobalSearchbtn').on('click', () => {
        var logId = parseInt($('#processLogId').val().toString());
        instance.ShowImportLogDetails(1, logId)
    });
    $('#importLogsGlobalSearch').keydown(function (e) {
        var logId = parseInt($('#processLogId').val().toString());
        if (e.keyCode === 13) {
            instance.ShowImportLogDetails(1, logId);
        }
    });

    $(document).on('click', '#viewImportLogDetails', function (e) {
        debugger;
        e.preventDefault();
        const id = $(this).data('id');
        instance.ShowImportLogDetailsPopup(id);
    });

    $('#importLogDetailsModal').on('hidden.bs.modal', function () {
        var page = parseInt($('#paginationControls').find('input').val());
        var pageCount = parseInt($('#paginationControls').find('.LowerCase').html().split('/')[1]);
        DynamicGrid.prototype.RenderPagination(page, pageCount, '', (page) => {
            instance.LoadZnodeImportLogsGrid(page);
        });
    });
});