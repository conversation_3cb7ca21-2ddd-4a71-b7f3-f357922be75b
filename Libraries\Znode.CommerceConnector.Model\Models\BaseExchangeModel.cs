﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Znode.CommerceConnector.Model
{
    public class BaseExchangeModel
    {
        public int ERPBaseDefinitionId { get; set; }

        public string Name { get; set; }


        public string Format { get; set; }

        public string Version { get; set; }

        public string DataSource { get; set; }

        public string DataDestination { get; set; }
        public string TriggerOrigin { get; set; }

        public string Description { get; set; }
        public string Tags { get; set; }
        public string Access { get; set; }

        public string Library { get; set; }

        public string Collection { get; set; }
        //public string Schedule { get; set; }
        //public DateTime LastRunTime { get; set; }
        //public string LastRunResult { get; set; }
        //public bool Status { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }

        public int ModifiedBy { get; set; }

        public DateTime ModifiedDate { get; set; }

    }
}
