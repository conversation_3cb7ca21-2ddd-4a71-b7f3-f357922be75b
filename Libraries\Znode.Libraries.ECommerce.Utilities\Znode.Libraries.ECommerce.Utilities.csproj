﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="MongoDB.Bson" Version="3.4.0" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
	<PackageReference Include="Azure.Storage.Blobs" Version="12.18.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Znode.CommerceConnector.Model\Znode.CommerceConnector.Model.csproj" />
  </ItemGroup>

</Project>
