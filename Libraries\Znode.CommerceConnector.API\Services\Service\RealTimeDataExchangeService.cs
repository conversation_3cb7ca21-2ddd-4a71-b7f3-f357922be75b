﻿using System.Diagnostics;
using System.Reflection;
using Znode.CommerceConnector.API.Helpers;
using Znode.CommerceConnector.API.IHelpers;
using Znode.CommerceConnector.InputHandler;
using Znode.CommerceConnector.Model;
using Znode.CommerceConnector.OutputHandler;
using Znode.CommerceConnector.Parser;
using Znode.Libraries.Data.Helpers;
using Znode.Libraries.ECommerce.Utilities;

namespace Znode.CommerceConnector.API.Services
{
    public class RealTimeDataExchangeService : IRealTimeDataExchangeService
    {
        public object Result { get; set; }
        IAPIDataHelper _APIDataHelper;
        IOutputHandlerInitializer _outputHandler;
        IInputHandlerInitializer _inputHandler;
        IParser _iParser;

        public RealTimeDataExchangeService(IOutputHandlerInitializer outputHandler, IAPIDataHelper APIDataHelper, IInputHandlerInitializer inputHandler, IParser iParser)
        {
            _outputHandler = outputHandler;
            _APIDataHelper = APIDataHelper;
            _inputHandler = inputHandler;
            _iParser = iParser;
        }

        public dynamic RealTimeDataExchange(int erpId, dynamic inputModel)
        {
            try
            {
                string json = "";
                MongoLogging.LogMessage(CommerceConnectorConstants.RealTimeDataExchangeServiceCalled, CommerceConnectorConstants.RealTimeDataExchange, TraceLevel.Info);
                if (HelperUtility.IsNotNull(inputModel))
                {
                    inputModel = inputModel.GetRawText();
                }
                else
                {
                    inputModel = null;
                }
                ProcessorDetails processorDetails = HelperMethods.GetProcessorDetails(erpId);
                bool isSchedulerRealTime = _APIDataHelper.GetTransmissionType(erpId);
                if (isSchedulerRealTime)
                {
                    dynamic isSchedularTriggered = CallProcessor(processorDetails, erpId, inputModel);
                    return isSchedularTriggered;
                }
                return false;
            }
            catch(Exception ex)
            {
                MongoLogging.LogMessage(CommerceConnectorConstants.RealTimeDataExchangeServiceCFailed, CommerceConnectorConstants.RealTimeDataExchange, TraceLevel.Info);
                return false;
            }
        }

        public dynamic CallProcessor(ProcessorDetails processorDetails, int erpId, dynamic inputModel)
        {
            MongoLogging.LogMessage(CommerceConnectorConstants.CallProcessorMethodCalled, CommerceConnectorConstants.TriggerTaskSchedularService, TraceLevel.Info);
            Assembly _erpAssembly = Assembly.Load("Znode.CommerceConnector.Processor");
            Type _erpClassName = (_erpAssembly?.GetTypes()).FirstOrDefault(g => g.Name == Convert.ToString(processorDetails.ProcessorFileName));

            if (!Equals(_erpClassName, null))
            {
                //Create instance
                object _erpInstance = Activator.CreateInstance(_erpClassName, new object[] { _outputHandler, _inputHandler, _iParser });
                MethodInfo _method = _erpClassName.GetMethods().FirstOrDefault();

                Result = _method?.Invoke(_erpInstance, new object[] { processorDetails, erpId, inputModel });
            }
            return Result;
        }
    }
}
