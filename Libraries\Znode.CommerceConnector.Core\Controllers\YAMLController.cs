﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Znode.CommerceConnector.Core.IAgents;
using Znode.Libraries.Data.Models;

namespace Znode.CommerceConnector.Core.Controllers
{
    public class YAMLController : Controller
    {
        public readonly IYAMLAgent _yAMLAgent;

        public YAMLController(IYAMLAgent yAMLAgent)
        {
            _yAMLAgent = yAMLAgent;
        }

        public IActionResult DownloadSample()
        {
            dynamic content = _yAMLAgent.DownloadSample();
            return File(content, "text/yaml", "sample-mapping.yaml");
        }

        [HttpPost]
        public IActionResult UploadYaml(IFormFile yamlFile)
        {
            if (yamlFile != null && yamlFile.Length > 0)
            {
                dynamic content = _yAMLAgent.UploadYaml(yamlFile);
                return Json(new { success = true, content });
            }
            return Json(new { success = false, message = "Invalid file." });
        }

        [HttpPost]
        public IActionResult SaveYaml([FromBody] YAMLSaveRequestModel content)
        {
            bool result = _yAMLAgent.SaveYaml(content);
            if (result)
            {
                return Json(new { status = result});
            }
            else
            {
                return Json(new { status = result});
            }
        }

        public IActionResult GetSavedYAMlData(int erpId)
        {
            dynamic content = _yAMLAgent.GetSavedYamlData(erpId);
            if(content != null && content != "")
            {
                return Json(new { success = true, content });
            }
            return Json(new { success = true, content });
        }
    }
}
