﻿using Znode.CommerceConnector.Core.Models;
using Znode.CommerceConnector.Core.ViewModels;

namespace Znode.CommerceConnector.Core.IAgents
{
    public interface IGetExchangeLibrariesAgent
    {
        DataExchangeListViewModel GetLibrarieslist(GridModel gridModel);

        bool AddSelectedExchange(AddSelectedExchangeViewModel viewModel, out string message);
        bool ValidateExchangeName(string exchangeName, out string message);
    }
}
