﻿namespace Znode.Libraries.ECommerce.Utilities
{
    public struct MongoLoggingConstants
    {
        public const string ZnodeMongoDBForLog = "ZnodeMongoDBForLog";
        public const string LogMessageEntity = "logmessageentity";
        public const string LogMessageId = "LogMessageId";
        public const string Component = "Component";
        public const string TraceLevel = "TraceLevel";
        public const string LogMessage = "LogMessage";
        public const string CreatedDate = "CreatedDate";
        public const string StackTraceMessage = "StackTraceMessage";

        #region ERP Logging Messages

        public const string LogRenderDataExchangeGridMethodStart =
            "Started execution of RenderDataExchangeGrid method in ERP controller.";

        public const string LogRenderDataExchangeGridMethodEnd =
            "Completed execution of RenderDataExchangeGrid method in ERP controller.";
        public const string ConfigurationChangeLogMethodStart =
            "Started execution of ConfigurationChangeLog method in ERP controller.";

        public const string ConfigurationChangeLogMethodEnd =
            "Completed execution of ConfigurationChangeLog method in ERP controller.";

         public const string ShowLogDetailsMethodStart =
            "Started execution of ShowLogDetails method in DataExchange controller.";

        public const string ShowLogDetailsMethodEnd =
            "Completed execution of ShowLogDetails method in DataExchange controller.";
        
         public const string RenderProcessingLogsGridMethodStart =
            "Started execution of RenderProcessingLogsGrid method in DataExchange controller.";

        public const string RenderProcessingLogsGridMethodEnd =
            "Completed execution of RenderProcessingLogsGrid method in DataExchange controller.";

        #endregion

        #region Hangfire Constants
        public const string Scheduled = "Scheduled";
        public const string OneTimeScheduler = "OneTime";
        public const string RecurringScheduler = "Recurring";
        public const string HangfireJob = "HangfireJob";
        public const string JobCreationStarted = "Hangfire job creation starting for ";
        public const string JobCreated = "Hangfire job created for ";
        public const string HangfireError = "Error in Hangfire: {0}";
        public const string JobRemoved = "Hangfire job Removed for ";
        public const string JobRemovingError = "Error in removing Hangfire job: {0}";
        #endregion

        #region TaskScheduler Constant
        public const string TaskSchedulerConfiguration = "TaskSchedulerConfiguration";
        public const string TaskSchedulerCalled = "Trigger Task Scheduler method called";
        public const string CallProcessorCalled = "CallProcessor Method Called";
        public const string ProcessingLogsNotUpdated = "Processing logs are not updated in database";
        #endregion
    }
}
