﻿@charset "UTF-8";
/* 
 |   
 | Theme Name: Znode Admin.
 | Theme URL: http://www.znode.com
 | Description: Znode e-commerce framework Admin CSS.
 | Author: Znode.
 | Author URL: http://www.znode.com
 | Version: *******
 | 
*/
@font-face {
  font-family: "Roboto-Regular";
  src: url("../../fonts/Roboto-Regular.ttf") format("truetype");
  src: url("../../fonts/Roboto-Regular.eot") format("embedded-opentype");
  src: url("../../fonts/Roboto-Regular.svg") format("svg");
  src: url("../../fonts/Roboto-Regular.woff") format("woff");
}
@font-face {
  font-family: "Roboto-Medium";
  src: url("../../fonts/Roboto-Medium.ttf") format("truetype");
  src: url("../../fonts/Roboto-Medium.eot") format("embedded-opentype");
  src: url("../../fonts/Roboto-Medium.svg") format("svg");
  src: url("../../fonts/Roboto-Medium.woff") format("woff");
}
@font-face {
  font-family: "Roboto-Light";
  src: url("../../fonts/Roboto-Light.ttf") format("truetype");
  src: url("../../fonts/Roboto-Light.eot") format("embedded-opentype");
  src: url("../../fonts/Roboto-Light.svg") format("svg");
  src: url("../../fonts/Roboto-Light.woff") format("woff");
}
@font-face {
  font-family: "RobotoCondensed-Regular";
  src: url("../../fonts/RobotoCondensed-Regular.ttf") format("truetype");
  src: url("../../fonts/RobotoCondensed-Regular.eot") format("embedded-opentype");
  src: url("../../fonts/RobotoCondensed-Regular.svg") format("svg");
  src: url("../../fonts/RobotoCondensed-Regular.woff") format("woff");
}
@font-face {
  font-family: "RobotoCondensed-Light";
  src: url("../../fonts/RobotoCondensed-Light.ttf") format("truetype");
  src: url("../../fonts/RobotoCondensed-Light.eot") format("embedded-opentype");
  src: url("../../fonts/RobotoCondensed-Light.svg") format("svg");
  src: url("../../fonts/RobotoCondensed-Light.woff") format("woff");
}
@font-face {
  font-family: "RobotoCondensed-Bold";
  src: url("../../fonts/RobotoCondensed-Bold.ttf") format("truetype");
  src: url("../../fonts/RobotoCondensed-Bold.eot") format("embedded-opentype");
  src: url("../../fonts/RobotoCondensed-Bold.svg") format("svg");
  src: url("../../fonts/RobotoCondensed-Bold.woff") format("woff");
}
html, body {
  height: 100%;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
  font-size: 14px;
  color: #2A2C2E;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-transition: all 0.5s ease;
  -moz-transition: all 0.5s ease;
  -o-transition: all 0.5s ease;
  -ms-transition: all 0.5s ease;
  transition: all 0.5s ease;
  -ms-overflow-style: scrollbar;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  background-color: #fff;
}

.body-wrapper form {
  padding: 0;
}

.nopadding {
  padding: 0;
  margin: 0;
}

.required:after {
  content: " * ";
  color: #ea2f25;
  padding: 0 2px;
}

a {
  outline: medium none !important;
  text-decoration: none !important;
  color: rgb(73.4814814815, 139.0617283951, 52.9382716049);
  cursor: pointer;
}
a:hover {
  outline: medium none;
  text-decoration: none;
  color: #5db043;
}

h1, h2, h3, h4, h5, h6 {
  color: #3c4145;
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
}

h1 {
  margin: 5px 0;
  font-size: 24px;
}

h2 {
  margin: 5px 0;
  font-size: 21px;
}

h3 {
  margin: 5px 0;
  font-size: 19px;
}

h4 {
  margin: 5px 0;
  font-size: 16px;
}

h5 {
  margin: 4px 0;
  font-size: 16px;
}

h6 {
  margin: 4px 0;
  font-size: 14px;
}

label {
  margin-bottom: 0;
}

.dropdown-menu {
  border-radius: 0;
  z-index: 99;
}

.error-msg, .field-validation-error, .field-validation-error span {
  color: #FC5961 !important;
  float: left;
  width: 98%;
  font-size: 14px;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
}

.success-msg {
  color: #36882F;
}

.input-validation-error {
  border: 1px solid #FC5961 !important;
}

.caret-color {
  color: #fff;
}

input[type=text], input[type=password], input[type=email], input[type=date], input[type=number] {
  color: #2A2C2E;
  padding: 0 5px;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
  font-size: 14px;
  width: 100%;
  border: 1px solid #969EA4;
  box-shadow: none;
  outline: medium none;
  border-radius: 3px;
  height: 34px;
}

input[type=number] {
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
}
input[type=number]::-webkit-outer-spin-button, input[type=number]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

textarea {
  color: #2A2C2E;
  padding: 0 5px;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
  font-size: 14px;
  width: 100%;
  border: 1px solid #969EA4;
  box-shadow: none;
  outline: medium none;
  border-radius: 3px;
  resize: vertical;
  overflow-y: auto;
}

select {
  cursor: pointer;
}

html input[disabled], html select[disabled], html textarea[disabled], html input[readonly], html select[readonly], html textarea[readonly] {
  cursor: not-allowed;
  background-color: #EFEFEf;
}

input[type=checkbox], input[type=radio] {
  opacity: 0 !important;
  position: absolute;
  z-index: 12;
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin: 0;
}

input[type=checkbox]:checked, input[type=radio]:checked, input[type=checkbox]:focus, input[type=radio]:focus {
  outline: none !important;
  cursor: pointer;
}

input[type=checkbox] + .lbl, input[type=radio] + .lbl {
  z-index: 11;
  display: inline-block;
  margin: 0;
  min-height: 14px;
  min-width: 14px;
  font-weight: normal;
  cursor: pointer;
}

input[type=checkbox] + .lbl.padding-8::before, input[type=radio] + .lbl.padding-8::before {
  margin-right: 8px;
}

input[type=checkbox] + .lbl::before {
  font-family: Glyphicons Halflings;
  font-weight: normal;
  font-size: 13px;
  color: #5db043;
  content: " ";
  background-color: #fafafa;
  border: 2px solid #3c4145;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  display: inline-block;
  text-align: center;
  vertical-align: baseline;
  height: 15px;
  line-height: 9px;
  min-width: 16px;
  margin-right: 1px;
  margin-top: -5px;
}

input[type=radio] + .lbl::before {
  font-family: Glyphicons Halflings;
  font-weight: normal;
  font-size: 13px;
  color: #5db043;
  content: " ";
  background-color: #fafafa;
  border: 1px solid #969EA4;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 0;
  display: inline-block;
  text-align: center;
  vertical-align: baseline;
  height: 16px;
  line-height: 15px;
  min-width: 16px;
  margin-right: 1px;
  margin-top: -3px;
}

input[type=radio] + .lbl::before {
  vertical-align: middle;
}

input[type=checkbox]:checked + .lbl::before, input[type=radio]:checked + .lbl::before {
  display: inline-block;
  content: "\e013";
  background-color: #5db043;
  border-color: #5db043;
  color: #fff;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0 -15px 10px -12px rgba(0, 0, 0, 0.05), inset 15px 10px -12px rgba(255, 255, 255, 0.1);
  cursor: pointer;
}

input[type=checkbox]:hover + .lbl::before, input[type=radio]:hover + .lbl::before, input[type=checkbox] + .lbl:hover::before, input[type=radio] + .lbl:hover::before { /*border-color:$border-bgcolor-primary;*/
  cursor: pointer;
}

input[type=checkbox]:active + .lbl::before, input[type=radio]:active + .lbl::before, input[type=checkbox]:checked:active + .lbl::before, input[type=radio]:checked:active + .lbl::before { /*box-shadow:0 1px 2px rgba(0, 0, 0, 0.05),inset 0px 1px 3px rgba(0, 0, 0, 0.1);*/ }

input[type=checkbox]:disabled + .lbl::before, input[type=radio]:disabled + .lbl::before, input[type=checkbox][disabled] + .lbl::before, input[type=radio][disabled] + .lbl::before, input[type=checkbox].disabled + .lbl::before, input[type=radio].disabled + .lbl::before {
  background-color: #ddd !important;
  border-color: #EFEFEf !important;
  box-shadow: none !important;
  color: #BBB;
}

input[type=radio] + .lbl::before {
  border-radius: 32px;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
  font-size: -4px;
}

input[type=radio]:checked + .lbl::before {
  content: "•";
}

.disabled-checkbox {
  pointer-events: none !important;
  cursor: not-allowed;
}
.disabled-checkbox:before {
  pointer-events: none !important;
  background-color: rgba(242, 245, 245, 0.8) !important;
  color: rgba(242, 245, 245, 0.8) !important;
}

.overlay, .ui-widget-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.overlay {
  z-index: 99999;
}

.overlay-boxed {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  background-color: rgba(255, 255, 255, 0.82);
}

.dashboard-panel-heading {
  background: #fff;
  color: #333;
  border-color: #ddd;
  border-bottom: 0.5px solid #EFEFEf;
}

.accordion {
  box-shadow: 0 5px 8px -5px #cccccc;
}
.accordion .accordion-header {
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.accordion .accordion-button:not(.collapsed) {
  color: #212529;
  background-color: transparent;
}
.accordion .accordion-button:focus {
  box-shadow: 0 0 0 0.5px #cccccc;
}
.accordion .accordion-body .col-md-12 {
  padding: 2px 20px;
}

.form-select, .form-control {
  border: 1px solid #969EA4;
}
.form-select:focus, .form-control:focus {
  box-shadow: 0 0 0 0.5px #cccccc;
  border: 1px solid #969EA4;
}

.search-close-icon {
  background: transparent;
  border: none;
}
.search-close-icon .z-close-circle {
  font-size: 16px;
  color: #498b35;
}

.full-height h1 {
  font-size: 34px;
}
.full-height h3 {
  font-size: 28px;
}
.full-height p {
  font-size: 16px;
}

@media (max-width: 991px) {
  .container, body, html, header {
    width: 970px !important;
  }
}
.modal-title {
  font-size: 20px;
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
  display: block;
  line-height: 38px;
  color: #fff;
  text-transform: uppercase;
}

.modal-header {
  background-color: #3c4145;
  padding: 10px 14px;
}
.modal-header .modal-title {
  font-size: 20px;
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
  display: block;
  line-height: 0;
  color: #fff;
  text-transform: uppercase;
}
.modal-header .modal-header {
  background-color: #3c4145;
}
.modal-header .modal-header .close {
  color: #fff;
  opacity: 1;
  margin-top: 3px;
}
.modal-header .btn-text-icon {
  color: #fff;
}
.modal-header .btn-text-icon:hover, .modal-header .btn-text-icon:focus {
  color: #fff !important;
}

.my-validation-error {
  border: 1px solid #FC5961 !important;
}

.loader {
  position: fixed;
  inset: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99;
}
.loader img {
  width: 175px;
  height: auto;
}

body {
  padding-top: 43px;
  padding-bottom: 25px;
  background-color: #fff;
}

header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 50px;
  z-index: 9988;
  line-height: 26px;
  color: #fff;
  background: #3c4145;
  border-bottom: 1px solid #fff;
  border-top: 2px solid #5db043;
}

header .logo {
  display: block;
  color: #fff;
  font-size: 22px;
  text-transform: uppercase;
  width: fit-content;
  padding: 12px 6px;
}

/*Search CSS*/
.filter-search {
  width: auto;
}
.filter-search input[type=text] {
  width: 200px;
  height: 34px;
  padding-right: 18px;
}
.filter-search input[type=text]::placeholder {
  font-style: italic; /* Italic only for placeholder */
}
.filter-search .btn-search {
  border-left: 1px solid #969EA4;
  background-color: #fff;
  border-color: #969EA4;
  border-style: solid;
  border-width: 1px 1px 1px 0;
  color: #3c4145;
  height: 34px;
  line-height: 34px;
  padding-right: 6px;
  font-size: 16px;
  margin-left: -3px;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}

.wrapper {
  min-height: 100%;
}
.wrapper .table tbody tr td, .wrapper .table thead tr th {
  vertical-align: middle;
  height: auto;
}

.title-container {
  height: 70px;
  padding: 14px 16px;
  position: fixed;
  z-index: 999;
  background-color: #fff;
  width: 100%;
  box-shadow: 0 5px 5px -5px #cccccc;
}
.title-container h1 {
  font-size: 24px;
  margin: 0;
  color: #3c4145;
  line-height: 49px;
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
  text-transform: uppercase;
}
.title-container .total-count {
  font-size: 14px;
  vertical-align: middle;
  padding: 1px 0 0 7px;
}
.title-container .total-count .btn {
  display: inline-block;
  padding: 6px 10px;
  background: #5db043;
  line-height: 10px;
  vertical-align: middle;
  border-radius: 3px;
  margin: 0;
  margin-top: -3px;
  color: #fff;
  border: #5db043;
}

footer {
  z-index: 500;
  border-top: 1px solid #EFEFEf;
  text-align: center;
  color: #3c4145;
  height: 25px;
  line-height: 24px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: 0;
}
footer .footer-container p {
  margin-bottom: 0;
}

.page-container {
  padding: 90px 16px 20px;
  /*Data Exchange Library CSS*/
  /*Manage Data Exchange (Base Definition) CSS*/
  /*Schedule Configuration*/
  /* Processing Log CSS */
  /*Mapping CSS*/
}
.page-container .page-content .ag-theme-alpine {
  height: auto;
}
.page-container .page-content .ag-theme-alpine .ag-layout-normal {
  height: auto;
}
.page-container .page-content .ag-theme-alpine .ag-root-wrapper {
  box-shadow: 0 5px 8px -5px #cccccc;
  border: solid 0.5px #f3f3f3 !important;
}
.page-container .page-content .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body {
  background: #fff;
  margin-bottom: 0;
  border: 0.5px solid #EFEFEf;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.page-container .page-content .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-center-cols-viewport .ag-cell-value .fa {
  font-size: 15px;
}
.page-container .page-content .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-center-cols-viewport .ag-cell-value .fa-times, .page-container .page-content .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-center-cols-viewport .ag-cell-value .fa-check {
  font-size: 18px;
}
.page-container .page-content .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-center-cols-viewport .ag-cell-value .fa-times {
  color: #FC5961;
}
.page-container .page-content .ag-theme-alpine .ag-root-wrapper .ag-root-wrapper-body .ag-center-cols-viewport .ag-cell-value .fa-check {
  color: #5db043;
}
.page-container .page-content .ag-theme-alpine .ag-center-cols-container {
  min-width: 100% !important;
}
.page-container .page-content .ag-theme-alpine .ag-center-cols-container .ag-row-odd {
  background: #fff;
}
.page-container .page-content .ag-theme-alpine .ag-center-cols-container .exchange-link {
  text-decoration: underline !important;
}
.page-container .page-content .ag-theme-alpine .ag-center-cols-container .ag-row-hover.ag-full-width-row.ag-row-group:before, .page-container .page-content .ag-theme-alpine .ag-center-cols-container .ag-row-hover:not(.ag-full-width-row):before {
  background-color: rgba(242, 245, 245, 0.8);
}
.page-container .page-content .ag-header {
  background-color: rgba(242, 245, 245, 0.8);
}
.page-container .page-content .ag-header .ag-header-cell-label {
  font-weight: normal;
  border: 0;
  padding: 15px 0;
  text-transform: uppercase;
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.page-container .show-per-page {
  font-size: 12px;
  text-transform: uppercase;
}
.page-container .show-per-page .total-record, .page-container .show-per-page .show-page-count {
  display: inline-block;
  border-right: 1px solid #969EA4;
  line-height: 24px;
  padding-right: 10px;
  vertical-align: top;
  color: #2A2C2E;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.page-container .show-per-page .show-page-count {
  border-right: none;
}
.page-container .show-per-page select {
  min-width: 50px;
  float: none;
  text-align: center;
  text-align-last: center;
  line-height: 17px;
  height: 25px;
  border-radius: 5px;
}
.page-container .show-per-page .page-limit {
  border-right: 1px solid #969EA4;
  padding-right: 10px;
}
.page-container .show-per-page .page-limit .pagerTxt {
  width: 25px;
  text-align: center;
  height: 25px;
  border-radius: 5px;
  margin: 0 7px;
  padding: 0 2px;
  font-size: 12px;
}
.page-container .data-exchange-library input[type=checkbox] {
  opacity: 1 !important;
  margin: 12px 16px;
  display: block;
  border: 2px solid #cccccc;
  border-radius: 50%;
  width: 16px;
  height: 16px;
}
.page-container .data-exchange-library .form-check-input:checked {
  background-color: #5db043;
  border-color: #5db043;
  background-image: radial-gradient(circle, #fff 30%, transparent 40%);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
.page-container .data-exchange-library .form-check-input:focus {
  box-shadow: none !important;
}
.page-container .data-exchange-library a {
  cursor: default;
}
.page-container .data-exchange-library .data-exchange-note {
  font-size: 12px;
}
.page-container .base-definition .heading {
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.page-container .base-definition .view-details {
  word-break: break-word;
}
.page-container .base-definition input[type=checkbox] {
  opacity: 1 !important;
}
.page-container .base-definition input[type=checkbox]#isCustomProcessingRequired, .page-container .base-definition input[type=checkbox]#isEnable {
  accent-color: #498b35;
}
.page-container .base-definition input[disabled] {
  cursor: not-allowed !important;
}
.page-container .scheduler-status .one-time-frequency .schedule em {
  top: 10px;
  right: 10px;
  cursor: pointer;
}
.page-container .scheduler-status .one-time-frequency .schedule input.flatpickr-input[readonly] {
  background-color: #fff !important;
  cursor: pointer;
}
.page-container .scheduler-status .cron-expression-info {
  background: rgba(242, 245, 245, 0.8);
  margin: 0 10px;
}
.page-container .scheduler-status .cron-expression-info label {
  font-family: "Roboto-Medium", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.page-container .scheduler-status .cron-expression-info #toggleCronTable {
  cursor: pointer;
}
.page-container .scheduler-status #schedulerSetting h6 {
  padding-left: 10px;
}
.page-container .processing-log table, .page-container .import-log table {
  margin-left: auto;
}
.page-container .processing-log table .import-field, .page-container .import-log table .import-field {
  background: rgba(242, 245, 245, 0.8);
}
.page-container .processing-log table .import-value, .page-container .import-log table .import-value {
  width: 70px;
  text-align: center;
}
.page-container #logModalDialog .modal-content:empty {
  max-height: none;
  overflow: hidden;
}
.page-container #logModalDialog .modal-content {
  max-height: 520px;
  overflow-y: auto;
}
.page-container .mapping .CodeMirror-gutter {
  width: 30px !important;
  border-right: 1px solid #cccccc;
  background-color: #EEEEEE;
  white-space: nowrap;
}
.page-container .mapping .CodeMirror-sizer {
  margin-left: 30px !important;
}
.page-container .mapping .CodeMirror {
  background-color: #fff !important;
  border: 1px solid #cccccc !important;
  height: 200px !important;
}
.page-container .mapping .CodeMirror-cursor {
  border: 1px solid #cccccc !important;
}
.page-container .mapping .CodeMirror-placeholder {
  position: absolute;
  left: 8px;
  top: 0px;
  color: #999;
  pointer-events: none;
  font-style: italic;
  user-select: none;
  z-index: 12;
}
.page-container .mapping .editor-root {
  overflow-y: hidden !important;
}
.page-container .mapping .editor-menu {
  color: #fff;
  background-color: #626366;
}
.page-container .mapping .yaml-textarea .editor-menu {
  width: 100%;
  height: 35px;
  margin: 0;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #fff;
  background-color: #626366;
  border-bottom: 1px solid #626366;
}
.page-container .mapping .download-upload-yml .btn-text-secondary {
  width: 140px;
}
.page-container .mapping .download-upload-yml .download-btn {
  margin-left: 26px;
}
.page-container #headingMapping .accordion-button {
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);
}
.page-container .accordion-body .query-field-btn {
  margin-left: 23px;
}

.btn-text {
  display: inline-block;
  min-width: 90px;
  text-align: center;
  font-family: "Roboto-Regular", segoeui, HelveticaNeueLTStd Lt, Arial, sans-serif;
  border: none;
  color: #fff;
  border-radius: 3px;
  outline: none !important;
  font-size: 16px;
  padding: 0 15px;
  height: 34px;
  line-height: 34px;
  overflow: hidden;
  float: left;
  border-radius: 2px;
  font-family: "RobotoCondensed-Bold", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: rgba(242, 245, 245, 0.8);
  color: #3c4145;
  border: 1px solid #3c4145;
}
.btn-text:hover {
  background-color: #3c4145;
  color: #fff;
}

.btn-text-primary {
  background-color: #3c4145 !important;
  border: none;
  color: #fff;
}
.btn-text-primary:hover, .btn-text-primary:focus, .btn-text-primary:active {
  color: #fff;
  background-color: #3c4145;
}

.btn-text-secondary {
  background-color: #5db043 !important;
  border: none;
  color: #fff;
}
.btn-text-secondary:hover, .btn-text-secondary:active, .btn-text-secondary:focus {
  color: #fff;
  background-color: #498b35 !important;
}

.btn-text-icon {
  background-color: transparent;
  height: 34px;
  font-family: "RobotoCondensed-Regular", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
  color: #3c4145;
  border: 0;
  font-size: 16px;
  display: inline-block;
  line-height: 34px;
  text-transform: uppercase;
  margin-left: 10px;
  vertical-align: middle;
  outline: none !important;
}
.btn-text-icon:hover, .btn-text-icon:focus, .btn-text-icon:active {
  color: #5db043;
}
.btn-text-icon i, .btn-text-icon em {
  padding-right: 6px;
  vertical-align: middle;
  display: inline-block;
  margin-top: -2px;
}

.btn-text-group {
  position: relative;
  display: inline-block;
}
.btn-text-group .btn-text {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-text-group .btn-text-dropdown {
  border: 0;
  border-left: 1px solid #fff;
  height: 34px;
  padding: 15px 8px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background-color: #5db043;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
}
.btn-text-group .btn-text-dropdown:hover {
  background: rgb(73.4814814815, 139.0617283951, 52.9382716049);
}
.btn-text-group .btn-text-dropdown .caret {
  color: #fff;
  vertical-align: top;
}
.btn-text-group .dropdown-menu {
  margin-top: 0 !important;
  min-width: 145px;
}
.btn-text-group .dropdown-menu > li > a {
  padding: 5px 11px !important;
}
.btn-text-group .btn-dropdwn-lst {
  background: transparent;
  font-family: "RobotoCondensed-Regular", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
  border: 0;
  font-size: 14px;
  vertical-align: middle;
  outline: none !important;
  color: #3c4145;
  font-weight: normal;
  white-space: nowrap;
  clear: both;
}

.dropdown-button {
  display: inline-block;
  padding-left: 10px;
  line-height: 34px;
}

button[disabled], html input[disabled] {
  cursor: not-allowed;
  background-color: #e6e6e6;
}
button[disabled]:hover, html input[disabled]:hover {
  background-color: #dadada;
}

.btn-narrow-icon {
  border: 1px solid #5db043;
  border-radius: 3px;
  display: inline-block !important;
  height: 34px;
  width: 34px;
  line-height: 34px !important;
  margin-left: 5px;
  padding: 0 10px;
  text-align: center;
  background-color: #fff;
  outline: none !important;
}
.btn-narrow-icon:hover {
  color: #5db043;
  border: 1px solid #5db043;
}
.btn-narrow-icon[class^=z-], .btn-narrow-icon[class*=" z-"] {
  line-height: 25px;
}

.GetBannerList .banner-btn-group .btn-text-group .dropdown-menu {
  min-width: 116px;
}

@font-face {
  font-family: "znode-icons";
  src: url("../../fonts/znode-icons.eot?wvnuw4");
  src: url("../../fonts/znode-icons.eot?wvnuw4#iefix") format("embedded-opentype"), url("../../fonts/znode-icons.ttf?wvnuw4") format("truetype"), url("../../fonts/znode-icons.woff?wvnuw4") format("woff"), url("../../fonts/znode-icons.svg?wvnuw4#znode-icons") format("svg");
  font-weight: normal;
  font-style: normal;
}
[class^=z-], [class*=" z-"] {
  font-family: "znode-icons" !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.z-hamburger:before {
  font-family: "znode-icons";
  content: "\e900";
}

.z-backward:before {
  font-family: "znode-icons";
  content: "\f04a";
}

.z-forward:before {
  font-family: "znode-icons";
  content: "\f04e";
}

.z-delete:before, .z-Delete:before {
  font-family: "znode-icons";
  content: "\e901";
}

.z-detail-view:before {
  font-family: "znode-icons";
  content: "\e902";
}

.z-edit:before, .z-Edit:before {
  font-family: "znode-icons";
  content: "\e903";
}

.z-export:before {
  font-family: "znode-icons";
  content: "\e904";
}

.z-folder-close:before {
  font-family: "znode-icons";
  content: "\e905";
}

.z-folder-open:before {
  font-family: "znode-icons";
  content: "\e906";
}

.z-info:before {
  font-family: "znode-icons";
  content: "\e907";
}

.z-left-navigation:before {
  font-family: "znode-icons";
  content: "\e908";
}

.z-list-view:before {
  font-family: "znode-icons";
  content: "\e909";
}

.z-menu-hamburger:before {
  font-family: "znode-icons";
  content: "\e90a";
}

.z-refresh:before {
  font-family: "znode-icons";
  content: "\e90b";
}

.z-search:before {
  font-family: "znode-icons";
  content: "\e90c";
}

.z-setting:before, .z-manage:before, .z-Setup:before {
  font-family: "znode-icons";
  content: "\e90d";
}

.z-tile-view:before {
  font-family: "znode-icons";
  content: "\e90e";
}

.z-user:before {
  font-family: "znode-icons";
  content: "\e90f";
}

.z-first:before {
  font-family: "znode-icons";
  content: "\e912";
}

.z-last:before {
  font-family: "znode-icons";
  content: "\e913";
}

.z-left-all:before {
  font-family: "znode-icons";
  content: "\f100";
}

.z-right-all:before {
  font-family: "znode-icons";
  content: "\f101";
}

.z-up-all:before {
  font-family: "znode-icons";
  content: "\f102";
}

.z-down-all:before {
  font-family: "znode-icons";
  content: "\f103";
}

.z-left:before {
  font-family: "znode-icons";
  content: "\f104";
}

.z-right:before {
  font-family: "znode-icons";
  content: "\f105";
}

.z-up:before {
  font-family: "znode-icons";
  content: "\e914";
}

.z-down:before {
  font-family: "znode-icons";
  content: "\e911";
}

.z-close-circle:before {
  font-family: "znode-icons";
  content: "\f057";
}

.z-add:before {
  font-family: "znode-icons";
  content: "\f067";
}

.z-close:before {
  font-family: "znode-icons";
  content: "\f00d";
}

.z-back:before {
  font-family: "znode-icons";
  content: "\f060";
}

.z-calendar:before {
  font-family: "znode-icons";
  content: "\f073";
}

.z-minus:before {
  font-family: "znode-icons";
  content: "\f068";
}

.z-view:before, .z-View:before {
  font-family: "znode-icons";
  content: "\e910";
}

.z-ok:before {
  font-family: "znode-icons";
  content: "\f00e";
}

.z-disable:before {
  font-family: "znode-icons";
  content: "\e915";
}

.z-enable:before {
  font-family: "znode-icons";
  content: "\e916";
}

.z-sortable:before {
  font-family: "znode-icons";
  content: "\f0dc";
}

.z-admin:before, .z-Admin:before {
  font-family: "znode-icons";
  content: "\e917";
}

.z-cms:before, .z-CMS:before {
  font-family: "znode-icons";
  content: "\e918";
}

.z-media-manager:before, .z-Media:before {
  font-family: "znode-icons";
  content: "\e919";
}

.z-oms:before, .z-OMS:before {
  font-family: "znode-icons";
  content: "\e91a";
}

.z-pim:before, .z-PIM:before {
  font-family: "znode-icons";
  content: "\e922";
}

.z-customers:before {
  font-family: "znode-icons";
  content: "\e91b";
}

.z-dashboard:before, .z-Dashboard:before {
  font-family: "znode-icons";
  content: "\e91c";
}

.z-promotinons-and-coupons:before {
  font-family: "znode-icons";
  content: "\e91d";
}

.z-provider-engine:before {
  font-family: "znode-icons";
  content: "\e91e";
}

.z-roles:before {
  font-family: "znode-icons";
  content: "\e91f";
}

.z-supplier:before {
  font-family: "znode-icons";
  content: "\e920";
}

.z-taxes:before {
  font-family: "znode-icons";
  content: "\e921";
}

.z-attribute:before {
  font-family: "znode-icons";
  content: "\e923";
}

.z-attribute-family:before {
  font-family: "znode-icons";
  content: "\e924";
}

.z-attribute-group:before {
  font-family: "znode-icons";
  content: "\e925";
}

.z-clear-cache:before {
  font-family: "znode-icons";
  content: "\e926";
}

.z-upload:before {
  font-family: "znode-icons";
  content: "\f093";
}

.z-copy:before {
  font-family: "znode-icons";
  content: "\e927";
}

.z-preview:before {
  font-family: "znode-icons";
  content: "\e928";
}

.z-audio:before {
  font-family: "znode-icons";
  content: "\e929";
}

.z-video:before {
  font-family: "znode-icons";
  content: "\e92a";
}

.z-file-text:before {
  font-family: "znode-icons";
  content: "\f0f6";
}

.z-reports:before, .z-Reports:before {
  font-family: "znode-icons";
  content: "\e92c";
}

.z-download:before {
  font-family: "znode-icons";
  content: "\f019";
}

.z-angle-up:before {
  font-family: "znode-icons";
  content: "\f106";
}

.z-angle-down:before {
  font-family: "znode-icons";
  content: "\f107";
}

.z-check-circle:before {
  font-family: "znode-icons";
  content: "\f058";
}

.z-question-circle:before {
  font-family: "znode-icons";
  content: "\e92b";
}

.z-inventory:before {
  font-family: "znode-icons";
  content: "\e930";
}

.z-payment:before {
  font-family: "znode-icons";
  content: "\e931";
}

.z-price:before {
  font-family: "znode-icons";
  content: "\e932";
}

.z-profiles:before {
  font-family: "znode-icons";
  content: "\e933";
}

.z-shipping:before {
  font-family: "znode-icons";
  content: "\e934";
}

.z-stores:before {
  font-family: "znode-icons";
  content: "\e935";
}

.z-tax:before {
  font-family: "znode-icons";
  content: "\e936";
}

.z-warehouse:before {
  font-family: "znode-icons";
  content: "\e937";
}

.z-account:before {
  font-family: "znode-icons";
  content: "\e938";
}

.z-attribute-configuration:before {
  font-family: "znode-icons";
  content: "\e939";
}

.z-media-list:before {
  font-family: "znode-icons";
  content: "\e93a";
}

.z-contents:before {
  font-family: "znode-icons";
  content: "\e93b";
}

.z-customer-configuration:before {
  font-family: "znode-icons";
  content: "\e93c";
}

.z-customer-review:before {
  font-family: "znode-icons";
  content: "\e93d";
}

.z-email-templates:before {
  font-family: "znode-icons";
  content: "\e93e";
}

.z-slider-configuration:before {
  font-family: "znode-icons";
  content: "\e93f";
}

.z-theme-configuration:before {
  font-family: "znode-icons";
  content: "\e940";
}

.z-website-configuration:before {
  font-family: "znode-icons";
  content: "\e941";
}

.z-menu-list:before {
  font-family: "znode-icons";
  content: "\e92d";
}

.z-role-access-right:before {
  font-family: "znode-icons";
  content: "\e92e";
}

.z-store-admin:before {
  font-family: "znode-icons";
  content: "\e92f";
}

.z-tree-view:before {
  font-family: "znode-icons";
  content: "\e942";
}

.z-catalog:before {
  font-family: "znode-icons";
  content: "\e943";
}

.z-categories:before {
  font-family: "znode-icons";
  content: "\e944";
}

.z-products:before {
  font-family: "znode-icons";
  content: "\e945";
}

.z-global-setting:before {
  font-family: "znode-icons";
  content: "\e999";
}

.z-location:before {
  font-family: "znode-icons";
  content: "\e99a";
}

.z-countries:before {
  font-family: "znode-icons";
  content: "\e946";
}

.z-display:before {
  font-family: "znode-icons";
  content: "\e947";
}

.z-price-management:before {
  font-family: "znode-icons";
  content: "\e949";
}

.z-product-info:before, .z-ProductInfo:before {
  font-family: "znode-icons";
  content: "\e94a";
}

.z-smtp:before {
  font-family: "znode-icons";
  content: "\e94b";
}

.z-store-locator:before {
  font-family: "znode-icons";
  content: "\e94c";
}

.z-units:before {
  font-family: "znode-icons";
  content: "\e94d";
}

.z-url:before {
  font-family: "znode-icons";
  content: "\e94e";
}

.z-add-on-group:before, .z-Add-Ons:before {
  font-family: "znode-icons";
  content: "\e94f";
}

.z-bundle-product:before {
  font-family: "znode-icons";
  content: "\e950";
}

.z-custom-fields:before, .z-CustomFields:before {
  font-family: "znode-icons";
  content: "\e951";
}

.z-gallery-images:before, .z-GalleryImages:before {
  font-family: "znode-icons";
  content: "\e952";
}

.z-grouped-product:before {
  font-family: "znode-icons";
  content: "\e953";
}

.z-image:before, .z-Image:before {
  font-family: "znode-icons";
  content: "\e954";
}

.z-personalization:before, .z-Personalization:before {
  font-family: "znode-icons";
  content: "\e955";
}

.z-simple-product:before {
  font-family: "znode-icons";
  content: "\e957";
}

.z-manage-message:before {
  font-family: "znode-icons";
  content: "\f003";
}

.z-publish:before {
  font-family: "znode-icons";
  content: "\e959";
}

.z-seo:before {
  font-family: "znode-icons";
  content: "\e95a";
}

.z-template:before {
  font-family: "znode-icons";
  content: "\e956";
}

.z-url-redirects:before {
  font-family: "znode-icons";
  content: "\e958";
}

.z-address:before {
  font-family: "znode-icons";
  content: "\e95b";
}

.z-associated-accounts:before {
  font-family: "znode-icons";
  content: "\e95c";
}

.z-associated-customers:before {
  font-family: "znode-icons";
  content: "\e95d";
}

.z-associated-inventory:before {
  font-family: "znode-icons";
  content: "\e95e";
}

.z-associated-profiles:before {
  font-family: "znode-icons";
  content: "\e95f";
}

.z-associated-sku:before {
  font-family: "znode-icons";
  content: "\e960";
}

.z-associated-stores:before {
  font-family: "znode-icons";
  content: "\e961";
}

.z-associated-warehouse:before {
  font-family: "znode-icons";
  content: "\e962";
}

.z-associated-facet:before {
  font-family: "znode-icons";
  content: "\e9b6";
}

.z-coupon-information:before {
  font-family: "znode-icons";
  content: "\e963";
}

.z-departments:before {
  font-family: "znode-icons";
  content: "\e964";
}

.z-discount-information:before {
  font-family: "znode-icons";
  content: "\e965";
}

.z-erp-configurator:before {
  font-family: "znode-icons";
  content: "\e966";
}

.z-notes:before {
  font-family: "znode-icons";
  content: "\e967";
}

.z-orders:before {
  font-family: "znode-icons";
  content: "\e968";
}

.z-permission:before {
  font-family: "znode-icons";
  content: "\e969";
}

.z-quotes:before {
  font-family: "znode-icons";
  content: "\e96a";
}

.z-suppliers:before {
  font-family: "znode-icons";
  content: "\e96b";
}

.z-widgets-default:before {
  font-family: "znode-icons";
  content: "\e96c";
}

.z-affiliate:before {
  font-family: "znode-icons";
  content: "\e96d";
}

.z-associated-items:before {
  font-family: "znode-icons";
  content: "\e96e";
}

.z-associate-products:before, .z-AssociatedProducts:before {
  font-family: "znode-icons";
  content: "\e96f";
}

.z-association-category:before {
  font-family: "znode-icons";
  content: "\e970";
}

.z-cshtml-file:before {
  font-family: "znode-icons";
  content: "\e971";
}

.z-currencies:before {
  font-family: "znode-icons";
  content: "\e972";
}

.z-general-info:before {
  font-family: "znode-icons";
  content: "\e973";
}

.z-promotion-information:before {
  font-family: "znode-icons";
  content: "\e974";
}

.z-scheduler-list:before {
  font-family: "znode-icons";
  content: "\e975";
}

.z-touch-points-configuration:before {
  font-family: "znode-icons";
  content: "\e976";
}

.z-website-logo:before {
  font-family: "znode-icons";
  content: "\e977";
}

.z-product-page:before {
  font-family: "znode-icons";
  content: "\e978";
}

.z-time-picker:before {
  font-family: "znode-icons";
  content: "\e979";
}

.z-trigger:before {
  font-family: "znode-icons";
  content: "\e97a";
}

.z-widget-cart:before {
  font-family: "znode-icons";
  content: "\e97b";
}

.z-you-may-also-like:before {
  font-family: "znode-icons";
  content: "\e97a";
}

.z-service-request:before {
  font-family: "znode-icons";
  content: "\e97d";
}

.z-frequently-bought:before {
  font-family: "znode-icons";
  content: "\e97e";
}

.z-backward-arrow:before {
  font-family: "znode-icons";
  content: "\f04b";
}

.z-forward-arrow:before {
  font-family: "znode-icons";
  content: "\f04f";
}

.z-category-boost-setting:before {
  font-family: "znode-icons";
  content: "\e97f";
}

.z-create-index:before {
  font-family: "znode-icons";
  content: "\e980";
}

.z-fedex:before {
  font-family: "znode-icons";
  content: "\e981";
}

.z-field-boost-setting:before {
  font-family: "znode-icons";
  content: "\e982";
}

.z-gift-cards:before {
  font-family: "znode-icons";
  content: "\e983";
}

.z-issue-gift-card:before {
  font-family: "znode-icons";
  content: "\e984";
}

.z-product-boost-setting:before {
  font-family: "znode-icons";
  content: "\e985";
}

.z-reason-for-return:before {
  font-family: "znode-icons";
  content: "\e988";
}

.z-reply-to-customer:before {
  font-family: "znode-icons";
  content: "\e987";
}

.z-request-status:before {
  font-family: "znode-icons";
  content: "\e988";
}

.z-review-order:before {
  font-family: "znode-icons";
  content: "\e989";
}

.z-rma-configuration:before {
  font-family: "znode-icons";
  content: "\e98a";
}

.z-search-settings:before {
  font-family: "znode-icons";
  content: "\e98b";
}

.z-shipping-methods:before {
  font-family: "znode-icons";
  content: "\e98c";
}

.z-shopping-cart:before {
  font-family: "znode-icons";
  content: "\e98d";
}

.z-ups:before {
  font-family: "znode-icons";
  content: "\e98e";
}

.z-add-circle:before, .z-addattributegroups:before, .z-addattributes:before {
  font-family: "znode-icons";
  content: "\e98f";
}

.z-alert:before {
  font-family: "znode-icons";
  content: "\e990";
}

.z-cancel:before {
  font-family: "znode-icons";
  content: "\e991";
}

.z-columns:before, .z-managecolumns:before {
  font-family: "znode-icons";
  content: "\e992";
}

.z-help:before {
  font-family: "znode-icons";
  content: "\e993";
}

.z-help-circle:before {
  font-family: "znode-icons";
  content: "\e92b";
}

.z-manage-filter:before {
  font-family: "znode-icons";
  content: "\e995";
}

.z-nav-menu:before {
  font-family: "znode-icons";
  content: "\e996";
}

.z-save:before {
  font-family: "znode-icons";
  content: "\e997";
}

.z-locale:before {
  font-family: "znode-icons";
  content: "\e998";
}

.z-brand:before {
  font-family: "znode-icons";
  content: "\e948";
}

.z-capture-payment:before {
  font-family: "znode-icons";
  content: "\e99b";
}

.z-history:before {
  font-family: "znode-icons";
  content: "\e99c";
}

.z-order-quotes:before {
  font-family: "znode-icons";
  content: "\e99e";
}

.z-page-view:before {
  font-family: "znode-icons";
  content: "\e99f";
}

.z-resend-email:before {
  font-family: "znode-icons";
  content: "\e9a0";
}

.z-vendors:before {
  font-family: "znode-icons";
  content: "\e9a1";
}

.z-void-payment:before {
  font-family: "znode-icons";
  content: "\e9a2";
}

.z-active:before {
  font-family: "znode-icons";
  content: "\e99d";
}

.z-inactive:before {
  font-family: "znode-icons";
  content: "\e9a3";
}

.z-left-collaps-arrow:before {
  font-family: "znode-icons";
  content: "\e9a4";
}

.z-right-collaps-arrow:before {
  font-family: "znode-icons";
  content: "\e9a5";
}

.z-append:before {
  font-family: "znode-icons";
  content: "\e9a6";
}

.z-customer-trust:before {
  font-family: "znode-icons";
  content: "\e9a7";
}

.z-import:before {
  font-family: "znode-icons";
  content: "\e9a8";
}

.z-intelligent-integration:before {
  font-family: "znode-icons";
  content: "\e9a9";
}

.z-print:before {
  font-family: "znode-icons";
  content: "\e9aa";
}

.z-smart-automation:before {
  font-family: "znode-icons";
  content: "\e9ab";
}

.z-void:before {
  font-family: "znode-icons";
  content: "\e9ac";
}

.z-marketing:before {
  font-family: "znode-icons";
  content: "\e9ad";
}

.z-refund-order:before {
  font-family: "znode-icons";
  content: "\e9ae";
}

.z-default-view:before {
  font-family: "znode-icons";
  content: "\f06e";
}

.z-rma-manager:before {
  font-family: "znode-icons";
  content: "\e9af";
}

.z-product-feed:before {
  font-family: "znode-icons";
  content: "\e9b0";
}

.z-continue:before {
  font-family: "znode-icons";
  content: "\e9b1";
}

.z-display-settings:before, .z-displaysettings:before {
  font-family: "znode-icons";
  content: "\e9b2";
}

.z-import-export:before {
  font-family: "znode-icons";
  content: "\e9b3";
}

.z-plus-circle:before {
  font-family: "znode-icons";
  content: "\f055";
}

.z-minus-circle:before {
  font-family: "znode-icons";
  content: "\f056";
}

.z-save-and-close:before {
  font-family: "znode-icons";
  content: "\e9b4";
}

.z-swatch-box:before {
  font-family: "znode-icons";
  content: "\ea1e";
}

.z-blog-and-news:before {
  font-family: "znode-icons";
  content: "\e9b5";
}

.z-entity-attributes:before {
  font-family: "znode-icons";
  content: "\e9b7";
}

.z-global-attributes:before {
  font-family: "znode-icons";
  content: "\e9b8";
}

.z-manage-forms:before {
  font-family: "znode-icons";
  content: "\e9b9";
}

.z-form-submission:before {
  font-family: "znode-icons";
  content: "\e9ba";
}

.z-log-message:before {
  font-family: "znode-icons";
  content: "\e9bb";
}

.z-pin:before {
  font-family: "znode-icons";
  content: "\e9bc";
}

.z-lock:before {
  font-family: "znode-icons";
  content: "\e9bd";
}

.z-unlock:before {
  font-family: "znode-icons";
  content: "\e9be";
}

.z-application-logs:before {
  font-family: "znode-icons";
  content: "\e9bf";
}

.z-pause:before {
  font-family: "znode-icons";
  content: "\e9c0";
}

.z-resume:before {
  font-family: "znode-icons";
  content: "\e9c1";
}

.z-account_circle:before {
  font-family: "znode-icons";
  content: "\e9c2";
}

.z-new-refresh:before {
  font-family: "znode-icons";
  content: "\e9c3";
}

.z-cancel-circle:before {
  font-family: "znode-icons";
  content: "\e9c4";
}

.z-cloud-upload:before {
  font-family: "znode-icons";
  content: "\e9c5";
}

.z-pending-payment:before {
  font-family: "znode-icons";
  content: "\e9c6";
}

.z-accept:before {
  font-family: "znode-icons";
  content: "\e9c7";
}

.z-decline:before {
  font-family: "znode-icons";
  content: "\e9c8";
}

.z-pending-orders:before {
  font-family: "znode-icons";
  content: "\e9c9";
}

.z-advanced-report:before {
  font-family: "znode-icons";
  content: "\e9cb";
}

.is-star:before {
  font-family: "znode-icons";
  content: "\e9cc";
}

.z-diagnostics-status:before {
  font-family: "znode-icons";
  content: "\e9cd";
}

.z-analytics:before {
  font-family: "znode-icons";
  content: "\e9ce";
}

.z-power-bi:before {
  font-family: "znode-icons";
  content: "\e9cf";
}

.z-quote:before {
  font-family: "znode-icons";
  content: "\e9d2";
}

.z-return:before {
  font-family: "znode-icons";
  content: "\e9d1";
}

.z-quote:before {
  font-family: "znode-icons";
  content: "\e9d2";
}

.z-content-widgets:before {
  font-family: "znode-icons";
  content: "\e9d3";
}

.z-widget-templates:before {
  font-family: "znode-icons";
  content: "\e9d4";
}

.z-external-link:before {
  font-family: "znode-icons";
  content: "\e9d5";
}

.z-container-widget:before {
  font-family: "znode-icons";
  content: "\e9e0";
}

.z-banner-widget:before {
  font-family: "znode-icons";
  content: "\e9df";
}

.z-categorylist-widget:before {
  font-family: "znode-icons";
  content: "\e9de";
}

.z-form-widget:before {
  font-family: "znode-icons";
  content: "\e9dd";
}

.z-image-widget:before {
  font-family: "znode-icons";
  content: "\e9dc";
}

.z-link-widget:before {
  font-family: "znode-icons";
  content: "\e9db";
}

.z-productlist-widget:before {
  font-family: "znode-icons";
  content: "\e9da";
}

.z-richtext-widget:before {
  font-family: "znode-icons";
  content: "\e9d9";
}

.z-search-widget:before {
  font-family: "znode-icons";
  content: "\e9d8";
}

.z-text-widget:before {
  font-family: "znode-icons";
  content: "\e9d7";
}

.z-video-widget:before {
  font-family: "znode-icons";
  content: "\e9d6";
}

.left-panel-side {
  position: fixed;
  height: 100%;
  color: #fff;
  top: 113px;
  left: 0;
  background: rgba(242, 245, 245, 0.8);
  z-index: 101;
  overflow-y: auto;
  -moz-transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
  -o-transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
  -webkit-transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
  transition: left 0.5s ease, width 0.6s cubic-bezier(0.525, -0.35, 0.115, 1.335);
  padding-top: 15px;
  box-shadow: inset -10px 0px 8px -8px #cccccc;
  width: inherit;
}
.left-panel-side li a {
  padding: 10px 10px 10px 20px;
  color: #3c4145;
  display: block;
  text-transform: uppercase;
  font-size: 14px;
  font-family: "RobotoCondensed-Regular", segoeui-bold, HelveticaNeueLTStd Lt, Arial, sans-serif;
}
.left-panel-side li a:hover, .left-panel-side li a:focus, .left-panel-side li a:active {
  background-color: #FFFFFF;
  color: #3c4145;
  -moz-transition: all 0.2s linear 0s;
  -webkit-transition: all 0.2s linear 0s;
  transition: all 0.2s linear 0s;
  box-shadow: 0 4px 8px -2px #cccccc;
  border: none;
  border-radius: 0;
}
.left-panel-side li .active {
  color: #3c4145;
  background-color: #FFFFFF;
  -moz-transition: all 0.2s linear 0s;
  -webkit-transition: all 0.2s linear 0s;
  transition: all 0.2s linear 0s;
  box-shadow: 0 4px 8px -2px #cccccc;
  border: none;
  border-radius: 0;
}
.left-panel-side .left-panel-side-ul {
  padding-left: 15px !important;
}
.left-panel-side .nav-tabs .nav-link {
  margin-bottom: 0;
  border: none;
}
